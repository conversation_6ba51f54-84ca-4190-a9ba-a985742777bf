// Grid System
// --------------------------------------------------

// Framework grid generation
//
// Used only by <PERSON><PERSON><PERSON> to generate the correct number of grid classes given
// any value of `@grid-columns`.

.make-grid-columns() {
	// Common styles for all sizes of grid columns, widths 1-12
	.col(@index) when (@index = 1) { // initial
		@item: ~".x-col-@{index}-xs, .x-col-@{index}-sm, .x-col-@{index}-md, .x-col-@{index}-lg, .x-col-@{index}-xl";
		.col(@index + 1, @item);
	}
	.col(@index, @list) when (@index =< @grid-columns) { // general; "=<" isn't a typo
		@item: ~".x-col-@{index}-xs, .x-col-@{index}-sm, .x-col-@{index}-md, .x-col-@{index}-lg, .x-col-@{index}-xl";
		.col(@index + 1, ~"@{list}, @{item}");
	}
	.col(@index, @list) when (@index > @grid-columns) { // terminal
		@{list} {
			display: inline-block;
			position: relative;
			// Prevent columns from collapsing when empty
			min-height: 1px;
			// Inner gutter via padding
			padding-left:  (@grid-gutter-width / 2);
			padding-right: (@grid-gutter-width / 2);
		}
	}
	.col(1); // kickstart it
}

.make-grid-columns-float(@class) {
	.col(@index) when (@index = 1) { // initial
		@item: ~".x-col-@{index}@{class}";
		.col(@index + 1, @item);
	}
	.col(@index, @list) when (@index < @grid-columns) { // general
		@item: ~".x-col-@{index}@{class}";
		.col(@index + 1, ~"@{list}, @{item}");
	}
	.col(@index, @list) when (@index = @grid-columns) { // terminal
		@{list} {
			float: left;
		}
	}
	.col(1); // kickstart it
}

.calc-grid(@index, @class, @type) when (@type = width) and (@index > 0) {
	.x-col-@{index}@{class} {
		width: percentage((@index / @grid-columns));
	}
}
.calc-grid(@index, @class, @type) when (@type = push) {
	.x-col-push-@{index}@{class} {
		left: percentage((@index / @grid-columns));
	}
}
.calc-grid(@index, @class, @type) when (@type = pull) {
	.x-col-pull-@{index}@{class} {
		right: percentage((@index / @grid-columns));
	}
}
.calc-grid(@index, @class, @type) when (@type = offset) {
	.x-col-offset-@{index}@{class} {
		margin-left: percentage((@index / @grid-columns));
	}
}

// Basic looping in LESS
.make-grid(@index, @class, @type) when (@index >= 0) {
	.calc-grid(@index, @class, @type);
	// next iteration
	.make-grid(@index - 1, @class, @type);
}

