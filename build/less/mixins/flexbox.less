// Flexbox model
// --------------------------------------------------

// HORIZONTAL BOX layout
.flex-hbox(@justify: center; @align: center) {
	display: block;
	@media (min-width: (@flex-breakpoint+0.1)) {
		.flexbox & {
			display: block;
			.flex-container(row, @justify, @align);
			> * {
				display: block;
				flex: 0 1 auto;
				min-width: 0; // FF 34+ @see http://stackoverflow.com/questions/26895349/how-can-i-get-ff-33-x-flexbox-behavior-in-ff-34-x
			}
		}
		.no-flexbox &, .browser-safari & {
			display: table;
			table-layout: fixed; // fix IE9, iOS
			width: 100%;
			> * {
				display: table-cell;
			}
		}
	}
}

// VERTICAL BOX layout
.flex-vbox(@justify: center; @align: center) {
	display: block;
	@media (min-width: (@flex-breakpoint+0.1)) {
		.flexbox & {
			.flex-container(column, @justify, @align);
			> * {
				flex: 0 1 auto;
				min-height: 0; // FF 34+ @see http://stackoverflow.com/questions/26895349/how-can-i-get-ff-33-x-flexbox-behavior-in-ff-34-x
			}
		}
		.no-flexbox &, .browser-safari & {
			display: table;
		}
	}
}

.flex-container(@direction: row; @justify: center; @align: center) {
	display: -webkit-box; // OLD: Safari,  iOS, Android browser, older WebKit browsers.
	display: -moz-box; // OLD: Firefox (buggy)
	display: -ms-flexbox; // MID: IE10
	display: -webkit-flex; // NEW: Chrome 21–28, Safari 6.1+
	display: flex; // NEW: IE11, Chrome 29+ Firefox 22+, Opera 12
	.flex-container-direction(@direction);
	.flex-container-justify(@justify);
	.flex-container-align(@align);
}

.flex-element(@flex: 0 1 auto; @order: 0; @align: auto) {
	.flex-element-flex(@flex);
	.flex-element-order(@order);
	.flex-element-align(@align);
}

// Direction: specifying the direction of the main flexbox axis
.flex-container-direction(@direction) when (@direction = row) {
	-webkit-box-orient: horizontal; -moz-box-orient: horizontal; -ms-box-orient: horizontal; box-direction: normal; flex-direction: row;
}
.flex-container-direction(@direction) when (@direction = row-reverse) {
	-webkit-box-orient: horizontal;	-moz-box-orient: horizontal; -ms-box-orient: horizontal; box-direction: reverse; flex-direction: row-reverse;
}
.flex-container-direction(@direction) when (@direction = column) {
	-webkit-box-orient: vertical; -moz-box-orient: vertical; -ms-box-orient: vertical; box-direction: normal; flex-direction: column;
}
.flex-container-direction(@direction) when (@direction = column-reverse) {
	-webkit-box-orient: vertical; -moz-box-orient: vertical; -ms-box-orient: vertical; box-direction: reverse; flex-direction: column-reverse;
}

// Axis alignment: specifying alignment of items along the main flexbox axis
.flex-container-justify(@align) when (@align = start) {
	-webkit-box-pack: start; -moz-box-pack: start; -ms-flex-pack: start; -webkit-justify-content: flex-start; justify-content: flex-start;
}
.flex-container-justify(@align) when (@align = center) {
	-webkit-box-pack: center; -moz-box-pack: center; -ms-flex-pack: center; -webkit-justify-content: center; justify-content: center;
}
.flex-container-justify(@align) when (@align = end) {
	-webkit-box-pack: end; -moz-box-pack: end; -ms-flex-pack: end; -webkit-justify-content: flex-end; justify-content: flex-end;
}
.flex-container-justify(@align) when (@align = justify) {
	-webkit-box-pack: justify; -moz-box-pack: justify; -ms-flex-pack: justify; -webkit-justify-content: space-between; justify-content: space-between;
}
.flex-container-justify(@align) when (@align = distribute) {
	-webkit-box-pack: null; -moz-box-pack: null; -ms-flex-pack: distribute; -webkit-justify-content: space-around; justify-content: space-around;
}

// Cross-axis alignment: specifying alignment of items along the cross-axis
.flex-container-align(@align) when (@align = start) {
	-webkit-box-align: start; -moz-box-align: start; -ms-flex-align: start; -webkit-align-items: flex-start; align-items: flex-start;
}
.flex-container-align(@align) when (@align = center) {
	-webkit-box-align: center; -moz-box-align: center; -ms-flex-align: center; -webkit-align-items: center; align-items: center;
}
.flex-container-align(@align) when (@align = end) {
	-webkit-box-align: end; -moz-box-align: end; -ms-flex-align: end; -webkit-align-items: flex-end; align-items: flex-end;
}
.flex-container-align(@align) when (@align = baseline) {
	-webkit-box-align: baseline; -moz-box-align: baseline; -ms-flex-align: baseline; -webkit-align-items: baseline; align-items: baseline;
}
.flex-container-align(@align) when (@align = stretch) {
	-webkit-box-align: stretch; -moz-box-align: stretch; -ms-flex-align: stretch; -webkit-align-items: stretch; align-items: stretch;
}

// Flexibility: specifying how the size of items flex
.flex-element-flex(@flex) {
	-webkit-box-flex: @flex;
	-moz-box-flex: @flex;
	-ms-box-flex: @flex;
	box-flex: @flex;
	-webkit-flex: @flex;
	-moz-flex: @flex;
	-ms-flex: @flex;
	flex: @flex;
}
.flex-element-flex-important(@flex) {
	-webkit-box-flex: @flex !important;
	-moz-box-flex: @flex !important;
	-ms-box-flex: @flex !important;
	box-flex: @flex !important;
	-webkit-flex: @flex !important;
	-moz-flex: @flex !important;
	-ms-flex: @flex !important;
	flex: @flex !important;
}

// Individual item order
.flex-element-order(@order) {
	// Display order: specifying the order of flex items
	-webkit-box-ordinal-group: @order; -moz-box-ordinal-group: @order; -ms-box-ordinal-group: @order; box-ordinal-group: @order; -ms-flex-order: @order; order: @order;
}

// Individual cross-axis alignment: override to align individual items along the cross-axis
.flex-element-align(@align) when (@align = auto) {
	-ms-flex-item-align: auto;
	align-self: auto;
}
.flex-element-align(@align) when (@align = start) {
	-ms-flex-item-align: start;
	align-self: flex-start;
}
.flex-element-align(@align) when (@align = center) {
	-ms-flex-item-align: center;
	align-self: center;
}
.flex-element-align(@align) when (@align = end) {
	-ms-flex-item-align: end;
	align-self: flex-end;
}
.flex-element-align(@align) when (@align = baseline) {
	-ms-flex-item-align: baseline;
	align-self: baseline;
}
.flex-element-align(@align) when (@align = stretch) {
	-ms-flex-item-align: stretch;
	align-self: stretch;
}
