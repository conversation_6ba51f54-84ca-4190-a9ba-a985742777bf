(function(ng, module) {

	module.config(['$stateProvider', function($stateProvider) {
		$stateProvider
			.state('account', { url: '/account/', templateUrl: 'account/account.html', controller: 'app.account.Ctrl', navbar: 'dashboard' })
		;
	}]);

	// initialization
	var userID = null;
	var PWD_TOKEN = false;
	module.run(['$rootScope', function($rootScope) {
		$rootScope.$on('xng.auth:init', function(ev, authData) {
			if(authData) userID = authData.UID;
			if(authData && authData.PWD_TOKEN) PWD_TOKEN = authData.PWD_TOKEN;
		});
	}]);



	// EditCtrl
	module.controller('app.account.Ctrl', ['$scope', '$http', 'xng.$data', 'xng.ui.$growl', function($scope, $http, $data, $growl) {
		var Repository = $data.repository('User');

		// $scope initialization
		// ------------------------------------------
		$scope.Pwd = {
			token: PWD_TOKEN
		};
		Repository.fetch(userID, 'account').then(function(User) {
			$scope.User = User;
		});

		// Form functions
		// ------------------------------------------
		$scope.pwdAbort = function() {
			$scope.Pwd = {};
		};
		$scope.xngFormUser = {
			submitFn: function() {
				return Repository.update($scope.User);
			},
			successFn: function() {
				//$state.go('dashboard');
				$growl.success('Dati salvati', { icon: 'icon-checkmark', ttl: 2000 });
			},
			failureFn: function() {
				$growl.error('Dati non validi', { icon: 'icon-warning', ttl: 5000 });
			}
		};
		$scope.xngFormPassword = {
			submitFn: function() {
				$http({ method: 'PUT', url: '/api/auth/password', data: $scope.Pwd })
					.success(function(data) {
						if(data.success) {
							$scope.Pwd.token = PWD_TOKEN = false;
							$growl.success('Password modificata', { icon: 'icon-checkmark', ttl: 5000 });
							$scope.pwdAbort();
						} else $growl.error('Errore imprevisto', { icon: 'icon-warning', ttl: 5000 });
					})
					.error(function(data) {
						switch(data.errCode) {
							case 'WRONG_OLD_PASSWORD': $growl.error('Password attuale non valida', { icon: 'icon-warning', ttl: 5000 }); break;
							case 'PASSWORDS_MISMATCH': $growl.error('Le 2 password inserite NON coincidono', { icon: 'icon-warning', ttl: 5000 }); break;
							case 'PASSWORD_REGEX': $growl.error('La password inserita non rispetta lo standard di sicurezza (6 caratteri minimo)', { icon: 'icon-warning', ttl: 5000 }); break;
							case 'EXCEPTION': $growl.error('Errore imprevisto', { icon: 'icon-warning', ttl: 5000 }); break;
						}
					});
			},
			successFn: function(data) {
			},
			failureFn: function(data) {
			}
		};

	}]);

})(angular, angular.module('app.account', []));
