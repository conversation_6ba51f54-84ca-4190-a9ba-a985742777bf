<section class="x-table-container">
	<table class="x-table x-table-bordered x-table-striped">
		<thead>
		<tr>
			<th>Area</th>
			<th>AreaManager</th>
			<th>DistrictManager</th>
			<th>Status</th>
			<th ng-repeat="m in metadata">{{m.label}}</th>
		</tr>
		</thead>
		<tbody>
		<tr ng-repeat="d in data" ng-class="{ 'table-row-green-light': d.status=='ON' && !d.district && d.area, 'table-row-green': d.status=='ON' && !d.area, 'table-row-orange-light': d.status=='INTER' && d.area && !d.district, 'table-row-orange': d.status=='INTER' && !d.area }">
			<td>{{d.areaName}}</td>
			<td>{{d.areaManager}}</td>
			<td>{{d.districtManager}}</td>
			<td>{{d.status}}</td>
			<td ng-repeat="m in metadata" style="text-align: right">{{d[m.id] | dynFilter:m.filter}}</td>
		</tr>
		</tbody>
	</table>
</section>
