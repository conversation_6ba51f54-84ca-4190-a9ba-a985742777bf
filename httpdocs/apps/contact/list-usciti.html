<br/>
<br/>
<section class="x-panel" xng-grid="grid">
	<header><h2>Elenco clienti usciti</h2></header>
	<section>
		<div class="x-pull-right"><a class="x-btn x-blue" ng-href="/api/apps/contact/usciti.xls"><i class="icon-download"></i> Listato Excel</a></div>
	</section>
	<section class="x-table-container">
		<table class="x-table x-table-bordered x-table-striped x-table-hover" xng-grid="grid">
			<thead>
			<tr>
				<th colspan="4"></th>
				<th colspan="9">Dati anagrafici</th>
				<th colspan="10"><PERSON>ti assicurativi</th>
			</tr>
			<tr>
				<th xng-grid-sort="agenzia_id" class="x-sorting" style="min-width: 5rem">Ag.</th>
				<th xng-grid-sort="produttore">Produttore</th>
				<th xng-grid-sort="dataUscita" class="x-sorting">Uscita</th>
				<th xng-grid-sort="status" class="x-sorting" style="min-width: 14rem;">Status</th>
				<!-- anagrafica -->
				<th xng-grid-sort="nominativo" class="x-sorting">Nominativo</th>
				<th xng-grid-sort="dataNascita" class="x-sorting">Nascita</th>
				<th>Indirizzo</th>
				<th>CAP</th>
				<th>Località</th>
				<th>Comune</th>
				<th>Provincia</th>
				<th>Cod.Fisc.</th>
				<th>Età</th>
				<!-- dati assicurativi -->
				<th xng-grid-sort="settore" class="x-sorting" style="min-width: 7rem;">Settore</th>
				<th xng-grid-sort="polizza" class="x-sorting" style="min-width: 7rem;">Polizza</th>
				<th xng-grid-sort="targa" class="x-sorting">Targa</th>
				<th xng-grid-sort="classeInt" class="x-sorting" style="min-width: 5rem;">CI</th>
				<th xng-grid-sort="classeCU" class="x-sorting" style="min-width: 5rem;">CU</th>
				<th xng-grid-sort="marca" class="x-sorting">Marca</th>
				<th>Modello</th>
				<th>Cavalli</th>
				<th>Cilind.</th>
				<th xng-grid-sort="premioRCA" class="x-sorting">RCA</th>
			</tr>
			</thead>
			<tbody>
			<tr>
				<td colspan="22" style="height: 5em" ng-show="!GridStore.loading && GridStore.data.length==0">Nessun dato presente.</td>
			</tr>
			<tr ng-repeat="item in GridStore.data">
				<td>{{item.agenzia_id}}</td>
				<td>{{item.produttore}}</td>
				<td>{{item.dataUscita | date:'dd/MM/yyyy' }}</td>
				<td ng-if="grid.editing !== $index">
					<a ng-if="UI_TYPE == 'AGENTE'" ng-click="grid.editing = $index" class="x-btn"><i class="icon-pencil"></i></a>
					<a ng-if="UI_TYPE == 'AGENTE'" ng-click="remove(item)" class="x-btn"><i class="icon-remove"></i></a>
					<span ng-if="!item.status" class="x-label">DA CONTATTARE</span>
					<span ng-if="item.status=='CONTATTATO'" class="x-label x-blue">CONTATTATO</span>
					<span ng-if="item.status=='OK'" class="x-label x-green">ACQUISITO</span>
					<span ng-if="item.status=='KO'" class="x-label x-red">RIFIUTO</span>
					<span ng-if="item.status=='ELIMINATO'" class="x-label x-black">ELIMINATO</span>
				</td>
				<td ng-if="grid.editing === $index">
					<select ng-model="item.status" ng-change="grid.editing = null; update(item)" ng-options="status.value as status.key for status in statusArray" style="width: 9rem;"></select>
					<a ng-click="grid.editing = null" class="x-btn x-red"><i class="icon-close"></i></a>
				</td>
				<!-- anagrafica -->
				<td>{{item.nominativo}}</td>
				<td>{{item.dataNascita | date:'dd/MM/yyyy' }}</td>
				<td>{{item.indirizzo}}</td>
				<td>{{item.cap}}</td>
				<td>{{item.localita}}</td>
				<td>{{item.comune}}</td>
				<td>{{item.provincia}}</td>
				<td>{{item.codiceFisc}}</td>
				<td>{{item.eta}}</td>
				<!-- dati assicurativi -->
				<td>{{item.settore}}</td>
				<td>{{item.polizza}}</td>
				<td>{{item.targa}}</td>
				<td style="text-align: right">{{item.classeInt}}</td>
				<td style="text-align: right">{{item.classeCU}}</td>
				<td>{{item.marca}}</td>
				<td>{{item.modello}}</td>
				<td style="text-align: right">{{item.cavalli}}</td>
				<td style="text-align: right">{{item.cilindrata}}</td>
				<td style="text-align: right">{{item.premioRCA | currency}}</td>
			</tr>
			</tbody>
			<tfoot>
			<tr>
				<td colspan="23">
					{{GridStore.status.currentFirst}}-{{GridStore.status.currentLast}} di {{GridStore.status.total}}
					&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<div class="x-btn-group">
						<a class="x-btn" ng-click="GridStore.firstPage()"><i class="icon-first"></i></a>
						<a class="x-btn" ng-click="GridStore.prevPage()"><i class="icon-previous"></i></a>
						<a class="x-btn x-disabled">{{ GridStore.status.currentPage }} / {{GridStore.status.totalPages}}</a>
						<a class="x-btn" ng-click="GridStore.nextPage()"><i class="icon-next"></i></a>
						<a class="x-btn" ng-click="GridStore.lastPage()"><i class="icon-last"></i></a>
					</div>
					&nbsp;
					<a class="x-btn" ng-click="GridStore.reload()"><i class="icon-loop"></i></a>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Paginazione <select ng-model="GridStore.pageSize" style="width: 5em"><option value="10">10</option><option value="25">25</option><option value="50">50</option></select>
				</td>
			</tr>
			</tfoot>
		</table>
	</section>
</section>
