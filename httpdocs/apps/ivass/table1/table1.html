<div class="x-grid" style="padding-top: 30px">
	<div class="x-col-12-sm x-col-2-md">
		<ul class="x-nav-pills index">
			<li><a href ng-click="scroll('a')">Sezione A</a></li>
			<li><a href ng-click="scroll('c')">Sezione C</a></li>
			<li><a href ng-click="scroll('d-banche')">Sezione D</a></li>
			<li><a href ng-click="scroll('e')">Sezione E</a></li>
			<li><a href ng-click="scroll('norui')">Sezione No RUI</a></li>
		</ul>
	</div>

	<div class="x-col-12-sm x-col-10-md">

		<!-- A_AGENTI_MONO/PLURI -->
		<section id="a" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione A del RUI</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Agenti Monomandatari</th>
					<th>Agenti Plurimandatari</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['A']['AGENTI']['MONO'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['A']['AGENTI']['PLURI'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSection1()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /A_AGENTI_MONO/PLURI -->

		<!-- C_DIRETTI -->
		<section id="c" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione C Prod. Diretti</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Prod. Diretti</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['C']['DIRETTI']['DIRETTI'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveRecord(rows[year]['C']['DIRETTI']['DIRETTI'])" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /C_DIRETTI -->

		<!-- D / BANCHE -->
		<section id="d-banche" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione D / Banche</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Prodotti Standardizzati (art. 41 co.1)</th>
					<th>Prodotti non standardizzati  collocati da agenti all'interno dei locali (art. 41 co. 3)</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['D']['BANCHE']['STANDARD'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['BANCHE']['NONSTANDARD'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSection3()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /D BANCHE -->

		<!-- D / INTERMEDIARI -->
		<section id="d-int" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione D / Intermediari</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Prodotti Standardizzati (art. 41 co.1)</th>
					<th>Prodotti non standardizzati  collocati da agenti all'interno dei locali (art. 41 co. 3)</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['STANDARD'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['INTERMEDIARI']['NONSTANDARD'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSection4()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /D INTERMEDIARI -->

		<!-- D / SIM -->
		<section id="d-sim" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione D / SIM</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Prodotti Standardizzati (art. 41 co.1)</th>
					<th>Prodotti non standardizzati  collocati da agenti all'interno dei locali (art. 41 co. 3)</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['D']['SIM']['STANDARD'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['SIM']['NONSTANDARD'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSection5()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /D SIM -->

		<!-- D / POSTE -->
		<section id="d-poste" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione D / Poste Italiane</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Prodotti Standardizzati (art. 41 co.1)</th>
					<th>Prodotti non standardizzati  collocati da agenti all'interno dei locali (art. 41 co. 3)</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['D']['POSTE']['STANDARD'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['D']['POSTE']['NONSTANDARD'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSection6()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /D POSTE -->

		<!-- E -->
		<section id="e" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Sezione E</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>A</th>
					<th>D Banche</th>
					<th>D Inter. fin.</th>
					<th>D Sim</th>
					<th>D Poste It.</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['A'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DBANCHE'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DINTER'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DSIM'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['E']['ADDETTIRUI']['DPOSTE'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSectionE()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /E -->

		<!-- NORUI -->
		<section id="norui" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Non iscritti al RUI</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>A</th>
					<th>D Banche</th>
					<th>D Inter. fin.</th>
					<th>D Sim</th>
					<th>D Poste It.</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['A'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DBANCHE'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DINTER'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DSIM'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTIRUI']['DPOSTE'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSectionNoRui()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /NORUI -->

		<!-- NORUI_DIP -->
		<section id="norui-dip" class="x-panel">
			<header>
				<h2>Composizione della rete di vendita / Non iscritti al RUI</h2>
			</header>
			<table class="x-table x-table-responsive">
				<thead>
				<tr>
					<th width="25%"></th>
					<th></th>
					<th>Dipendenti dell'impresa</th>
					<th>Operatori di call center addetti alla vendita</th>
				</tr>
				</thead>

				<tbody>
				<tr>
					<td colspan="2">
						Numero totale al 31/12 dell'anno
					</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].totale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].totale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td rowspan="5">
						Di cui nel comparto vita
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].vitaTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].vitaTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo I, II e IV</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].vita124" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].vita124" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo III e V</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].vita35" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].vita35" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo VI</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].vita6" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].vita6" type="text" class="form-control"></td>
				</tr>

				<tr>
					<td rowspan="4">
						Di cui nel comparto danni
					</td>
				</tr>
				<tr>
					<td>totale</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].danniTotale" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].danniTotale" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>ramo 10 r.c. auto</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].danniRcAuto" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].danniRcAuto" type="text" class="form-control"></td>
				</tr>
				<tr>
					<td>altri rami</td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['DIPENDENTI'].danniAltro" type="text" class="form-control"></td>
					<td><input ng-model="rows[year]['NO']['ADDETTINORUI']['OPERATORI'].danniAltro" type="text" class="form-control"></td>
				</tr>
				</tbody>
			</table>

			<section class="x-form-actions">
				<button class="x-btn" ng-click="saveSectionNoRuiOther()" style="padding: 12px">Salva</button>
			</section>
		</section>
		<!-- /E -->

	</div>
</div>

