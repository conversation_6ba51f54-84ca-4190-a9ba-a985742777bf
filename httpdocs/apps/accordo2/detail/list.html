<div class="x-grid-row">
    <div class="x-col-12-md">
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 15px; background-color: #C8D200">
            <div>
                <select id="year" ng-model="selectedYear" ng-change="yearFilterChange()" style="margin-right: 10px">
                    <option value="{{year}}" ng-selected="year.toString() === selectedYear.toString()" ng-repeat="year in years">{{year}}</option>
                </select>
                <company-filters style="margin-right: 10px"></company-filters>
                <!--<span style="color: #3A3A3A; font-size: 18px; font-weight: 700">Solo Agenzie <PERSON>i <button type="button" class="x-btn" ng-class="filters.isWinningAgency ? 'x-green' : 'x-secondary'" ng-click="toggleWinningAgency()"><i ng-class="filters.isWinningAgency ? 'icon-checkmark' : 'icon-close'"></i></button></span>-->
            </div>
            <div>
                <button class="x-btn x-red" ng-click="buildExportUrl('storico')"><i class="icon-download"></i> Scarica Storico</button>
                <button class="x-btn x-red" ng-click="buildExportUrl()"><i class="icon-download"></i> Scarica Excel</button>
            </div>
        </div>
    </div>
</div>

<div class="x-table-container">
    <table class="x-table">

        <thead>
        <tr>
            <th class="x-sorting" ng-class="{'x-sorting-desc' : detailsTableHeaders[0].sortType === 'desc', 'x-sorting-asc' : detailsTableHeaders[0].sortType === 'asc'}" ng-click="sort(0)">Area</th>
            <th class="x-sorting" ng-class="{'x-sorting-desc' : detailsTableHeaders[1].sortType === 'desc', 'x-sorting-asc' : detailsTableHeaders[1].sortType === 'asc'}" ng-click="sort(1)">District Manager</th>
            <th class="x-sorting" ng-class="{'x-sorting-desc' : detailsTableHeaders[2].sortType === 'desc', 'x-sorting-asc' : detailsTableHeaders[2].sortType === 'asc'}" ng-click="sort(2)">Agenzia</th>
            <th class="x-sorting" ng-class="{'x-sorting-desc' : detailsTableHeaders[3].sortType === 'desc', 'x-sorting-asc' : detailsTableHeaders[3].sortType === 'asc'}" ng-click="sort(3)">
                OBJ Sviluppo
                <br>
                <select class="select-sm" ng-model="selectedStatus" ng-change="otherFilterChange()" ng-click="$event.stopPropagation()" ng-options="status as status.label for status in statusLabels"></select>
            </th>
            <th class="x-sorting" ng-class="{'x-sorting-desc' : detailsTableHeaders[4].sortType === 'desc', 'x-sorting-asc' : detailsTableHeaders[4].sortType === 'asc'}" ng-click="sort(4)">
                OBJ Personalizzati
                <br>
                <select class="select-sm" ng-model="selectedType" ng-change="otherFilterChange()" ng-click="$event.stopPropagation()" ng-options="type as type.label for type in typeLabels"></select>
            </th>
            <th></th>
            <th></th>
        </tr>
        </thead>

        <tbody>
        <tr ng-repeat="row in detailsTableData">
            <td>{{row.areaName}}</td>
            <td>{{row.districtMng}}</td>
            <td>{{row.agenzia_id}} {{row.localita}}</td>
            <td>
                <strong ng-if="row.partecipante" ng-style="{'color' : rawValueFormatter(row.status, 'color', 'status')}">{{rawValueFormatter(row.status, 'label', 'status')}}</strong>
                <strong ng-if="! row.partecipante">-</strong>
            </td>
            <td ng-style="{'color' : row.type === 'STANDARD' ? '#3A3A3A' : '#C8D200'}" style="font-weight: 700">
                <span ng-if="row.partecipante">{{rawValueFormatter(row.type, 'label', 'type')}}</span>
                <span ng-if="! row.partecipante">-</span>
            </td>
            <td>
                <select class="select-sm" style="background-color: #C8D200" ng-change="updatePartecipanteFlag(row)" ng-model="row.partecipante">
                    <option value="0">Non Partecipante</option>
                    <option value="1">Sviluppo e Redditività</option>
                    <option value="2">Solo Redditività</option>
                    <option value="3">Solo Sviluppo</option>
                </select>
            </td>
            <td>
                <button class="x-btn x-green" ui-sref="apps-accordo2.agency-detail({agency_id: row.agenzia_id, agreement_id: row.id, objPersonal: rawValueFormatter(row.type, 'label', 'type')})"><i class="icon-eye"></i> Visualizza la Scheda</button>
            </td>
        </tr>
        <!--<tr ng-repeat="row in data | orderBy:sort.column:sort.reverse">
            <td align="center">
                <progress-bar value="{{row['aggregate']}}"></progress-bar>
            </td>
            <td>{{ row['measurableId'] }}</td>
            <td ng-repeat="kpi in row['kpis'] track by $index" ng-if="id != 0" ng-click="loadDetail(kpi.rowId)" class="kpi-value">
                {{ kpi.value }}
            </td>
        </tr>-->
        </tbody>

    </table>
</div>