<div class="x-grid-row">
    <div class="x-col-12-md">
        <div style="padding: 15px; display: flex; align-items: center">
            <div>
                <a class="x-btn x-green" ui-sref="apps-accordo2.detail.list"><i class="icon-arrow-left"></i> Torna ell'Elenco</a>
            </div>
            <div style="width: 100%; color: #FFFFFF; font-size: 24px; text-transform: uppercase; text-align: center">{{agencyAgreement.agenzia_id}} - accordo economico {{selectedYear}}</div>
            <div>
                <div style="display: inline-block; padding: 8px 15px; background-color: #FFFFFF; color: #E8501D; font-size: 16px; border-radius: 4px; font-weight: 700">Deadline sottoscrizione: 9/9/2024</div>
            </div>
        </div>
    </div>
</div>

<div style="padding: 20px; background-color: #EEF0BC">

    <div style="text-align: center; margin-bottom: 20px">
        <select ng-model="selectedRevision" ng-change="revisionChange()" ng-options="revision as revision.label for revision in revisionsList"></select>
    </div>

    <div class="x-grid-row">
        <div class="x-col-6-md">
            <div class="section-header" style="padding: 20px 0;">INFORMAZIONI</div>
            <div class="agency-info">

                <div>Agenzia</div>
                <div>{{agencyInfo.localita}}</div>

                <div>Provincia</div>
                <div>{{agencyInfo.provincia}}</div>

                <div>Area</div>
                <div>{{agencyInfo.area}}</div>

                <div>District Manager</div>
                <div>{{agencyInfo.district}}</div>

                <div>OBJ Sviluppo</div>
                <div ng-style="{'color' : formatStatusLabel(agencyDetail.status, 'color')}">{{formatStatusLabel(agencyDetail.status, 'label')}}</div>

                <div>OBJ Personalizzati</div>
                <div>{{objPersonal}}</div>

            </div>
            <div style="text-align: center; padding-bottom: 20px">
                <button class="x-btn x-green" ng-click="buildExportUrl()"><i class="icon-download"></i> Scarica la Storia</button>
            </div>
        </div>
        <div class="x-col-6-md">

            <div class="x-grid-row">
                <div class="x-col-12-md">
                    <div class="section-header" style="padding: 20px 0;">NUOVA PRODUZIONE TCM VITA {{selectedYear}}</div>
                </div>
            </div>

            <div class="x-grid-row">

                <div class="x-col-6-md" style="padding: 0 2rem;">
                    <div class="section-subtitle">Obiettivo Annuale 1° Livello</div>

                    <div class="table-section">
                        <div>
                            <div class="row-label">Base Calcolo Obiettivo <a style="text-decoration: underline; font-weight: 700; color: #00674F" href="/api/apps/accordo2/base?id={{agencyAgreement.agenzia_id}}&year={{selectedYear}}">(Excel)</a></div>
                            <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.vitaBaseCalcoloObj" type="text" ui-number-mask="0"></div>
                        </div>
                        <div>
                            <div class="row-label">Numero Polizze</div>
                            <div class="row-value"><input ng-readonly="isReadonly()" ng-model="agencyDetail.vitaAnnL1ObjPezzi" type="text" ui-number-mask="0"></div>
                        </div>
                        <div>
                            <div class="row-label">Obiettivo al 31/12/{{selectedYear}}</div>
                            <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.vitaAnnL1IncassiCur" type="text" ui-number-mask="0"></div>
                        </div>
                        <div>
                            <div class="row-label">Aliquota Rappel</div>
                            <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.vitaAnnL1Rappel" type="text" ui-number-mask="2"></div>
                        </div>
                    </div>
                </div>

                <div class="x-col-6-md" style="padding: 0 2rem;">

                    <div class="info-panel" ng-show="infoPanelVisible.life">
                        <div class="close" ng-click="toggleInfoPanel('life')">
                            <i class="icon-close" style="color: #00674F; cursor: pointer"></i>
                        </div>
                        <h4>Fascia Agenzia: {{agencyAgreement.fasciaVita}}</h4>
                        <table>
                            <thead>
                            <tr>
                                <th></th>
                                <th>Fino a Obj %</th>
                                <th>Rappel % max</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr ng-repeat="row in vitaSlots" ng-class="{'highlight' : row.selected }">
                                <td>PERS. {{row.pers}}</td>
                                <td>{{row.obj | number : "2"}}</td>
                                <td>{{row.rappelMax | number : "2"}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="section-subtitle">Obiettivo Annuale Personalizzato <i class="icon-info" style="color: #00674F; cursor: pointer" ng-click="toggleInfoPanel('life')"></i></div>

                    <div class="table-section">
                        <div>
                            <div class="row-label">Aliquota Obiettivo</div>
                            <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-class="{'invalid' : ! vitaObjAllowed}" ng-model="agencyDetail.vitaAnnL2Obj" ng-change="onLifeRateChange('Ann', 'L2')" type="text" ui-number-mask="2"></div>
                        </div>
                        <div>
                            <div class="row-label">Obiettivo al 31/12/{{selectedYear}}</div>
                            <div class="row-value">
                                <div>
                                    <span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span>
                                    <input ng-model="agencyDetail.vitaAnnL2IncassiCur"
                                           ng-class="{'invalid' : ! checkObjVitaIsValid(agencyDetail.vitaAnnL2IncassiCur)}"
                                           ng-change="calculateVitaObj()"
                                           type="text" ui-number-mask="0">
                                </div>
                                <div class="invalid-feedback" ng-show="! checkObjVitaIsValid(agencyDetail.vitaAnnL2IncassiCur)" style="text-align: right; color: red">L'obiettivo personalizzato deve essere maggiore dell'Obiettivo Annuale 1° Livello.</div>
                            </div>
                        </div>
                        <div>
                            <div class="row-label">Aliquota Rappel</div>
                            <div class="row-value">
                                <div>
                                    <span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span>
                                    <input ng-class="{'invalid' : ! checkRappelAllowed(agencyDetail.vitaAnnL2ExtraRappel, 'vita') || ( agencyDetail.vitaAnnL2IncassiCur && !agencyDetail.vitaAnnL2ExtraRappel && $authData.UTYPE !== 'AMMINISTRATORE') }" ng-model="agencyDetail.vitaAnnL2ExtraRappel" type="text" ui-number-mask="2">
                                </div>
                                <div class="invalid-feedback" ng-show="! checkRappelAllowed(agencyDetail.vitaAnnL2ExtraRappel, 'vita') || ( agencyDetail.vitaAnnL2IncassiCur && !agencyDetail.vitaAnnL2ExtraRappel && $authData.UTYPE !== 'AMMINISTRATORE')" style="text-align: right; color: red">L'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.</div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div class="section-header" style="padding: 20px 0;">INCASSI RP {{selectedYear}} <span style="text-decoration: underline; text-decoration-style: dotted; cursor: pointer" ng-click="showFasciaSelect = ! showFasciaSelect">FASCIA <span ng-if="! showFasciaSelect">{{agencyAgreement.fasciaRamiPref}}</span></span> <select ng-if="showFasciaSelect" ng-model="assignedSlot" ng-change="slotChange(assignedSlot)" ng-options="slot.name as slot.name for slot in slotsList"></select></div>
        </div>
    </div>

    <div class="x-grid-row">

        <!-- SEMSTRALE -->
        <div class="x-col-3-md" style="padding: 0 2rem;">
            <div class="section-subtitle">Obiettivo Semestrale</div>

            <div class="table-section">
                <div>
                    <div class="row-label">Incassi RP emessi {{selectedYear - 1}}</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefSemIncassiPrev" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Aliquota Obiettivo</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefSemObj" ng-change="onRamiRateChange('Sem')" type="text" ui-number-mask="2"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo di incremento R.P.</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefSemIncremento" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo emessi al 30/06/{{selectedYear}}</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefSemIncassiCur" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Aliquota Rappel</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefSemRappel" type="text" ui-number-mask="2"></div>
                </div>
            </div>
        </div>

        <!-- PRIMO LIVELLO -->
        <div class="x-col-3-md" style="padding: 0 2rem;">
            <div class="section-subtitle">Obiettivo Annuale 1° Livello</div>

            <div class="table-section">
                <div>
                    <div class="row-label">Incassi RP emessi {{selectedYear - 1}}</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL1IncassiPrev" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Aliquota Obiettivo</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL1Obj" ng-change="onRamiRateChange('Ann', 'L1')" type="text" ui-number-mask="2"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo di incremento R.P.</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL1Incremento" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo emessi al 31/12/{{selectedYear}}</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span>
                            <input ng-model="agencyDetail.ramiPrefAnnL1IncassiCur"
                                   ng-readonly="isReadonly()"
                                   ng-class="{'invalid' : ! checkObjRPIsValid(agencyDetail.ramiPrefAnnL1IncassiCur, 'AnnL1')}"
                                   ng-change="calculateRPObj('AnnL1')" type="text" ui-number-mask="0">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkObjRPIsValid(agencyDetail.ramiPrefAnnL1IncassiCur, 'AnnL1')" style="text-align: right; color: red">L'Obiettivo Annuale 1° Livello deve essere maggiore dell'Incassi Emessi anno precedente.</div>
                    </div>
                </div>
                <div>
                    <div class="row-label">Aliquota Rappel</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span>
                            <input ng-class="{'invalid' : ! checkRappelAllowed(agencyDetail.ramiPrefAnnL1Rappel, 'ramiPref') || ( agencyDetail.ramiPrefAnnL1IncassiCur && !agencyDetail.ramiPrefAnnL1Rappel) }" ng-model="agencyDetail.ramiPrefAnnL1Rappel" type="text" ui-number-mask="2">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkRappelAllowed(agencyDetail.ramiPrefAnnL1Rappel, 'ramiPref') || ( agencyDetail.ramiPrefAnnL1IncassiCur && !agencyDetail.ramiPrefAnnL1Rappel)" style="text-align: right; color: red">L'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SECONDO LIVELLO -->
        <div class="x-col-3-md" style="padding: 0 2rem;">
            <div class="section-subtitle">Obiettivo Annuale 2° Livello</div>

            <div class="table-section">
                <div>
                    <div class="row-label">Aliquota Obiettivo</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL2Obj"  ng-change="onRamiRateChange('Ann', 'L2')" type="text" ui-number-mask="2"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo di incremento R.P.</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL2Incremento" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo emessi al 31/12/{{selectedYear}}</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span>
                            <input ng-model="agencyDetail.ramiPrefAnnL2IncassiCur"
                                   ng-readonly="isReadonly()"
                                   ng-class="{'invalid' : ! checkObjRPIsValid(agencyDetail.ramiPrefAnnL2IncassiCur, 'AnnL2')}"
                                   ng-change="calculateRPObj('AnnL2')" type="text" ui-number-mask="0">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkObjRPIsValid(agencyDetail.ramiPrefAnnL2IncassiCur, 'AnnL2')" style="text-align: right; color: red">L'Obiettivo Annuale 2° Livello deve essere maggiore dell'Obiettivo Annuale 1° Livello.</div>
                    </div>
                </div>
                <div>
                    <div class="row-label">Aliquota Rappel</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span>
                            <input ng-class="{'invalid' : ! checkRappelAllowed(agencyDetail.ramiPrefAnnL2Rappel, 'ramiPref') || ( agencyDetail.ramiPrefAnnL2IncassiCur && !agencyDetail.ramiPrefAnnL2Rappel) }" ng-model="agencyDetail.ramiPrefAnnL2Rappel" type="text" ui-number-mask="2">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkRappelAllowed(agencyDetail.ramiPrefAnnL2Rappel, 'ramiPref') || ( agencyDetail.ramiPrefAnnL2IncassiCur && !agencyDetail.ramiPrefAnnL2Rappel)" style="text-align: right; color: red">L'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="x-col-3-md" style="padding: 0 2rem;">

            <div class="info-panel" ng-show="infoPanelVisible.rp">
                <div class="close" ng-click="toggleInfoPanel('rp')">
                    <i class="icon-close" style="color: #00674F; cursor: pointer"></i>
                </div>
                <h4>Fascia Agenzia: {{agencyAgreement.fasciaRamiPref}}</h4>
                <table>
                    <thead>
                    <tr>
                        <th></th>
                        <th>Fino a Obj %</th>
                        <th>Rappel % max</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-repeat="row in ramiPrefSlots" ng-class="{'highlight' : row.selected }">
                        <td>PERS. {{row.pers}}</td>
                        <td>{{row.obj | number : "2"}}</td>
                        <td>{{row.rappelMax | number : "2"}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>

            <!-- PERSONALIZZATO -->
            <div class="section-subtitle">Obiettivo Annuale Personalizzato <i class="icon-info" style="color: #00674F; cursor: pointer" ng-click="toggleInfoPanel('rp')"></i></div>

            <div class="table-section">
                <div>
                    <div class="row-label">Aliquota Obiettivo</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span> <input ng-readonly="isReadonly()" ng-class="{'invalid' : ! ramiPrefObjAllowed}" ng-model="agencyDetail.ramiPrefAnnL2ObjPersonal" ng-change="onRamiRateChange('Ann', 'L2', 'Personal')" type="text" ui-number-mask="2"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo di incremento R.P.</div>
                    <div class="row-value"><span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span> <input ng-readonly="isReadonly()" ng-model="agencyDetail.ramiPrefAnnL2IncrementoPersonal" type="text" ui-number-mask="0"></div>
                </div>
                <div>
                    <div class="row-label">Obiettivo emessi al 31/12/{{selectedYear}}</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">€</span>
                            <input ng-model="agencyDetail.ramiPrefAnnL2IncassiCurPersonal"
                                   ng-class="{'invalid' : ! checkObjRPIsValid(agencyDetail.ramiPrefAnnL2IncassiCurPersonal, 'AnnL2', 'Personal')}"
                                   ng-change="calculateRPObj('AnnL2', 'Personal')" type="text" ui-number-mask="0">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkObjRPIsValid(agencyDetail.ramiPrefAnnL2IncassiCurPersonal, 'AnnL2', 'Personal')" style="text-align: right; color: red">L'obiettivo personalizzato deve essere maggiore dell'Obiettivo Annuale 2° Livello.</div>
                    </div>
                </div>
                <div>
                    <div class="row-label">Aliquota Rappel</div>
                    <div class="row-value">
                        <div>
                            <span style="font-size: 20px; font-weight: 700; margin-right: 5px">%</span>
                            <input ng-class="{'invalid' : ! checkRappelAllowed(agencyDetail.ramiPrefAnnL2RappelPersonal, 'ramiPref') || ( agencyDetail.ramiPrefAnnL2IncassiCurPersonal && !agencyDetail.ramiPrefAnnL2RappelPersonal && $authData.UTYPE !== 'AMMINISTRATORE') }" ng-model="agencyDetail.ramiPrefAnnL2RappelPersonal" type="text" ui-number-mask="2">
                        </div>
                        <div class="invalid-feedback" ng-show="! checkRappelAllowed(agencyDetail.ramiPrefAnnL2RappelPersonal, 'ramiPref') || ( agencyDetail.ramiPrefAnnL2IncassiCurPersonal && !agencyDetail.ramiPrefAnnL2RappelPersonal && $authData.UTYPE !== 'AMMINISTRATORE')" style="text-align: right; color: red">L'Aliquota Rappel deve essere maggiore di 0 e minore della % rappel massima.</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div class="section-header" style="padding: 20px 0;">STATO DELLA LAVORAZIONE</div>
        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div style="text-align: center; color: #3A3A3A; font-size: 16px; font-weight: 700; padding: 30px 0 20px">
                Indicare ed aggiornare tempestivamente lo stato della lavorazione.
            </div>
        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div style="text-align: center">
                <div class="radio-container" ng-repeat="status in statusLabels track by $index" ng-if="checkAvailableState(status)">
                    <label for="status{{$index}}" ng-style="{'color' : formatStatusLabel(status.id, 'color')}">{{status.label}}</label>
                    <input type="radio" ng-disabled="( user === 'AREAMGR' || user === 'DISTRICTMGR') && agencyDetail.status === 'ATTESA_RICALCOLO'" ng-value="status.id" ng-model="agencyDetail.status" id="status{{$index}}" style="width: auto">

                </div>
            </div>
        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div style="text-align: center; color: #3A3A3A; font-size: 16px; font-weight: 400; padding: 30px 0 30px; border-bottom: 4px solid #FFFFFF">
                È possibile effettuare e salvare variazioni parziali usando il pulsante “SALVA” (il DM vedrà comunque le modifiche parziali).
                <br>
                Completate le operazioni e definiti i nuovi valori, selezionare lo status “DA RILAVORARE” e poi cliccare su “SALVA” (il comando lancia anche una e-mail di avvertimento verso il DM e l’AM di competenza).
            </div>
        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-offset-3-md x-col-6-md">
            <div ng-if="agencyDetail.year < 2022" style="padding: 30px 0; border-bottom: 4px solid #FFFFFF">
                <textarea placeholder="Scrivi qui il tuo messaggio" cols="30" rows="10" ng-model="agencyDetail.statusChangeMessage"></textarea>
            </div>
            <div ng-if="agencyDetail.year >= 2022" style="padding: 30px 0">
                <div ng-repeat="message in agencyDetail.statusChangeMessage" ng-if="agencyDetail.statusChangeMessage.length">
                    <div class="feed-container {{message.userType}}">
                        <div style="padding: 10px 20px 10px; color: #3A3A3A; font-size: 20px"><strong>{{message.author}}</strong> - {{ message.timestamp}}</div>
                        <div style="background-color: #C8D200; border-radius: 8px; padding: 20px; color: #3A3A3A; font-size: 20px">
                            {{message.text}}
                        </div>
                    </div>
                </div>
                <textarea placeholder="Scrivi qui il tuo messaggio" cols="30" rows="10" ng-model="newMessage.text" style="margin-top: 30px"></textarea>
            </div>
        </div>
    </div>

    <div class="x-grid-row">
        <div class="x-col-12-md">
            <div style="display: flex; align-items: center; justify-content: space-around; padding: 30px 0">
                <div style="text-align: center; font-size: 12px">
                    Stampare il modulo da far firmare solo dopo il salvataggio dei dati.
                    <br>
                    <br>
                    <a class="x-btn x-green" target="_blank" href="/api/apps/accordo2/pdf/stampa.html?agreementId={{selectedAgreement}}"><i class="icon-print"></i> Stampa</a>
                </div>
                <div style="text-align: center; font-size: 12px" ng-class="{'not-allowed' : ! validData}">
                    Qualunque modifica effettuata sarà registrata solo con il tasto “SALVA”.
                    <br>
                    <br>
                    <button class="x-btn x-red" ng-click="saveRevision()" ng-disabled="! validData"><i class="icon-disk"></i> Salva</button>
                    <br>
                    <div style="color: red" ng-if="! validData">Per salvare ricontrollare che i dati inseriti siano corretti.</div>
                </div>
            </div>
        </div>
    </div>

</div>
