(function(ng, module) {

	module.config(['$stateProvider', function($stateProvider) {
		$stateProvider
			.state('gestione-privacy',		{ url: '/gestione-privacy', templateUrl: 'privacy/liberatoria-gasite.html',	controller: 'app.privacy.Ctrl',		navbar: 'dashboard' })
		;
	}]);

	module.controller('app.privacy.Ctrl', ['$scope', '$http', 'xng.$data', 'xng.ui.$growl', 'xng.ui.$modal', '$window', function($scope, $http, $data, $growl, $modal, $window) {
		$scope.userData = $scope.$authData;
		$scope.showPrivacy = false;
		//console.log($scope.userData);
		$scope.privacyStatus = {
			consent: null,
			gdprA: null,
			gdprB: null,
		};

		$scope.onGdprAChange = function() {

			$scope.message = 'Il consenso è facoltativo, il mancato consenso impedisce di usufruire dei servizi di visibilità (Pagina Agenzia sul Sito Istituzionale Groupama.it) offerti sul Portale Agendo.';

			if (! $scope.privacyStatus.gdprA) {
				$scope.privacyModal = $modal.open({ scope: $scope, templateUrl: '/privacy/warning-modal.html' });
			}

		};

		$scope.onGdprBChange = function() {

			$scope.message = 'Il consenso è facoltativo, il mancato consenso impedisce di usufruire dei servizi di visibilità (Pagina Agenzia sul Sito Istituzionale Groupama.it) offerti sul Portale Agendo.';

			if (! $scope.privacyStatus.gdprB) {
				$scope.privacyModal = $modal.open({ scope: $scope, templateUrl: '/privacy/warning-modal.html' });
			}

		};

		$scope.onConsentChange = function() {

			$scope.message = 'L’autorizzazione è facoltativa, il mancato rilascio delle stessa impedisce la pubblicazione della tua immagine sulla pagina Agenzia nel Sito Istituzionale Groupama.it';

			if (! $scope.privacyStatus.consent) {
				$scope.privacyModal = $modal.open({ scope: $scope, templateUrl: '/privacy/warning-modal.html' });
			}

		};

		$scope.savePrivacyStatus = function() {

			$http({ method: 'POST', data: $scope.privacyStatus, url: '/api/apps/cms-agenzie/privacy/change' })
				.then( function(response) {

					$scope.privacyRequestWaiting = false;

					if (response.data.success) {
						//$scope.showAlert = true;
						$growl.success('Preferenze aggiornate.', { icon: 'icon-checkmark', ttl: 4000 });
					}

					window.location.href = '/cms-agenzie/#!/' + $scope.userData.AGENZIA + '/dati';

					}
				);

		};

	}]);

})(angular, angular.module('app.privacy', []));
