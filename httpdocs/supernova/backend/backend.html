<div class="container">

    <div class="row pt-5 mb-5">
        <div class="col d-block d-sm-flex justify-content-sm-between">
            <div class="text-center text-sm-left">
                <img ng-src="//{{staticHost}}/themes/incentivazioni/media/supernova/logo.svg" alt="" class="img-fluid mb-5 mb-sm-0" style="max-width: 250px">
                <div style="font-size: 16px; font-weight: 700; padding: 15px 0 0; color: #ffffff">Dati aggiornati al: {{lastUpdateLabel | date : 'd-M-yyyy'}}</div>
            </div>
            <div class="d-flex flex-column justify-content-center">
                <div class="mini-menu d-flex justify-content-center justify-content-md-end align-items-center">
                    <!--<a ui-sref="">Torna alla home</a> <span class="divider">|</span>--> <a href="/api/apps/supernova/agenzie/pdf">Regolamento</a>
                </div>
            </div>
        </div>
    </div>

    <!--<pre>
        usertype: {{userAuth.UTYPE}}
        tableview: {{tableView}}
        selectedArea: {{selectedArea | json}}
        selectedDistrict: {{selectedDistrict | json}}
    </pre>-->

    <div class="row mb-5">
        <div class="col">
            <div class="table-responsive">
                <table class="table position-relative">
                    <thead>
                    <tr>
                        <th ng-if="tableView === 'GLOBAL'">AREA</th>
                        <th ng-if="tableView === 'AREAS'">
                            DISTRETTI <span ng-if="selectedArea.areaName">AREA {{selectedArea.areaName}}</span>
                            <br>
                            <a ng-click="getSuperset('GLOBAL')" ng-if="accessLevel > 2">Torna alle aree</a>
                        </th>
                        <th ng-if="tableView === 'DISTRICTS'">
                            AGENZIE <span ng-if="selectedDistrict.districtName">DISTRETTO {{selectedDistrict.districtName}}</span>
                            <br>
                            <a ng-click="getSuperset('GLOBAL')" ng-if="accessLevel > 2">Torna alle aree</a><span style="color: #000000; margin: 0 10px;" ng-if="accessLevel > 2">|</span><a ng-click="getSuperset('AREAS')" ng-if="accessLevel > 1">Torna ai distretti</a>
                        </th>
                        <th class="text-right">Incentivo maturato<br>Investimento e Programma per te</th>
                        <th class="text-right">Incentivo maturato<br>per Nuovi Clienti Investimento e<br>Programma per te</th>
                        <th class="text-right">Incentivo maturato<br>Programma Open</th>
                        <th class="text-right">Incentivo maturato<br>totale</th>
                        <th class="text-right">
                            <span ng-if="tableView !== 'DISTRICTS'">% Agenzie con<br>importo minimo liquidabile</span>
                            <span ng-if="tableView === 'DISTRICTS'">Importo minimo<br>liquidabile raggiunto</span>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-if="loading">
                        <td colspan="6" style="padding: 0">
                            <div class="d-flex justify-content-center w-100 py-5" style="background-color: #ffffff">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Caricamento...</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr ng-repeat="row in tableData" ng-if=" ! loading">
                        <td>
                            <a ng-if="tableView !== 'DISTRICTS'" ng-click="getSubset(row)"><span ng-if="tableView === 'GLOBAL'">AREA: {{row.areaName}}</span><span ng-if="tableView === 'AREAS'">{{row.districtName}}</span></a>
                            <span ng-if="tableView === 'DISTRICTS'">{{row.agenzia_id}} - {{row.localita}}</span>
                        </td>
                        <td class="text-right">{{row.incentIPPT | currency : '€' : 2}}</td>
                        <td class="text-right">{{row.incentNC | currency : '€' : 2}}</td>
                        <td class="text-right">{{row.incentOpen | currency : '€' : 2}}</td>
                        <td class="text-right">{{row.incentTot | currency : '€' : 2}}</td>
                        <td class="text-right">
                            <span ng-if="tableView !== 'DISTRICTS'">{{row.objReachedPerc | number: 2}}%</span>
                            <span ng-if="tableView === 'DISTRICTS'"><span ng-if="!stringEval(row.objReached)">NO</span><span ng-if="stringEval(row.objReached)">SI</span></span>
                        </td>
                    </tr>
                    <tr class="total" ng-if="loadingTotals">
                        <td colspan="6" style="padding: 0">
                            <div class="d-flex justify-content-center w-100 py-2" style="background-color: #ffffff">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Caricamento...</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr class="total" ng-if="! loadingTotals">
                        <td>TOTALE</td>
                        <td class="text-right">{{tableTotalData.totalIncentIPPT | currency : "€" : 2}}</td>
                        <td class="text-right">{{tableTotalData.totalIncentNC | currency : "€" : 2}}</td>
                        <td class="text-right">{{tableTotalData.totalIncentOpen | currency : "€" : 2}}</td>
                        <td class="text-right">{{tableTotalData.totalIncentTot | currency : "€" : 2}}</td>
                        <td class="text-right">{{tableTotalData.totalPerc | number  : 2}}%</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>