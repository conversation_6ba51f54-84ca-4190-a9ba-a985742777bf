<header><i class="icoagendo-menu-news"></i> ARCHIVIO NEWS</header>

<section class="x-grid docs-grid" xng-grid="gridConfig">
	<div class="x-grid-row" ng-repeat="News in GridStore.data">
		<div class="x-col-4-md">
			<span class="docs-date">{{ News.date | date: 'dd/MM/yyyy' }}</span>
			<span class="docs-author">{{ News.author }}</span>
		</div>
		<div class="x-col-8-md docs-title">
			<div style="display: inline-block; width: 3em;"><a ng-if="News.file" ng-href="/api/download/news/{{News.id}}" target="_blank" class="x-btn btn-download"><i class="icon-download"></i></a></div>
			{{ News.title }}
		</div>
		<div class="x-col-12-md docs-text">{{ News.text }}</div>
	</div>

	<div class="x-table-footer">
		{{GridStore.status.currentFirst}}-{{GridStore.status.currentLast}} di {{GridStore.status.total}}
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<div class="x-btn-group">
			<a class="x-btn" ng-click="GridStore.firstPage()"><i class="icon-first"></i></a>
			<a class="x-btn" ng-click="GridStore.prevPage()"><i class="icon-previous"></i></a>
			<a class="x-btn x-disabled">{{ GridStore.status.currentPage }} / {{GridStore.status.totalPages}}</a>
			<a class="x-btn" ng-click="GridStore.nextPage()"><i class="icon-next"></i></a>
			<a class="x-btn" ng-click="GridStore.lastPage()"><i class="icon-last"></i></a>
		</div>
		&nbsp;
		<a class="x-btn" ng-click="GridStore.reload()"><i class="icon-loop"></i></a>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Paginazione <select ng-model="GridStore.pageSize" style="width: 5em"><option value="10">10</option><option value="25">25</option><option value="50">50</option></select>
	</div>
</section>
