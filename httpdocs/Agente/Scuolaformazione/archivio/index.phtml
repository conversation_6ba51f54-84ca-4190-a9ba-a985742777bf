<?php include dirname(__FILE__).'/../common/header.phtml'?>

<!--
<pre>
<?php echo $nome?>
<br/>
<?php echo $oreanno?>
<br/>
<?php print_r($status);?>
</pre>
-->
<div id="tabella_corsi_dettaglio">
	<div id="ind_ore_anno">
		<span class="nero"><b><?php echo $nome?></b></span>
        <span class="regular">- Agente</span><br/>
		<span class="verde">Per l'anno selezionato risultano accreditate</span>
        <span class="nero"><b><?php echo $oreanno;?> ore formative</b></span> <span class="verde">in aula.</span>
	</div>
</div>

<div id="box_scrollabile">
<div id="tabella_status">
    <table border="1" cellpadding="0" cellspacing="0">
            <tr>
                <th colspan="2" scope="col" class="th_verde" width="357">
                Archivio - I miei corsi
                <select name='year' onChange="location.href='/Agente/Scuolaformazione/archivio/?year='+this.value">
                	<?php foreach ($years as $y):?>
                	<option value='<?php echo $y?>' <?php echo $y==$year?'selected':''?>><?php echo $y?></option>
                	<?php endforeach;?>
                </select>
                </th>
                <th width="119" style="text-align:center;">Data</th>
                <th width="156" style="text-align:left;">Sede del Corso</th>
                <th width="202" style="text-align:left;">Status</th>
                <th width="120" style="text-align:left;">Esito</th>
                <th width="120" style="text-align:center;">Ore Accreditate</th>
            </tr>

        <?php if(!empty($status)):?>
        <?php foreach($status as $s):?>
            <tr>
                <td width="23" class="lente_img"><a href="/Agente/Scuolaformazione/corso/?id=<?php echo $s['idcorso']?>">
                	<img src="/Agente/Scuolaformazione/img/lente.gif" alt="" /></a>
                 </td>

                <td width="334" class="celeste_bold">
                	<a href="/Agente/Scuolaformazione/corso/?id=<?php echo $s['idcorso']?>" class="celeste_bold"><?php echo $s['nomecorso']?><br/>
                    	<span class="celeste_bold_it"><?php echo $s['dettagli']?></span>
                    </a><br/>
                    <span class="celeste">
						<?php echo $s['duratagiorni']?> <?php echo ($s['duratagiorni'] == '1' ? 'giorno' : 'giorni')?>
                    	- Ore formative <?php echo $s['oreformative']?>
                    </span>
                    <?php if($s['allegati']>0 && $s['web']==1 && $s['esito']=='ok'):?>
                    <a class="webhancor" href="/Agente/Scuolaformazione/archivio/allegati?id=<?php echo $s['idcorso']?>"><div class="allegatiweb"></div></a>
                    <?php endif;?>
                </td>

                <td width="119" class="celeste" style="text-align:center;"><?php echo date('d/m/Y', strtotime($s['giorno']));?></td>

                <td width="156" class="regular">
                    <span class="nero_bold"><?php echo $s['luogo']?></span><br />
                    <?php echo $s['struttura']?><br />
                    <?php echo $s['indirizzo']?><br />
                    <?php echo $s['localita']?><br />
                    <?php echo $s['telefono']?>
                </td>

                <td width="202" class="regular"><?php echo $s['statusiscrizione']?></td>

                 <td><?php echo $s['textesito']?></td>
                <td style="text-align:center;"><b><?php echo $s['oreaccreditate']?></b></td>
            </tr>
        <?php endforeach;?>
        <?php else:?>
        <tr>
                <td width="23" class="lente_img">
                 </td>

                <td width="365" class="celeste_bold">
                  <b>Nessun corso da visualizzare</b>
                </td>

                <td width="79" class="celeste" style="text-align:center;"></td>

                <td width="156" class="regular">

                </td>

                <td width="202" class="regular"></td>

                <td></td>
                <td style="text-align:center;"></td>
            </tr>
        <?php endif;?>

    </table>
</div>
</div>
<?php include dirname(__FILE__).'/../common/footer.phtml'?>