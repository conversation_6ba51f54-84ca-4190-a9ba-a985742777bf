<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="IT-it">
<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico" />

<link rel="stylesheet" type="text/css" media="all" href="/Agente/Scuolaformazione/css/css.css" />
<link rel="stylesheet" type="text/css" media="all" href="/Agente/Scuolaformazione/css/slidemenu.css" />
<link rel="stylesheet" type="text/css" media="all" href="/Agente/Scuolaformazione/css/prenotazione.css" />
<link rel="stylesheet" type="text/css" media="all" href="/Agente/Scuolaformazione/css/fancy.css" />
<link rel="stylesheet" type="text/css" media="all" href="/Agente/Scuolaformazione/css/jquery.jscrollpane.css" />

<!--[if IE 6]>
<link rel="stylesheet" href="/Agente/Scuolaformazione/css/iecss.css" />
<![endif]-->

<script type="text/javascript" src="/ControlPanel/fr/js/iepngfix_tilebg.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/jquery.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/slidemenu.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/fancybox/jquery.fancybox-1.3.4.pack.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/js.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/prenota.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/jquery.mousewheel.js"></script>
<script type="text/javascript" src="/Agente/Scuolaformazione/js/jquery.jscrollpane.min.js"></script>

<title>Scuola formazione</title>
</head>
<body>
<div id="container">
<div id="header">
	<!--logo -->
	<div id="logo_formazione_agenti">
		<img src="/Agente/Scuolaformazione/img/logo_scuola_formazione.png" alt="" />
	</div>
	<!--menu -->
	<div id="menu_primo_livello" class="jqueryslidemenu">
		<ul>
			<li class="<?php echo (isset($hoh)) ? 'hovermenu' : 'item_primo_livello';?>"><a href="/Agente/Scuolaformazione/">Corsi Disponibili</a></li>
			<li class="<?php echo (isset($hos)) ? 'hovermenu' : 'item_primo_livello';?>"><a href="javascript:void(0);">Status Corsi</a>
				<ul id="menu_secondo_livello_1">
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/status/">I miei Corsi</a></li>
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/status/dipendenti">Corsi Dipendenti</a></li>
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/status/intermediari">Corsi Intermediari</a></li>
				</ul>
			</li>

			<li class="<?php echo (isset($hoa)) ? 'hovermenu' : 'item_primo_livello';?>"><a href="javascript:void(0);">Archivio Corsi</a>
				<ul id="menu_secondo_livello_2">
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/archivio/">I miei Corsi</a></li>
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/archivio/dipendenti">Corsi Dipendenti</a></li>
					<li class="item_secondo_livello"><a href="/Agente/Scuolaformazione/archivio/intermediari">Corsi Intermediari</a></li>
				</ul>
			</li>
            <li class="item_primo_livello"><a href="/Agente/Scuolaformazione/allegati/">Documenti</a></li>
		</ul>
    <div style='display:none'>
      <form id='elearning-form' method='post' action='http://groupama.forassurance.it/esystem/indexagendo.asp' target='_new'>
        <input type='hidden' name='login' value='<?php print $_SESSION['User']->login2 ?: $_SESSION['User']->login ?>' />
        <input type='hidden' name='authkey' value='dh7843hdwfeD9DHFRehwf9FEFf9fhehfFEF49fw4rf4f4f42kF8hD8jgFGa9KmGrSD' />
      </form>
    </div>
	</div>
	<div id="numeroposta" class="<?php echo (isset($tts)) ? 'applica' : 'numeroposta';?>"><a href="/Agente/Scuolaformazione/tts/" id="box_posta">Posta <?php echo $ticket?></a></div>
</div>
<div class="clear_both"></div>

