/* CSS Document */


body {
margin: 0px;
padding: 0px;
background-color: #FFFFFF;

behavior: url('/Agente/js/csshover3.htc');	
}


img{
display: block;
margin: 0px;
padding: 0px;
border: 0px;
}



object, embed{
display: block;
margin: 0px;
padding: 0px;
border: 0px;
}

html, body{
height: 100%;
}

div
{
	font-family:Arial;
	font-size:11px;
}

/* BASE STYLE */
ul {margin-top: 0px;margin-bottom: 0px;}
li {margin-top: 0px;margin-bottom: 0px;}



select
{
	font-family:Verdana, Helvetica, sans-serif;
	font-size:12px;	
}


table
{
	border: 0px;
	padding: 0px;
	margin: 0px;
}

tr
{
	border: 0px;
	padding: 0px;
	margin: 0px;
}

td
{
	border: 0px;
	padding: 5px 10px 5px 10px;
	margin: 0px;
}

.clear
{
	clear:left;
}

.centered
{
	display:block;
	margin: 0 auto;
}

A{ color: #000; text-decoration: none;}
.a_nocolor{ color: #818181; text-decoration: none;}
.a_nocolor:hover { color: #000000; text-decoration: none;}
/*
A:link {text-decoration: none;}
A:visited {text-decoration: none;}
A:active {text-decoration: none;}
A:hover {text-align: center; font-weight: bold;}
*/


.gray_11_link:link 
{
	font-size:11px;
	color: #818181;
	font-weight: bold;	
}
.gray_11_link:visited 
{
	font-size:11px;
	color: #818181;
	font-weight: bold;	
}
.gray_11_link:active 
{
	font-size:11px;
	color: #818181;
	font-weight: bold;	
}
.gray_11_link:hover 
{
	font-size:11px;
	color: #000000;
	font-weight: bold;	
}



.spacer_5{
  height: 5px;  
} 

.spacer_10{
  height: 10px;  
} 
.spacer_20{
  height: 20px;   
} 
.spacer_30{
  height: 30px; 
} 

/* SPAN TYPES*/

.font_10_black
{
	font-family:Arial;
	font-size:10px;
	color: #000000;

}

.font_10_black_bold
{
	font-family:Arial;
	font-size:10px;
	color: #000000;
	font-weight: bold;	

}

.font_10_gray
{
	font-family:Arial;
	font-size:10px;
	color: #818181;

}

.font_10_gray_bold
{
	font-family:Arial;
	font-size:10px;
	color: #818181;
	font-weight: bold;	

}

.font_11_black
{
	font-family:Arial;
	font-size:11px;
	color: #000000;

}

.font_11_black_bold
{
	font-family:Arial;
	font-size:11px;
	color: #000000;
	font-weight: bold;	

}

.font_11_gray
{
	font-family:Arial;
	font-size:11px;
	color: #818181;

}

.font_11_gray_bold
{
	font-family:Arial;
	font-size:11px;
	color: #818181;
	font-weight: bold;	

}

.font_12_black
{
	font-family:Arial;
	font-size:12px;
	color: #000000;

}

.font_12_black_bold
{
	font-family:Arial;
	font-size:12px;
	color: #000000;
	font-weight: bold;	

}

.font_12_gray
{
	font-family:Arial;
	font-size:12px;
	color: #818181;

}

.font_12_gray_bold
{
	font-family:Arial;
	font-size:12px;
	color: #818181;
	font-weight: bold;	

}

.font_13_black
{
	font-family:Arial;
	font-size:13px;
	color: #000000;

}

.font_13_black_bold
{
	font-family:Arial;
	font-size:13px;
	color: #000000;
	font-weight: bold;	

}

.font_13_gray
{
	font-family:Arial;
	font-size:13px;
	color: #818181;

}

.font_13_gray_bold
{
	font-family:Arial;
	font-size:13px;
	color: #818181;
	font-weight: bold;	

}

.font_13_gray_italic
{
	font-family:Arial;
	font-size:13px;
	color: #818181;
	font-weight: bold;	
	font-style: italic;
}

.font_13_orange_bold
{
	font-family:Arial;
	font-size:13px;
	color: #FF7F00;
	font-weight: bold;	

}
.font_14_blue_bold
{
	font-family:Arial;
	font-size:14px;
	color: #2883B2;
	font-weight: bold;	

}

.font_14_black
{
	font-family:Arial;
	font-size:14px;
	color: #000000;
}

.font_20_black_bold
{
	font-family:Arial;
	font-size:20px;
	color: #000000;
	font-weight: bold;	

}

.font_16_orange_dark
{
	font-family:Arial;
	font-size:16px;
	color: #E75113;
	font-weight: bold;	
}

.font_16_orange_light
{
	font-family:Arial;
	font-size:24px;
	color: #FF6600;
	font-weight: bold;	
}


.font_red
{
	color: #FF0000;
}

.font_red_bold
{
	color: #FF0000;
	font-weight: bold;	
}

.font_bold
{
	font-weight: bold;	
}

.div_relative
{
	position: relative;	
}

.div_absolute
{
	position: absolute;	
}

/* INIZIATIVA */

#center_frame_container
{
	float: left;
	width: 884px;
	height: 507px;			
	overflow: hidden;
	position: relative;
	background-color: #E6E7E7;	
	border-left: 1px solid #E6E7E7;
	border-top: 1px solid #E6E7E7;
}

#content_container
{
	float: left;
	width: 868px;
	height: 491px;		
	top: 8px;
	left: 8px;	
	overflow: hidden;
	position: relative;
	background-color: #9EC6DC;
}

#menu
{
	float: left;
	width: 868px;
	height: 23px;			
	overflow: hidden;
	background-color: #79B1CF;
}

	#menu_1
	{
		float: left;
		width: 65px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;
		cursor:pointer;
	}
	#menu_2
	{
		float: left;
		width: 115px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;
		cursor:pointer;		
	}
	#menu_3
	{
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;	
		cursor:pointer;	
	}
	#menu_3_empty
	{ 
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;			
	}	
	#menu_4
	{
		float: left;
		width: 125px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;	
		cursor:pointer;	
	}
	#menu_4_empty
	{ 
		float: left;
		width: 125px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;			
	}		
	#menu_5
	{
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;	
		cursor:pointer;	
	}
	#menu_5_empty
	{ 
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;			
	}
	#menu_6
	{
		float: left;
		width: 343px;
		height: 23px;			
		overflow: hidden;
	}
	#menu_7
	{
		float: left;
		width: 100px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #FFFFFF;
		font-size: 10px;	
		cursor:pointer;	
	}	
	#menu_1_sel
	{
		float: left;
		width: 65px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;
	}
	#menu_2_sel
	{
		float: left;
		width: 115px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;		
	}
	#menu_3_sel
	{
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;		
	}
	#menu_4_sel
	{
		float: left;
		width: 125px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;		
	}
	#menu_5_sel
	{
		float: left;
		width: 60px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;		
	}
	#menu_7_sel
	{
		float: left;
		width: 100px;
		height: 23px;			
		overflow: hidden;
		text-align: center;
		line-height: 23px;
		color: #2883B2;
		background-color: #CADFE9;
		font-size: 10px;	
	}		
	#menu_1:hover,#menu_2:hover,#menu_3:hover,#menu_4:hover,#menu_5:hover,#menu_7:hover
	{
		color: #2883B2;
		background-color: #CADFE9;
	}		

#dettaglio_polizze_header
{
	float: left;
	width: 868px;
	height: 62px;			
	overflow: hidden;
	position: relative;
	background-image:url('images/dettaglio_polizze_header.png');
}	
	#dp_page_back
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 73px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);	*/
		cursor:pointer;		
	}	
	#dp_page_forw
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 157px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);	*/
		cursor:pointer;	
	}	
	#dp_page_number
	{
		float: left;
		width: 68px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 89px; 
		/*background-color: green;
		opacity:0.5;	
		filter: alpha(opacity = 50);		*/
		line-height: 16px;
		font-size: 10px;
		color: #000000;
		text-align: center;	
	}

#lista_clienti_target_header
{
	float: left;
	width: 868px;
	height: 62px;			
	overflow: hidden;
	position: relative;
	background-image:url('images/lista_clienti_target_header.png');
}	
	#lct_page_back
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 5px;
		left: 255px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);	*/
		cursor:pointer;		
	}	
	#lct_page_forw
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 5px;
		left: 339px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);		*/
		cursor:pointer;	
	}	
	#lct_page_number
	{
		float: left;
		width: 68px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 5px;
		left: 271px; 
		/*background-color: green;
		opacity:0.5;	
		filter: alpha(opacity = 50);		*/
		line-height: 16px;
		font-size: 10px;
		color: #000000;
		text-align: center;	
	}
	
#orderby_button
{
	float: left;
	width: 93px;
	height: 16px;			
	overflow: hidden;
	position: absolute;
	top: 4px;
	left: 582px; 
	/*background-color: green;
	opacity:0.5;	
	filter: alpha(opacity = 50);	*/
	line-height: 16px;
	font-size: 11px;
	color: #818181;
	cursor:pointer;
	padding: 0 0 0 5px;
}	
#scarica_button
{
	float: left;
	width: 141px;
	height: 16px;			
	overflow: hidden;
	position: absolute;
	top: 4px;
	left: 695px; 
	/*background-color: green;
	opacity:0.5;	
	filter: alpha(opacity = 50);		*/
	line-height: 16px;
	font-size: 11px;
	color: #818181;
	cursor:pointer;
	padding: 0 0 0 5px;
}	

#orderby_button:hover, #scarica_button:hover
{
	color: #000000;	
}		


/* FULLPAGE GRID */

	/* <![CDATA[ */
	#fullpage_grid {
		position: absolute;
		top: 85px;
		left: 10px;
		width: 831px;
		clip: rect(0px 831px 398px 0px);
		overflow: hidden;
		padding-bottom: 40px;
		clear: both;
	}
		
	#fullpage_grid p:first-child {
		margin-top: 0;
	}
	#fullpage_grid_handle {
		position: absolute;
		left: 0;
		top: 0;
		width: 12px;
		height: 13px;
		background-image:url('images/handle.png');
		behavior: url('/Agente/js/iepngfix.htc');
		cursor:pointer;
	}	
	#fullpage_grid_track {
		position: absolute;
		left: 845px;
		top: 85px;
		width: 12px;
		height: 398px;
		background-image:url('images/track.png');
		behavior: url('/Agente/js/iepngfix.htc');	
		/*background-repeat:repeat-y;*/
		/*background-color: red;*/
		clear: both;
	}
	
	#fullpage_grid-up {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_up.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: red;*/
	}
	#fullpage_grid-down {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_down.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: green;*/
	}
	/* ]]> */

.fullpage_grid_row
{
	float: left;
	width: 831px;  
	height: 17px;			
	overflow: hidden;
	/*background-color: yellow;
	opacity:0.5;	
	filter: alpha(opacity = 50);*/
	color: #000000;
	border-bottom: 2px solid #79B1CF;	
	display: block;
}	

.fullpage_grid_row div
{
	line-height: 17px;	
	font-size: 10px;
}	
	
.dp_grid_field_1
{
	float: left;
	width: 65px;		
	text-align: center;			
}

.dp_grid_field_2
{
	float: left;
	width: 125px;		
	text-align: center;			
}

.dp_grid_field_3
{
	float: left;
	width: 110px;		
	text-align: left;		
	padding: 0 0 0 25px;	
}

.dp_grid_field_4
{
	float: left;
	width: 250px;		
	text-align: left;	
	padding: 0 0 0 10px;	
}

.dp_grid_field_5
{
	float: left;
	width: 85px;		
	text-align: center;		
}

.dp_grid_field_6
{
	float: left;
	width: 144px;		
	text-align: right;	
	padding: 0 15px 0 0;		
}


#orderby_container
{
	float: left;
	width: 315px;
	height: 275px;

	position: absolute;
	top: 20px;
	left: 400px;	

	z-index: 9999;	
	/*background-image:url('images/orderby_background.png');		*/
	
	/*behavior: url('/Agente/js/iepngfix.htc');	*/
	/*background-repeat: no-repeat;*/
	/*background-color: red;
	opacity:0.5;	
	filter: alpha(opacity = 50);*/

}

	#orderby_background
	{
		float: left;
		width: 315px;
		height: 268px;
		z-index: 9999;	
		background-image:url('images/orderby_background.png');	
		
		behavior: url('/Agente/js/iepngfix.htc');
		/*background-repeat: no-repeat;*/
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);*/
	
	}
	
	#orderby_form
	{
		float: left;
		width: 315px;
		height: 268px;
		z-index: 9999;	
		/*background-image:url('/Agente/images/orderby_background.png');		*/
		
		/*behavior: url('/Agente/js/iepngfix.htc');	*/
		/*background-repeat: no-repeat;*/
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);*/
	
	}
		#orderby_select_1
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 36px;
			left: 25px;		
			z-index: 9999;
		}
		#orderby_select_2
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 85px;
			left: 25px;		
			z-index: 9999;		
		}
		#orderby_select_3
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 130px;
			left: 25px;		
			z-index: 9999;		
		}
		#orderby_select_4
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 178px;
			left: 25px;		
			z-index: 9999;		
		}	
		
		#orderby_no
		{
			float: left;
			width: 44px;
			height: 16px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 217px;
			left: 23px;		
			z-index: 9999;		
			cursor:pointer;
			line-height: 17px;
			padding: 0 0 0 20px;
		}
		#orderby_yes
		{
			float: left;
			width: 37px;
			height: 16px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 217px;
			left: 237px;		
			z-index: 9999;		
			cursor:pointer;
			line-height: 17px;
			padding: 0 0 0 6px;
		}				
	
		#orderby_no:hover
		{
			font-weight: bold;	
		}
		#orderby_yes:hover
		{
			font-weight: bold;	
		}	
	
		#orderby_check_1_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 26px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_1_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 44px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		
		#orderby_check_2_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);*/	
			position: absolute;
			top: 73px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_2_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 91px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		
		#orderby_check_3_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 120px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_3_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 138px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		
		#orderby_check_4_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 168px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_4_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 186px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		
		
/* SHORT GRID bg color: 006DFF */

	/* <![CDATA[ */

	#short_grid {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 245px;
		clip: rect(0px 245px 415px 0px);
		overflow: hidden;
		padding-bottom: 40px;
		clear: both;
	}

	#short_grid p:first-child {
		margin-top: 0;
	}
	#short_grid_handle {
		position: absolute;
		left: 0;
		top: 0;
		width: 12px;
		height: 13px;
		background-image:url('images/handle.png');
		behavior: url('/Agente/js/iepngfix.htc');
		cursor:pointer;
		z-index: 9999;
		overflow: hidden;
	}		
	#short_grid_track {
		position: absolute;
		left: 248px;
		top: 0px;
		width: 12px;
		height: 415px;
		background-image:url('images/short_track.png');
		behavior: url('/Agente/js/iepngfix.htc');	
		/*background-repeat:repeat-y;*/
		/*background-color: red;*/
		clear: both;
	}
	
	#short_grid-up {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_up.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: red;*/
	}
	#short_grid-down {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_down.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: green;*/
	}
	/* ]]> */

.short_grid_row
{
	float: left;
	width: 241px;  
	height: 17px;			
	overflow: hidden;
	/*background-color: yellow;
	opacity:0.5;	
	filter: alpha(opacity = 50);*/
	color: #FFFFFF;
	border-bottom: 2px solid #003366;	
	display: block;
	margin-left: 4px;
}	

.short_grid_row div
{
	line-height: 17px;	
	font-size: 10px;
}	
	
.gruppo_grid_field_1
{
	float: left;
	width: 40px;		
	text-align: center;		
}

.gruppo_grid_field_2
{
	float: left;
	width: 201px;		
	text-align: left;	
}

#gruppo_header
{
	float: left;
	width: 260px;  
	height: 47px;			
	overflow: hidden;
	position: absolute;
	top: 23px;
	left: 147px; 
	background-image:url(images/gruppo_header.png);
	clear: both;
}

#gruppo_name
{
	float: left;
	width: 150px;  
	height: 20px;			
	overflow: hidden;
	position: absolute;
	top: 14px;
	left: 100px; 
	clear: both;	
	font-size: 11px;
	line-height: 20px;
	font-weight: bold;
	color: #000000;	
}

#gruppo_grid_bg
{
	float: left;
	position: absolute;
	top: 70px;
	left: 147px;
	width: 260px;
	height: 421px;
	overflow: hidden;
	background-color: #006DFF;
	clear: both;
}


#flash_container
{
	float: left;
	width: 869px;
	height: 469px;			
	overflow: hidden;
	position: absolute;
	top: 23px;
	clear: both;
}

#full_content_container
{
	float: left;
	width: 869px;
	height: 469px;			
	overflow: hidden;
	position: absolute;
	top: 23px;
	clear: both;
}

	#status_left
	{
		float: left;
		width: 580px;
		height: 469px;
		overflow: hidden;
		background-image:url('images/status_image.jpg');
		background-color: #9EC6DC;
	}
	#status_right
	{
		float: left;
		width: 289px;
		height: 469px;
		overflow: hidden;
		background-color: #9EC6DC;
	}	
		#status_bar
		{
			float: left;
			width: 143px;
			height: 415px;
			overflow: hidden;
			background-image:url('images/bar_background.png');
			position: relative;
		}		
			#status_flo
			{
				float: left;
				width: 143px;
				height: 94px;
				position: absolute;
				bottom: 0px;
				left: 0px;
				text-align: right;
				background-image:url('images/bar_flo.png');
				font-size: 14px;
				font-weight: bold;
				line-height: 20px;
				color: #FFFFFF;			
				behavior: url('/Agente/js/iepngfix.htc');		
			}
			
		#status_desc
		{
			float: left;
			width: 146px;
			height: 415px;
			overflow: hidden;
			background-color: #9EC6DC;
			text-align: center;
			visibility: hidden;
		}		
			.separator
			{
				width: 45px;
				height: 30px;
				margin: 0 auto;	
			}
		#status_bottom
		{
			float: left;
			width: 289px;
			height: 54px;
			overflow: hidden;
			background-color: #CADFE9;
			padding: 4px 4px 4px 4px;
			font-size: 11px;
			line-height: 12px;
			color: #000000;				
		}						