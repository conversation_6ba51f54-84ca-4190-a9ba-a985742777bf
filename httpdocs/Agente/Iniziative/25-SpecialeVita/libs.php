<?

function truncateString($string, $cutAt)
{
	$string = trim($string);
	$returnString = $string;
	
	
	
	//print "<!-- TEST(".$string."): ".strlen(trim($string))." -->";
	
	
	if(strlen($string) > $cutAt)
	{
		$returnString = substr($string, 0, $cutAt);

		$returnString .= '...';
	}

	return $returnString;
}



function check_var($var)
{
	if(strlen(trim($var)) > 0)
		return true;
	else
		return false;
}
?>