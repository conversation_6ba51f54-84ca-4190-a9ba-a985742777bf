<public:attach event="ondocumentready" onevent="CSSHover()" />
<script>
/**
 *	Whatever:hover - V3.00.081222
 *	--------------------------------------------------------
 *	Author  - <PERSON>, http://www.xs4all.nl/~peterned
 *	License - http://creativecommons.org/licenses/LGPL/2.1
 *	Packed  - http://dean.edwards.name/packer
 *
 *	howto: body { behavior:url("csshover3.htc"); }
 */
eval(function(p,a,c,k,e,r){e=function(c){return(c<a?'':e(parseInt(c/a)))+((c=c%a)>35?String.fromCharCode(c+29):c.toString(36))};if(!''.replace(/^/,String)){while(c--)r[e(c)]=k[c]||e(c);k=[function(e){return r[e]}];e=function(){return'\\w+'};c=1};while(c--)if(k[c])p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c]);return p}('r.R=(8(){4 f=/(^|\\s)((([^a]([^ ]+)?)|(a([^#.][^ ]+)+)):(C|D|E))/i,S=/(.*?)\\:(C|D|E)/i,T=/[^:]+:([a-z-]+).*/i,U=/(\\.([a-V-W-]+):[a-z]+)|(:[a-z]+)/1c,X=/\\.([a-V-W-]*Y(C|D|E))/i,Z=/1d (5|6|7)/i,10=/1e/i;4 g=\'1f-\';4 h={p:[],t:{},11:8(){n(!Z.F(1g.1h)&&!10.F(r.12.1i))u;4 a=r.12.1j,l=a.v;w(4 i=0;i<l;i++){3.G(a[i])}},G:8(a){n(a.H){I{4 b=a.H,l=b.v;w(4 i=0;i<l;i++){3.G(a.H[i])}}J(13){}}I{4 c=a.1k,l=c.v;w(4 j=0;j<l;j++){3.14(c[j],a)}}J(13){}},14:8(a,b){4 c=a.1l;n(f.F(c)){4 d=a.K.1m,L=S.15(c)[1],M=c.N(T,\'Y$1\'),O=c.N(U,\'.$2\'+M),o=X.15(O)[1];4 e=L+o;n(!3.t[e]){b.16(L,g+o+\':1n(R(3, "\'+M+\'", "\'+o+\'"))\');3.t[e]=17}b.16(O,d)}},18:8(a,b,c){4 d=g+c;n(a.K[d]){a.K[d]=q}n(!a.x)a.x=[];n(!a.x[c]){a.x[c]=17;4 e=19 P(a,b,c);3.p.1o(e)}u b},y:8(){I{4 l=3.p.v;w(4 i=0;i<l;i++){3.p[i].y()}3.p=[];3.t={}}J(e){}}};r.Q(\'1p\',8(){h.y()});4 k={1q:{9:\'1r\',m:\'1s\'},1t:{9:\'1u\',m:\'1v\'},1a:{9:\'1a\',m:\'1w\'}};8 P(a,b,c){3.A=a;3.B=b;4 d=19 1x(\'(^|\\\\s)\'+c+\'(\\\\s|$)\',\'g\');3.9=8(){a.o+=\' \'+c};3.m=8(){a.o=a.o.N(d,\' \')};a.Q(k[b].9,3.9);a.Q(k[b].m,3.m)}P.1y={y:8(){3.A.1b(k[3.B].9,3.9);3.A.1b(k[3.B].m,3.m);3.9=q;3.m=q;3.A=q;3.B=q}};u 8(a,b,c){n(a){u h.18(a,b,c)}1z{h.11()}}})();',62,98,'|||this|var||||function|activator|||||||||||||deactivator|if|className|elements|null|window||callbacks|return|length|for|csshover|unload||node|type|hover|active|focus|test|parseStylesheet|imports|try|catch|style|affected|pseudo|replace|newSelect|CSSHoverElement|attachEvent|CSSHover|REG_AFFECTED|REG_PSEUDO|REG_SELECT|z0|9_|REG_CLASS|on|REG_MSIE|REG_COMPAT|init|document|securityException|parseCSSRule|exec|addRule|true|patch|new|onfocus|detachEvent|gi|msie|backcompat|csh|navigator|userAgent|compatMode|styleSheets|rules|selectorText|cssText|expression|push|onbeforeunload|onhover|onmouseenter|onmouseleave|onactive|onmousedown|onmouseup|onblur|RegExp|prototype|else'.split('|'),0,{}));

</script>