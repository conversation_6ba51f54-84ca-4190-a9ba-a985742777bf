<? require_once("libs.php"); ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="DimensioneTop 2010 - Dettaglio Polizze" />
<link href="/Agente/Iniziative/35-DimensioneTopInv2010/css/site.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/Agente/js/site.js"></script>
<link href="/Agente/Iniziative/35-DimensioneTopInv2010/plugins/flexcroll/flexcrollstyles.css" rel="stylesheet" type="text/css" />
<script type='text/javascript' src="/Agente/Iniziative/35-DimensioneTopInv2010/plugins/flexcroll/flexcroll.js"></script>

<title>DimensioneTop 2010 - Dettaglio Polizze</title>
</head>
<body>
	<div id="center_frame_container">
		<div id="content_container">
			<div id="menu">
				<a href="/Agente/Iniziative/35-DimensioneTopInv2010/">
					<div id="menu_1" >Status
					</div>
				</a>
				<a href="/Agente/Iniziative/35-DimensioneTopInv2010/dettaglioPolizze">
					<div id="menu_2"  class="menu_sel" >Dettaglio Polizze
					</div>
				</a>
				<a href="RegolamDimTopInv2010.pdf">
					<div id="menu_6">Regolamento
					</div>
				</a>
			</div>
			<div id="dettaglio_polizze_header">
			<?
			if($prevPage)
			{
			?>
				<a href="?page=<?=$prevPage ?>">
					<div id="dp_page_back">
					</div>
				</a>
			<?
			}
			else
			{
			?>
				<div id="dp_page_back">
				</div>
			<?
			}

			if($nextPage)
			{
			?>
				<a href="?page=<?=$nextPage ?>">
					<div id="dp_page_forw">
					</div>
				</a>
			<?
			}
			else
			{
			?>
				<div id="dp_page_forw">
				</div>
			<?
			}
			?>
				<div id="dp_page_number"><span class="font_10_black"><?=$currentPage ?></span><span class="font_10_gray">/<?=$totalPages ?></span>
				</div>

				<a href="#">
					<div id="orderby_button" onclick="show_order_by(); return false;">Ordina l'elenco
					</div>
				</a>
				<a href="dettaglioPolizze-CSV">
					<div id="scarica_button">Scarica l'elenco in Excel
					</div>
				</a>
			</div>

			<div id="orderby_container">
				<div id="orderby_background">
				</div>
				<div id="orderby_form">
					<form name="orderby_form" method="post">
						<select name="orderbyField1" id="orderby_select_1">
							<option value=""></option>
							<?foreach($orderParams as$k=>$v) {?><option value="<?=$k?>"><?=$v?></option><?}?>
						</select>
						<div id="orderby_check_1_c" onclick="order_by_check('1', 'ASC')"><img src="" id="orderby_check_1_c_image" />
						</div>
						<div id="orderby_check_1_d" onclick="order_by_check('1', 'DESC')"><img src="" id="orderby_check_1_d_image" />
						</div>
						<input type="hidden" name="orderbyDir1" id="orderby_check_1" value="" />
						<select name="orderbyField2" id="orderby_select_2">
							<option value=""></option>
							<?foreach($orderParams as$k=>$v) {?><option value="<?=$k?>"><?=$v?></option><?}?>
						</select>
						<div id="orderby_check_2_c" onclick="order_by_check('2', 'ASC')"><img src="" id="orderby_check_2_c_image" />
						</div>
						<div id="orderby_check_2_d" onclick="order_by_check('2', 'DESC')"><img src="" id="orderby_check_2_d_image" />
						</div>
						<input type="hidden" name="orderbyDir2" id="orderby_check_2" value="" />
						<select name="orderbyField3" id="orderby_select_3">
							<option value=""></option>
							<?foreach($orderParams as$k=>$v) {?><option value="<?=$k?>"><?=$v?></option><?}?>
						</select>
						<div id="orderby_check_3_c" onclick="order_by_check('3', 'ASC')"><img src="" id="orderby_check_3_c_image" />
						</div>
						<div id="orderby_check_3_d" onclick="order_by_check('3', 'DESC')"><img src="" id="orderby_check_3_d_image" />
						</div>
						<input type="hidden" name="orderbyDir3" id="orderby_check_3" value="" />
						<select name="orderbyField4" id="orderby_select_4">
							<option value=""></option>
							<?foreach($orderParams as$k=>$v) {?><option value="<?=$k?>"><?=$v?></option><?}?>
						</select>
						<div id="orderby_check_4_c" onclick="order_by_check('4', 'ASC')"><img src="" id="orderby_check_4_c_image" />
						</div>
						<div id="orderby_check_4_d" onclick="order_by_check('4', 'DESC')"><img src="" id="orderby_check_4_d_image" />
						</div>
						<input type="hidden" name="orderbyDir4" id="orderby_check_4" value="" />
						<div id="orderby_no" onclick="hide_order_by();">Annulla
						</div>
						<div id="orderby_yes" onclick="document.orderby_form.submit();">Ok
						</div>
					</form>
					<script>
						hide_order_by();
						order_by_check('1', 'ASC')
						order_by_check('2', 'ASC')
						order_by_check('3', 'ASC')
						order_by_check('4', 'ASC')
					</script>
				</div>
			</div>

			<div id="fullpage_grid" class='flexcroll'>
			<?

			if($arrayPolizze)
			{
				foreach($arrayPolizze as $key => $value)
				{
				?>
					<div class="fullpage_grid_row">
						<div class="dp_grid_field_1"><?=check_var($value->codEsazione) ? truncateString($value->codEsazione, 8):"&nbsp;" ?>
						</div>
						<div class="dp_grid_field_2"><?=check_var($value->polizza) ? truncateString($value->polizza, 8):"&nbsp;" ?>
						</div>
						<div class="dp_grid_field_3"><?=check_var($value->dataEmissione) ? date('d/m/Y', strtotime($value->dataEmissione)):"&nbsp;" ?>
						</div>
						<div class="dp_grid_field_4"><?=check_var($value->nomeCliente) ? truncateString($value->nomeCliente, 23):"&nbsp;" ?>
						</div>
						<div class="dp_grid_field_5"><?=check_var($value->famigliaProdotto) ? truncateString($value->famigliaProdotto, 23):"&nbsp;" ?>
						</div>
						<div class="dp_grid_field_5b">&nbsp;
						</div>
						<div class="dp_grid_field_6"><?=check_var($value->premioComputabile) ? number_format($value->premioComputabile,2,',','.'):"&nbsp;" ?>
						</div>
					</div>

				<?
				}
			}

			?>
			</div>
		</div>
	</div>
</body>
</html><? echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>
