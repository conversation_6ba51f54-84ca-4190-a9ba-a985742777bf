var debug = 0; 
var myDelay = 5;
var myTimeout = 500;
var place_num = 0;
var place_timeout = 1000;
var place_max = 8; // seconds
var max_place_timeout = 500;

/* general effects*/
opacityEnter = {type: 'opacity', from:0, to:100, step: 1, delay:10}


function startEffects(in_place_num)
{
	
	myAlert("startEffects");
	//alert("DEBUG1: "+in_place_num);
	place_num = in_place_num;
	//alert("DEBUG2: "+myContracts);
	//showDiv('status_note');
	
	showDiv('info');
	showDiv('nb');

	place_timeout = Math.round((place_max/place_num)*1000);
	 
	//alert("timeout per piece: "+place_timeout+"  ( Math.round(("+place_max+"/"+place_num+")*1000) )" );
	
	if(max_place_timeout > 500)
		max_place_timeout = 500;
	
	
	
	setTimeout("effect1(0)", myTimeout);
}

function effect1(myCicle)
{
	//alert("debug: "+myCicle);
	if(place_num > myCicle && place_num < 51)
	{
		myCicle++;
		//alert("DEBUG: "+'status_fiore'+myCicle);
		showDiv('piece_'+myCicle);
		setTimeout("effect1("+myCicle+")", place_timeout);
	}
	else
	{
		effect2();
	}
}

function effect2()
{
	//alert("finito");

	showDiv('title');
	
	setTimeout(effect3, myTimeout);
}
function effect3()
{
	//alert("finito");

	showDiv('output_2_bg');
	
	setTimeout(effect4, myTimeout);
}
function effect4()
{
	//alert("finito");

	showDiv('output_2');
	
	setTimeout(effect5, myTimeout);
}

function effect5()
{
	//alert("finito");

	showDiv('output_3_bg');
	
	setTimeout(effect6, myTimeout);
}
function effect6()
{
	//alert("finito");

	showDiv('output_3');
	
	setTimeout(effect7, myTimeout);
}
function effect7()
{
	//alert("finito");

	showDiv('output_4_bg');
	
	setTimeout(effect8, myTimeout);
}
function effect8()
{
	//alert("finito");

	showDiv('output_4');
	
	setTimeout(effect9, myTimeout);
}
function effect9()
{
	//alert("finito");

	showDiv('output_1');
	
	//setTimeout(effect9, myTimeout);
}

/* general functions */
function myGetElementById(id_name)
{
	if (document.getElementById && !window.opera)
	{
	  var obj = document.getElementById(id_name);         
	}
	else if(document.all)
	{
	  var obj = document.all.id_name;
	}   	
	
	return obj;		
}

function showDiv(divName)
{
	myGetElementById(divName).style.display = 'inline';
}

function hideDiv(divName)
{
	myGetElementById(divName).style.display = 'none';
}

function getDiv(divName)
{
	return myGetElementById(divName);
}

function myAlert(myMex)
{
	if(debug)
		alert(myMex);	
}