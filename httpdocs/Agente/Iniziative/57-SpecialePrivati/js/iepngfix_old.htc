<public:component>
<public:attach event="onpropertychange" onevent="iePNGFix(0)" />

<script type="text/javascript">

// IE5.5+ PNG Alpha Fix v1.0
// (c) 2004-2008 <PERSON>bull http://www.twinhelix.com

// This is licensed under the GNU LGPL, version 2.1 or later.
// For details, see: http://creativecommons.org/licenses/LGPL/2.1/

 
// This must be a path to a blank image, relative to the HTML document(s).
// In production use I suggest '/images/blank.gif' or similar. That's all!
if (typeof blankImg == 'undefined') var blankImg = '/Agente/Iniziative/dimensionetop/images/blank.gif';



function filt(s, b)
{
 var f = 'DXImageTransform.Microsoft.AlphaImageLoader';
 var sM = (currentStyle.backgroundRepeat == 'no-repeat') ? 'crop' : 'scale';
 s = (s || '').replace(/\(/g, '%28').replace(/\)/g, '%29');

 if (s && !(/IMG|INPUT/.test(nodeName) && !b) &&
  currentStyle.width == 'auto' && currentStyle.height == 'auto')
 {
  style.width = offsetWidth + 'px';
  style.height = clientHeight + 'px';
  if (currentStyle.display == 'inline') style.display = 'inline-block';
 }

 if (filters[f])
 {
  filters[f].enabled = s ? true : false;
  if (s) with (filters[f]) { src = s }
 }
 else if (s) style.filter = 'progid:'+f+'(src="'+s+'",sizingMethod="' + sM + '")';
}

function iePNGFix(init)
{
 if (!/MSIE (5\.5|6)/.test(navigator.userAgent) || typeof filters == 'unknown') return;
 var evt = init ? { propertyName: 'src,background' } : event;
 var isSrc = /src/.test(evt.propertyName);
 var isBg = /background/.test(evt.propertyName);
 var isClass = !init &&
  ((this.className != this._png_class) && (this.className || this._png_class));
 if (!(isSrc || isBg || isClass)) return;
 this._png_class = this.className;
 var blank = blankImg.match(/([^\/]+)$/)[1];

 // Required for Whatever:hover support - erase any set BG if className changes.
 if (isClass && ((style.backgroundImage.indexOf('url(') == -1) ||
     (style.backgroundImage.indexOf(blank) > -1)))
 {
  setTimeout(function() { this.style.backgroundImage = '' }, 0);
  return;
 }

 if (isSrc && this.src && /IMG|INPUT/.test(nodeName))
 {
  if ((/\.png/i).test(src))
  {
   filt(src, 1);
   src = blankImg;
  }
  else if (src.indexOf(blank) == -1) filt();
 }

 var bgSrc = currentStyle.backgroundImage || style.backgroundImage;
 if ((bgSrc + this.src).indexOf(blank) == -1)
 {
  var bgPNG = bgSrc.match(/^url[("']+(.*\.png[^\)"']*)[\)"']+[^\)]*$/i);

  if (bgPNG)
  {
   style.backgroundImage = 'url("' + blankImg + '")';
   filt(bgPNG[1], 0);
   // Unclickable elements inside PNG backgrounds.
   var tags = ['a', 'input', 'select', 'textarea', 'iframe', 'object'],
    t = tags.length, tFix = [];
   while (t--)
   {
    var elms = all.tags(tags[t]), e = elms.length;
    while (e--) tFix.push(elms[e]);
   }
   var t = tFix.length;
   if (t && (/relative|absolute/i).test(currentStyle.position))
    alert('IEPNGFix: Children of positioned element are unclickable:\n\n<' +
     nodeName + (id && ' id=' + id) + '>');
    while (t--)
     if (!(/relative|absolute/i).test(tFix[t].currentStyle.position))
      tFix[t].style.position = 'relative';
  }
  else filt();
 }
}

iePNGFix(1);

</script>
</public:component>
