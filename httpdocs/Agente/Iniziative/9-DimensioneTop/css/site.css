/*CSS Document*/
BODY
{
	margin: 0px;
	padding: 0px;
	background-color: #FFFFFF;
/*
background-attachment:scroll;
background-image:url(/Agente/Iniziative/7-ContaPunti/images/sfondo.gif);
background-position:center top;
background-repeat:repeat-x;
*/
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/csshover3.htc);
}
IMG
{
	display: block;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
}
.img_text
{
	display: inline;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
}
OBJECT, EMBED
{
	display: block;
	margin: 0px;
	padding: 0px;
	border: 0px solid #000000;
}
HTML, BODY
{
	height: 100%;
}
DIV
{
	font-family: Arial;
	font-size: 12px;
}
/*BASE STYLE*/
UL
{
	margin-top: 0px;
	margin-bottom: 0px;
}
LI
{
	margin-top: 0px;
	margin-bottom: 0px;
}
SELECT
{
	font-family: Arial;
	font-size: 12px;
}
.float_none
{
	float: none;
}
TABLE
{
	border: 0px solid #000000;
	padding: 0px;
	margin: 0px;
}
TR
{
	border: 0px solid #000000;
	padding: 0px;
	margin: 0px;
}
TD
{
	border: 0px solid #000000;
	padding: 5px 10px;
	margin: 0px;
}
.clear
{
	clear: left;
}
.float_left
{
	float: left;
}
.centered
{
	display: block;
	margin: 0 auto;
}
A:link
{
	color: #FFFFFF;
	text-decoration: none;
}
A:visited
{
	color: #FFFFFF;
	text-decoration: none;
}
A:active
{
	color: #FFFFFF;
	text-decoration: none;
}
A:hover
{
	color: #FFFFFF;
	font-weight: bold;
}
.intro_link:link
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:visited
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:active
{
	color: #81B39B;
	text-decoration: none;
}
.intro_link:hover
{
	color: #FFFFFF;
	font-weight: bold;
}
.spacer_5
{
	height: 5px;
}
.spacer_10
{
	height: 10px;
}
.spacer_20
{
	height: 20px;
}
.spacer_30
{
	height: 30px;
}
/*FONTS*/
.yellow
{
	color: #FFC200;
}
.white_bold
{
	color: #FFF;
	font-weight: bold;
}
.font_18
{
	font-size: 18px;
}
.black_16_bold
{
	font-size: 16px;
	font-weight: bold;
	color: #000;
}
/*MAIN TEMPLATE*/
#contents
{
	float: left;
	width: 869px;
	height: 492px;
	background-color: #FFFFFF;
	border-top: 8px solid #E7E7E7;
	border-bottom: 8px solid #E7E7E7;
	border-left: 8px solid #E7E7E7;
	border-right: 8px solid #E7E7E7;
	overflow: hidden;
}
#menu
{
	float: left;
	position: relative;
	width: 869px;
	height: 23px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/bkg_menu.png);
	background-repeat: repeat-x;
	clear: left;
	overflow: hidden;
}
#center
{
	float: left;
	position: relative;
	width: 869px;
	height: 469px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/bkg_status.png);
	background-repeat: repeat-x;
	clear: left;
	overflow: hidden;
}
#menu_1
{
	float: left;
	position: absolute;
	top: 0px;
	left: 0px;
	width: 53px;
	height: 23px;
	line-height: 23px;
	font-size: 11px;
	color: #FFFFFF;
	text-align: center;
}
#menu_2
{
	float: left;
	position: absolute;
	top: 0px;
	left: 53px;
	width: 98px;
	height: 23px;
	line-height: 23px;
	font-size: 11px;
	color: #FFFFFF;
	text-align: center;
}
#menu_6
{
	float: left;
	position: absolute;
	top: 0px;
	left: 784px;
	width: 85px;
	height: 23px;
	line-height: 23px;
	font-size: 11px;
	color: #FFFFFF;
	text-align: center;
}
#menu_1, #menu_2, #menu_6
{
	cursor: pointer;
}
#menu_1:hover, #menu_2:hover, #menu_6:hover
{
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/bkg_menu_roll.png);
	background-repeat: repeat-x;
	font-weight: bold;
}
.menu_sel
{
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/bkg_menu_roll.png);
	background-repeat: repeat-x;
	font-weight: bold;
}
/*STATUS*/
#milioni_title
{
	float: left;
	width: 80px;
	height: 13px;
	position: absolute;
	top: 63px;
	left: 148px;
	font-size: 13px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_0
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 198px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_10
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 313px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_20
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 433px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_30
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 553px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_40
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 673px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#milioni_50
{
	float: left;
	width: 80px;
	height: 17px;
	position: absolute;
	top: 61px;
	left: 793px;
	font-size: 16px;
	color: #C7E1DE;
	font-weight: bold;
}
#bonus_10
{
	float: left;
	width: 119px;
	height: 20px;
	position: absolute;
	top: 39px;
	left: 203px;
	font-size: 13px;
	color: #A8D0CC;
	font-weight: bold;
	text-align: center;
}
#bonus_20
{
	float: left;
	width: 119px;
	height: 20px;
	position: absolute;
	top: 39px;
	left: 323px;
	font-size: 13px;
	color: #A8D0CC;
	font-weight: bold;
	text-align: center;
}
#bonus_30
{
	float: left;
	width: 119px;
	height: 20px;
	position: absolute;
	top: 39px;
	left: 443px;
	font-size: 13px;
	color: #A8D0CC;
	font-weight: bold;
	text-align: center;
}
#bonus_40
{
	float: left;
	width: 119px;
	height: 20px;
	position: absolute;
	top: 39px;
	left: 563px;
	font-size: 13px;
	color: #A8D0CC;
	font-weight: bold;
	text-align: center;
}
#bonus_50
{
	float: left;
	width: 119px;
	height: 20px;
	position: absolute;
	top: 39px;
	left: 683px;
	font-size: 13px;
	color: #A8D0CC;
	font-weight: bold;
	text-align: center;
}
#baloon_1
{
	float: left;
	width: 103px;
	height: 20px;
	position: absolute;
	top: 45px;
	left: 13px;
	font-size: 16px;
	color: #196183;
	font-weight: bold;
	text-align: right;
	padding-right: 3px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/baloon_1.png);
	line-height: 20px;
	display: none;
	background-color: #89C1BD;
}
#status_bar
{
	float: left;
	width: 0px;
	height: 20px;
	position: absolute;
	top: 62px;
	left: 202px;
	font-size: 16px;
	color: #196183;
	font-weight: bold;
	text-align: center;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/status_bar.png);
	line-height: 20px;
	background-position: right;
}
#raccolta_agenzia
{
	float: left;
	width: 119px;
	position: absolute;
	top: 98px;
	left: 13px;
	font-size: 12px;
	color: #196183;
	font-weight: bold;
	text-align: left;
	display: none;
	background-color: #89C1BD;
}
#baloon_2
{
	float: left;
	width: 106px;
	height: 54px;
	position: absolute;
	top: 135px;
	left: 13px;
	font-size: 11px;
	color: #196183;
	font-weight: bold;
	text-align: center;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/baloon_2.png);
	padding-top: 4px;
	display: none;
	overflow: hidden;
	background-color: #89C1BD;
}
.target_ok
{
	line-height: 12px;
}
#status_note
{
	float: left;
	width: 155px;
	position: absolute;
	top: 387px;
	left: 13px;
	font-size: 11px;
	color: #000000;
	text-align: left;
	display: none;
	background-color: #89C1BD;
}
#baloon_3
{
	float: left;
	width: 90px;
	height: 58px;
	position: absolute;
	top: 200px;
	left: 13px;
	font-size: 16px;
	color: #196183;
	font-weight: bold;
	text-align: right;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/baloon_3.png);
	line-height: 85px;
	padding-right: 16px;
	display: none;
	overflow: hidden;
	background-color: #89C1BD;
}
#pork_2
{
	float: left;
	width: 66px;
	height: 78px;
	position: absolute;
	top: 270px;
	left: 13px;
	display: none;
}
#pork_2_bkg_sad
{
	float: left;
	width: 66px;
	height: 78px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/pork_2_sad.png);
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
}

#pork_2_bkg_happy
{
	float: left;
	width: 66px;
	height: 78px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/pork_2_happy.png);
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
}
/*
#pork_1
{
	float: left;
	width: 66px;
	height: 102px;
	position: absolute;
	top: 366px;
	left: 41px;
	font-size: 16px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/pork_1.png);
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
}
*/
#pork_baloon
{
	float: left;
	width: 112px;
	height: 53px;
	position: absolute;
	top: 286px;
	left: 72px;
	overflow: hidden;
	display: none;
}
#pork_baloon_bkg
{
	float: left;
	width: 112px;
	height: 53px;
	position: relative;
	top: 0px;
	left: 0px;
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/pork_baloon.png);
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
}
#pork_baloon_value
{
	float: left;
	width: 62px;
	height: 20px;
	position: relative;
	top: -23px;
	left: 42px;
	font-size: 16px;
	color: #000;
	font-weight: bold;
	text-align: right;
	line-height: 20px;
	background-color: #FFF;
}
#pork_container
{
	float: left;
	width: 158px;
	height: 469px;
	position: absolute;
	top: 0;
	left: 125px;
	display: none;
}
#pork_contents
{
	float: left;
	width: 158px;
	height: 469px;
	position: relative;
	top: 0;
	left: 0;
	/*[disabled]background-color:#FF0000;*/
}
#ci_text
{
	float: left;
	width: 138px;
	height: 16px;
	position: absolute;
	top: 6px;
	left: 10px;
	font-size: 13px;
	text-align: center;
	color: #FFFFFF;
	display: none;
	background-color: #89C1BD;
}
#ci_box
{
	float: left;
	width: 112px;
	height: 18px;
	position: absolute;
	top: 24px;
	left: 23px;
	background-color: #FEFE00;
	font-size: 13px;
	text-align: center;
	color: #FFFFFF;
	display: none;
}
#eur_space
{
	float: left;
	width: 16px;
	height: 18px;
	line-height: 18px;
	color: #196183;
	font-size: 13px;
	font-weight: bold;
}
#money_space
{
	float: left;
	width: 93px;
	height: 18px;
	text-align: right;
	line-height: 18px;
	color: #196183;
	font-size: 13px;
	font-weight: bold;
}
#center_line
{
	float: left;
	width: 6px;
	height: 304px;
	position: absolute;
	top: 88px;
	left: 75px;
	background-color: #427D99;
}
#pork_1
{
	float: left;
	width: 66px;
	height: 102px;
	position: absolute;
	top: 366px;
	left: 44px;
	font-size: 16px;
	/*
	background-image: url(/Agente/Iniziative/9-DimensioneTop/images/pork_1.png);
	behavior: url(/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc);
	*/
	z-index: 500;
}
#yellow_line
{
	float: left;
	width: 2px;
	height: 348px;
	position: absolute;
	top: 42px;
	left: 77px;
	background-color: #FED600;
	display: none;
	z-index: 999;
}
#dati_aggiornati
{
	float: left;
	width: 130px;
	height: 13px;
	position: absolute;
	top: 68px;
	left: 14px;
	font-size: 11px;
	color: #000403;
	background-color: #89C1BD;
	display: none;
}


/* DETTAGLIO POLIZZE  */

#center_frame_container
{
	float: left;
	width: 884px;
	height: 507px;			
	overflow: hidden;
	position: relative;
	background-color: #E6E7E7;	
	border-left: 1px solid #E6E7E7;
	border-top: 1px solid #E6E7E7;
}

#content_container
{
	float: left;
	width: 868px;
	height: 491px;		
	top: 8px;
	left: 8px;	
	overflow: hidden;
	position: relative;
	background-color: #8AC2BE;
}

#dettaglio_polizze_header
{
	float: left;
	width: 868px;
	height: 62px;			
	overflow: hidden;
	position: relative;
	background-image:url('/Agente/Iniziative/9-DimensioneTop/images/dettaglio_polizze_header.png');
}	
	#dp_page_back
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 73px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);	*/
		cursor:pointer;		
	}	
	#dp_page_forw
	{
		float: left;
		width: 16px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 157px; 
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);	*/
		cursor:pointer;	
	}	
	#dp_page_number
	{
		float: left;
		width: 68px;
		height: 16px;			
		overflow: hidden;
		position: absolute;
		top: 4px;
		left: 89px; 
		/*background-color: green;
		opacity:0.5;	
		filter: alpha(opacity = 50);		*/
		line-height: 16px;
		font-size: 10px;
		color: #000000;
		text-align: center;	
	}


#orderby_container
{
	float: left;
	width: 315px;
	height: 275px;

	position: absolute;
	top: 20px;
	left: 400px;	

	z-index: 9999;	
	/*background-image:url('images/orderby_background.png');		*/
	
	/*behavior: url('/Agente/js/iepngfix.htc');	*/
	/*background-repeat: no-repeat;*/
	/*background-color: red;
	opacity:0.5;	
	filter: alpha(opacity = 50);*/

}

	#orderby_background
	{
		float: left;
		width: 315px;
		height: 268px;
		z-index: 9999;	
		background-image:url('/Agente/Iniziative/9-DimensioneTop/images/orderby_background.png');	
		
		behavior: url('/Agente/js/iepngfix.htc');
		/*background-repeat: no-repeat;*/
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);*/
	
	}
	
	#orderby_form
	{
		float: left;
		width: 315px;
		height: 268px;
		z-index: 9999;	
		/*background-image:url('/Agente/images/orderby_background.png');		*/
		
		/*behavior: url('/Agente/js/iepngfix.htc');	*/
		/*background-repeat: no-repeat;*/
		/*background-color: red;
		opacity:0.5;	
		filter: alpha(opacity = 50);*/
	
	}
		#orderby_select_1
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 36px;
			left: 25px;		
			z-index: 9999;
		}
		#orderby_select_2
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 85px;
			left: 25px;		
			z-index: 9999;		
		}
		#orderby_select_3
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 130px;
			left: 25px;		
			z-index: 9999;		
		}
		#orderby_select_4
		{
			float: left;
			width: 160px;
			height: 18px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 178px;
			left: 25px;		
			z-index: 9999;		
		}	
		
		#orderby_no
		{
			float: left;
			width: 44px;
			height: 16px;		
			/*background-color: red;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 217px;
			left: 23px;		
			z-index: 9999;		
			cursor:pointer;
			line-height: 17px;
			padding: 0 0 0 20px;
		}
		#orderby_yes
		{
			float: left;
			width: 37px;
			height: 16px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			position: absolute;
			top: 217px;
			left: 237px;		
			z-index: 9999;		
			cursor:pointer;
			line-height: 17px;
			padding: 0 0 0 6px;
		}				
	
		#orderby_no:hover
		{
			font-weight: bold;	
		}
		#orderby_yes:hover
		{
			font-weight: bold;	
		}	
	
		#orderby_check_1_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 26px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_1_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 44px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		
		#orderby_check_2_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);*/	
			position: absolute;
			top: 73px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_2_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 91px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		
		#orderby_check_3_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 120px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_3_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 138px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		
		#orderby_check_4_c
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_checked.png);	*/
			position: absolute;
			top: 168px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}
		#orderby_check_4_d
		{
			float: left;
			width: 12px;
			height: 12px;		
			/*background-color: green;
			opacity:0.5;	
			filter: alpha(opacity = 50);*/
			/*background-image:url(images/checkbox_unchecked.png);	*/
			position: absolute;
			top: 186px;
			left: 197px;		
			z-index: 9999;	
			cursor:pointer;	
		}	
		

#orderby_button
{
	float: left;
	width: 93px;
	height: 16px;			
	overflow: hidden;
	position: absolute;
	top: 4px;
	left: 582px; 
	/*background-color: green;
	opacity:0.5;	
	filter: alpha(opacity = 50);	*/
	line-height: 16px;
	font-size: 11px;
	color: #818181;
	cursor:pointer;
	padding: 0 0 0 5px;
}	
#scarica_button
{
	float: left;
	width: 141px;
	height: 16px;			
	overflow: hidden;
	position: absolute;
	top: 4px;
	left: 695px; 
	/*background-color: green;
	opacity:0.5;	
	filter: alpha(opacity = 50);		*/
	line-height: 16px;
	font-size: 11px;
	color: #818181;
	cursor:pointer;
	padding: 0 0 0 5px;
}	

#orderby_button:hover, #scarica_button:hover
{
	color: #000000;	
	font-weight: normal;
}		

		
/* FULLPAGE GRID */

	/* <![CDATA[ */
	#fullpage_grid {
		position: absolute;
		top: 85px;
		left: 10px;
		width: 831px;
		clip: rect(0px 831px 380px 0px);
		overflow: hidden;
		padding-bottom: 40px;
		clear: both;
	}
		
	#fullpage_grid p:first-child {
		margin-top: 0;
	}
	#fullpage_grid_handle {
		position: absolute;
		left: 0;
		top: 0;
		width: 24px;
		height: 24px;
		background-image:url('/Agente/Iniziative/9-DimensioneTop/images/handle.png');
		behavior: url('/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc');
		cursor:pointer;
	}	
	#fullpage_grid_track {
		position: absolute;
		left: 842px;
		top: 85px;
		width: 24px;
		height: 380px;
		background-image:url('/Agente/Iniziative/9-DimensioneTop/images/track.png');
		behavior: url('/Agente/Iniziative/9-DimensioneTop/js/iepngfix.htc');	
		/*background-repeat:repeat-y;*/
		/*background-color: red;*/
		clear: both;
	}
	
	#fullpage_grid-up {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_up.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: red;*/
	}
	#fullpage_grid-down {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 0px;
		height: 0px;
		/*background-image:url(images/track_down.png);
		behavior: url(js/iepngfix.htc);*/
		/*background-color: green;*/
	}
	/* ]]> */

.fullpage_grid_row
{
	float: left;
	width: 831px;  
	height: 17px;			
	overflow: hidden;
	/*background-color: yellow;
	opacity:0.5;	
	filter: alpha(opacity = 50);*/
	color: #000000;
	border-bottom: 2px solid #A9D1CD;	
	display: block;
}	

.fullpage_grid_row div
{
	line-height: 17px;	
	font-size: 10px;
}	
	
.dp_grid_field_1
{
	float: left;
	width: 65px;		
	text-align: center;			
}

.dp_grid_field_2
{
	float: left;
	width: 125px;		
	text-align: center;			
}

.dp_grid_field_3
{
	float: left;
	width: 110px;		
	text-align: left;		
	padding: 0 0 0 25px;	
}

.dp_grid_field_4
{
	float: left;
	width: 250px;		
	text-align: left;	
	padding: 0 0 0 10px;	
}

.dp_grid_field_5
{
	float: left;
	width: 85px;		
	text-align: center;		
}

.dp_grid_field_6
{
	float: left;
	width: 144px;		
	text-align: right;	
	padding: 0 15px 0 0;		
}			