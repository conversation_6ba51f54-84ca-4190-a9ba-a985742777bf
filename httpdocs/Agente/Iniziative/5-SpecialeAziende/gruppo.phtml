<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Agendo v.0.1" />
<link href="site.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="/Agente/js/site.js"></script>
<title>Agendo v.0.1</title>
</head>
<body>
	<div id="center_frame_container">

		<script type="text/javascript" src="/Agente/js/prototype.js"></script>
		<script type="text/javascript" src="/Agente/js/slider.js"></script>
		<script type="text/javascript" src="/Agente/js/scroller.js"></script>
		<script type="text/javascript" language="javascript">
		// <![CDATA[
		$(document).observe("dom:loaded", function() {
			new Control.Scroller( 'short_grid', 395, false,'short_grid_handle', 'short_grid_track', {
				up: "short_grid-up",
				down: "short_grid-down"
			});
		});
		// ]]>
		</script>

		<div id="content_container">
			<div id="menu">
				<a href="/Agente/Iniziative/5-SpecialeAziende/">
					<div id="menu_1">Status
					</div>
				</a>
				<a href="/Agente/Iniziative/5-SpecialeAziende/dettaglioPolizze">
					<div id="menu_2">Dettaglio Polizze
					</div>
				</a>
				<a href="/Agente/Iniziative/5-SpecialeAziende/gruppo">
					<div id="menu_3_sel">Gruppo
					</div>
				</a>
				<a href="/Agente/Iniziative/5-SpecialeAziende/listaClientiTarget">
					<div id="menu_4">Lista Clienti Target
					</div>
				</a>
				<!--
				<a href="#">
					<div id="menu_5">Risorse
					</div>
				</a>
				-->
				<div id="menu_5_empty"> 
				</div>
				<div id="menu_6">
				</div>

				<div id="menu_6_a_empty">
				</div>				
				<!--
				<a href="/Agente/Iniziative/5-SpecialeAziende/playFlipper">
					<div id="menu_6_a">
					</div>
				</a>
				-->
				
				<div id="menu_6_b">
				</div>	
				<a href="/Agente/Iniziative/5-SpecialeAziende/<?= $Agenzia->id[0] == 'G' ? 'GA' : 'NT' ?>_AziendeRegolam.pdf">
					<div id="menu_7">Regolamento
					</div>
				</a>
			</div>
			<div id="gruppo_header">
					<?
					
					$gruppo_conv = Array(
															'1G'=>'<span class="font_12_brown_light_bold">1° Gruppo Nazionale</span>',
															'2G'=>'<span class="font_12_brown_light_bold">2° Gruppo Nazionale</span>',
															'RM'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Roma e Provincia</span>',
															'N'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Nord</span>',
															'CN'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Centro-Nord</span>',
															'NE'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Nord-Est</span>',
															'NO'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Nord-Ovest</span>',
															'C'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Centro</span>',
															'SO'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Sud-Ovest</span>',
															'SE'=>'<span class="font_12_black_bold">Gruppo</span> <span class="font_12_brown_light_bold">Area Sud-Est</span>',
															'1'=>'<span class="font_12_brown_light_bold">Gruppo 1</span>',
															'2'=>'<span class="font_12_brown_light_bold">Gruppo 2</span>',
															'3'=>'<span class="font_12_brown_light_bold">Gruppo 3</span>',
															'4'=>'<span class="font_12_brown_light_bold">Gruppo 4</span>'
															);
					
					?>
					<span class="font_12_black_bold">Composizione</span> <?=$gruppo_conv[$Status->gruppo] ?>
			</div>

			<div id="gruppo_grid_bg">
				<div id="short_grid_track"><div id="short_grid_handle"></div></div>
				<div id="short_grid-up"></div>
				<div id="short_grid-down"></div>
				<div id="short_grid">
					<div>
						<? 
						if($agenzieArray)
						{
							foreach($agenzieArray as $Agenzia){?>
							<div class="short_grid_row">
								<div class="gruppo_grid_field_1"><?=$Agenzia->getCodiceAgenzia()?>
								</div>
								<div class="gruppo_grid_field_2"><?=$Agenzia->getNome()?>
								</div>
							</div>
							<?}
						}
						?>
					</div>

				</div>

			</div>
		</div>
	</div>
</body>
</html><? // echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>