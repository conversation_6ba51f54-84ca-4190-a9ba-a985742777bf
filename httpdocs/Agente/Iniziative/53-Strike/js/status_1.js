var debug = 0; 
var myDelay = 15;
var myTimeout = 500;
var milions = 0;
var agency = 0;
var graph01BaseLeft = 158;
var graph01BaseTop = 288; /*top + height circa*/
var statusBarEnd = 0;
var bonus_highlight = '';


/* preload */
var birilli_h = 0;
var title_h = 0;
var ball_h = 0;
var status_h = 0;

var birilli_id = 'birilli_box';
var title_id = "title_box";
var ball_id = "ball";
var status_id = "status_box_1";
var ball_top = 469;
var ballEnd = 133;

var sprite_step = 1;
var sprite_step_done = 0;
var sprite_step_delay = 0;


var resize_val_from = 100;
var resize_val_to = 62;
var resize_running = 1;

var contratti = 0;
var contratti_max = 8;
var obj_done = 0;
//var resize_step = 1;
/**/

/* general effects*/
opacityEnter = {type: 'opacity', from:0, to:100, step: 1, delay:10}

function startEffects(contratti_in,obj_done_in)
{
	contratti = contratti_in;
	obj_done = obj_done_in;
	
	if(contratti >= contratti_max)
	{
		sprite_step = 1;
		sprite_step_delay = 500;		
	}
	else if(contratti > 0)
	{
		sprite_step = 2;
		sprite_step_delay = 500;
	}
	else
	{
		sprite_step = 12; 
		sprite_step_delay = 80;
	}	

	/* preload */
	
	birilli_h = getDiv(birilli_id).style.height;  
	title_h = getDiv(title_id).style.height;

	getDiv(birilli_id).style.height = '0px';
	getDiv(title_id).style.height = '0px';
	showDiv(birilli_id);
	showDiv(title_id);
	
	/**/
	//hideDiv(birilli_id);
	//hideDiv(title_id);
	//hideDiv(ball_id);
	setTimeout(effect1_1pre, myTimeout*5);
}

function effect1_1pre()
{
	hideDiv("preloader");
	
	setTimeout(effect1_1, myTimeout*2);
}
function effect1_1()
{
	getDiv(birilli_id).style.height = birilli_h;
	//showDiv(birilli_id);
	setTimeout(effect1_2, myTimeout*2);
}
function effect1_2()
{
	getDiv(title_id).style.height = title_h;
	//showDiv(title_id);
	setTimeout(effect2, myTimeout*2);
}
function effect2()
{
	//alert(ball_top+" / "+ballEnd);
	var moveBall = {type: 'top',from: ball_top, to: ballEnd, step: -3, delay: myDelay}
	

	var ball = getDiv(ball_id);
	

	$fx(ball).fxAdd(moveBall).fxRun(runSprite);
	
	syncResizeBall();

}



function syncResizeBall()
{
	if(resize_running)
	{
		var ball_top_value = Math.round((getDiv(ball_id).style.top.replace("px", "")));
		//alert("ball_top_value: "+ball_top_value);
		
		var new_resize = Math.round((ball_top-ball_top_value)/((ball_top-ballEnd)/(resize_val_from-resize_val_to)));
		
		//if(Math.round(resize_val_from-new_resize) < 80)
		//	alert("new_resize: "+Math.round(resize_val_from-new_resize));
		
		getDiv('ball_img').style.width = Math.round(resize_val_from-new_resize)+'%';
		
		setTimeout(syncResizeBall, myDelay*2);
	}
/*
	if(resize_val_from-resize_step >= resize_val_to)
	{
		resize_val_from = resize_val_from-resize_step;
		getDiv('ball_img').style.width = resize_val_from+'%';
		setTimeout(resizeBall, myDelay);
	}
	else
		getDiv('ball_img').style.width = resize_val_to+'%';
		
	*/
}





function runSprite()
{
	resize_running = 0;
	hideDiv(ball_id);
	//alert("init: "+sprite_step+" / "+sprite_step_done);
	if(sprite_step_done < sprite_step)
	{
		
		sprite_step_done++;
		//alert("step: "+getDiv(birilli_id).style.backgroundPosition+" /  step: "+sprite_step_done);

		
		getDiv(birilli_id).style.backgroundPosition = '-'+(Math.round(390*sprite_step_done))+'px 0'; //  '+Math.round((fondale_px/86)*100)+'
		
		setTimeout(runSprite, sprite_step_delay);
	}
	else
	{
		if(contratti >= contratti_max)
		{
			if(obj_done)
				getDiv(birilli_id).style.backgroundPosition = '-'+(Math.round(390*2))+'px 0';
			else
				getDiv(birilli_id).style.backgroundPosition = '-'+(Math.round(390*3))+'px 0';
		}
		
		effect4();
	}
}

function effect4()
{	
	moveStatus = {type: 'top',from: -214, to: 0, step: 2, delay: myDelay}
	$fx(getDiv('status_box_1')).fxAdd(moveStatus).fxRun(effect5);
}
function effect5()
{
	showDiv("status_data_container_1");

	setTimeout(effect6, myTimeout*2);
}
function effect6()
{
	showDiv("status_note");
}


/* general functions */
function myGetElementById(id_name)
{
	if (document.getElementById && !window.opera)
	{
	  var obj = document.getElementById(id_name);         
	}
	else if(document.all)
	{
	  var obj = document.all.id_name;
	}   	
	
	return obj;		
}

function showDiv(divName)
{
	myGetElementById(divName).style.display = 'inline';
}

function hideDiv(divName)
{
	myGetElementById(divName).style.display = 'none';
}

function getDiv(divName)
{
	return myGetElementById(divName);
}

function myAlert(myMex)
{
	if(debug)
		alert(myMex);	
}