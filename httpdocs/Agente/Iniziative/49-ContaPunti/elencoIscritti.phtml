<? require_once("libs.php"); ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "xhtml11.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta name="description" content="Contapunti v.0.2" />
<link href="css/site.css" rel="stylesheet" type="text/css" />
<link href="/Agente/Iniziative/49-ContaPunti/plugins/flexcroll/flexcrollstyles.css" rel="stylesheet" type="text/css" />
<script type='text/javascript' src="/Agente/Iniziative/49-ContaPunti/plugins/flexcroll/flexcroll.js"></script>
<title>Contapunti v.0.2</title>
</head>
<body>
	<div id="contents">
		<? include "menu.php"?>
		<div id="center">
			<div id="top_title_logo">
			</div>
			<div id="top_title_desc">

				I Collaboratori in gioco

			</div>

			<div id="a_grid_container">
				<div id="a_grid_container_title">
					<div class="a_grid_col_t_1"><br/>Nome<br/>/Ragione Sociale
					</div>
					<div class="a_grid_col_t_2"><br/><br/>Cognome
					</div>
					<div class="a_grid_col_t_3"><br/>Codice<br/>Esazione
					</div>
					<div class="a_grid_col_t_4"><br/>Codice<br/>RUI
					</div>
					<div class="a_grid_col_t_5"><br/><br/>User ID
					</div>
					<div class="a_grid_col_t_6"><br/><br/>Password
					</div>
				</div>
				<div id="a_grid_container_contents" class='flexcroll'>
					<?
					if(isset($arrayIntermediari))
					{
						foreach($arrayIntermediari as $key => $value)
						{
						?>
						<div class="a_grid_line_content">
							<div class="a_grid_col_1"><?=check_var($value->ragionesociale) ? truncateString($value->ragionesociale, 45) :"" ?> <?=check_var($value->nome) ? $value->nome:"&nbsp;" ?>
							</div>
							<div class="a_grid_col_2"><?=check_var($value->cognome) ? $value->cognome:"&nbsp;" ?>
							</div>
							<div class="a_grid_col_3"><?=check_var($value->codEsazione) ? $value->codEsazione:"&nbsp;" ?>
							</div>
							<div class="a_grid_col_4"><?=check_var($value->RUI) ? $value->RUI:"&nbsp;" ?>
							</div>
							<div class="a_grid_col_5"><?=check_var($value->login) ? $value->login:"&nbsp;" ?>
							</div>
							<div class="a_grid_col_6"><?=check_var($value->password) ? $value->password:"&nbsp;" ?>
							</div>
						</div>
						<?
						}
					}
					else
					{
						?>
						<!--
						<div class="a_grid_line_content">
							<div class="a_grid_col_1">TEST Nome
							</div>
							<div class="a_grid_col_2">TEST cognome
							</div>
							<div class="a_grid_col_3">12345
							</div>
							<div class="a_grid_col_4">E123456
							</div>
							<div class="a_grid_col_5">GA123456
							</div>
							<div class="a_grid_col_6">U123456
							</div>
						</div>
						-->
						<?
					}
					?>

				</div>
			</div>


		</div>
	</div>
</body>
</html><? echo '<!-- <pre>'; print_r(get_defined_vars()); echo '</pre> -->';?>