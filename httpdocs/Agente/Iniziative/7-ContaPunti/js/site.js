


function show_order_by()
{
	myGetElementById('orderby_container').style.display = 'inline';
}

function hide_order_by()
{
	myGetElementById('orderby_container').style.display = 'none';
}

function order_by_check(check_num, check_value)
{
	myGetElementById('orderby_check_'+check_num).value = check_value;
	
	if(check_value == 'ASC')
	{
		myGetElementById('orderby_check_'+check_num+'_c_image').src = 'images/checkbox_checked.png';
		myGetElementById('orderby_check_'+check_num+'_d_image').src = 'images/checkbox_unchecked.png';
	}
	else
	{
		myGetElementById('orderby_check_'+check_num+'_c_image').src = 'images/checkbox_unchecked.png';
		myGetElementById('orderby_check_'+check_num+'_d_image').src = 'images/checkbox_checked.png';		
	}
}

function myGetElementById(id_name)
{
	if (document.getElementById && !window.opera)
	{
	  var obj = document.getElementById(id_name);         
	}
	else if(document.all)
	{
	  var obj = document.all.id_name;
	}   	
	
	return obj;		
}