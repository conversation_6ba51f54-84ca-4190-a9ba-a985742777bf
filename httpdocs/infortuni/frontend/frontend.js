(function(angular, module){

    module.config(['$stateProvider', '$urlRouterProvider', function($stateProvider, $urlRouterProvider) {

        $stateProvider.state('root.frontend', {
            url: '/frontend',
            views: {
                'content@': {
                    templateUrl: 'frontend/frontend.html',
                    controller: "FrontendController"
                }
            },
            resolve: {
                getStatus: function($http) {
                    return $http({
                        method: 'GET',
                        url: '/api/apps/infortuni/status'
                    }).then(function (response) {
                        return response.data.data;
                    });
                },
                getLastUpdate: function($http) {
                    return $http({
                        method: 'GET',
                        url: '/api/apps/infortuni/last-update'
                    }).then(function (response) {
                        return response.data.data.lastUpdateLabel;
                    });
                }
            },
            data: {/*
                permissions: {
                    only: ['AGENTE'],
                    redirectTo: "../"
                }*/
            }
        });
    }]);

    module.controller('FrontendController', function($scope, $http, $rootScope, getStatus, getLastUpdate){

        $scope.lastUpdateLabel = getLastUpdate;
        $scope.agencyStatus = getStatus;
        $scope.progress = $scope.agencyStatus.pezzi ? $scope.agencyStatus.pezzi / $scope.agencyStatus.obiettivo * 100 : 0;
        $scope.objReached = $scope.agencyStatus.pezzi >= $scope.agencyStatus.obiettivo;

    });

})(angular, angular.module('infortuni.frontend', []));