(function(angular, module){

    module.service('$sessionHandler', ['$localStorage', function($localStorage) {
        return {
            save: function (type, userId, state, stateParams) {

                function addMinutesToTimestamp(minutes) {
                    var date = new Date();
                    // Set expire time by adding n minutes to current time
                    date.setMinutes( date.getMinutes() + minutes );
                    return date;
                }

                var obj = {
                    // User type
                    type: type,
                    // User ID
                    userId: userId,
                    // Current state
                    state: state,
                    // Convert date to string before stringify to prevent losing timezone
                    expireTime: JSON.stringify(addMinutesToTimestamp(5).toString()),
                    stateParams: null
                };

                // Since $state.params when empty passes an object with a property set to null instead of just null we need to check length of keys instead of just checking $state.params existence
                var stateParamsSize = Object.keys(stateParams).length;
                if(stateParamsSize > 1) {
                    obj.stateParams = stateParams;
                }

                $localStorage.setObject('infortuniCachedSession', obj);

                return obj;

            },
            get: function () {
                var cachedSession = $localStorage.getObject('infortuniCachedSession');

                // No cached session
                if (!cachedSession) {
                    return false;
                }

                // Cached session expired
                if (new Date() > new Date(cachedSession.expireTime)) {
                    return false;
                }

                return cachedSession;

            }
        }
    }]);

})(angular, angular.module('infortuni.session-handler', []));