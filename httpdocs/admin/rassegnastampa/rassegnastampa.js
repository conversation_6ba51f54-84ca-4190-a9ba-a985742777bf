(function(ng, module) {

	module.config(['$stateProvider', 'xng.$dataProvider', function($stateProvider, $dataProvider) {
		$stateProvider
			.state('admin-rassegnastampa', 		{ url: '/admin/rassegnastampa/',	templateUrl: 'admin/rassegnastampa/index.html',	controller: 'app.admin.rassegnastampa.IndexCtrl',	navbar: 'dashboard' })
			.state('admin-rassegnastampa-edit',	{ params: { id: null },				templateUrl: 'admin/rassegnastampa/edit.html',	controller: 'app.admin.rassegnastampa.EditCtrl',	navbar: 'dashboard' })
		;

		$dataProvider
			.store('RassegnaStampaStore', 'RassegnaStampa', { autoLoad: false })
		;
	}]);

	// IndexCtrl
	module.controller('app.admin.rassegnastampa.IndexCtrl', ['$scope', '$state', 'xng.$data', 'xng.ui.$growl', function($scope, $state, $data, $growl) {
		var RassegnaStampaRepository = $data.repository('RassegnaStampa');
		var RassegnaStampaStore = $data.store('RassegnaStampaStore');
		RassegnaStampaStore.orderBy('id','DESC');
		$scope.gridConfig = { autoLoad: true, store: RassegnaStampaStore };
		$scope.edit = function(RassegnaStampa) {
			$state.go('admin-rassegnastampa-edit', { id: RassegnaStampa.id });
		};
		$scope.remove = function(RassegnaStampa) {
			if(confirm('Sicuro?')==true) RassegnaStampaRepository.remove(RassegnaStampa).then(function(success) {
				if(success) {
					RassegnaStampaStore.reload();
					$growl.success('Rassegna Stampa eliminata', { icon: 'icon-checkmark', ttl: 2000 });
				}
				else $growl.error('Errore imprevisto, riprova!', { icon: 'icon-warning' });
			});
		};
	}]);

	// EditCtrl
	module.controller('app.admin.rassegnastampa.EditCtrl', ['$scope', '$state', '$stateParams', 'xng.$data', 'xng.ui.$growl', function($scope, $state, $stateParams, $data, $growl) {
		var RassegnaStampaRepository = $data.repository('RassegnaStampa');
		var RassegnaStampaStore = $data.store('RassegnaStampaStore');

		// $scope initialization
		// ------------------------------------------
		$scope.flowInit = {
			target: '/api/upload/rassegnastampa',
			singleFile: true,
			query: function() { return { date: $scope.RassegnaStampa.id } }
		};
		if($stateParams.id) {
			RassegnaStampaRepository.fetch($stateParams.id).then(function(RassegnaStampa) {
				$scope.new = false;
				$scope.RassegnaStampa = RassegnaStampa;
			});
		} else {
			$scope.new = true;
			$scope.RassegnaStampa = {};
		}

		// Form functions
		// ------------------------------------------
		$scope.abort = function() {
			$state.go('admin-rassegnastampa');
		};
		$scope.xngFormRassegnaStampa = {
			submitFn: function() {
				if(!$scope.new) return RassegnaStampaRepository.update($scope.RassegnaStampa);
				else return RassegnaStampaRepository.add($scope.RassegnaStampa);
			},
			successFn: function(RassegnaStampa) {
				if($scope.$flow) {
					$scope.RassegnaStampa = RassegnaStampa;
					$scope.$flow.on('complete', function() {
						RassegnaStampaStore.reload();
						$state.go('admin-rassegnastampa');
						$growl.success('Rassegna Stampa modificata', { icon: 'icon-checkmark', ttl: 2000 });
					});
					$scope.$flow.upload();
				} else {
					RassegnaStampaStore.reload();
					$state.go('admin-rassegnastampa');
					$growl.success('Rassegna Stampa modificata', { icon: 'icon-checkmark', ttl: 2000 });
				}
			},
			failureFn: function(data) {
				$growl.error('Dati non validi', { icon: 'icon-warning', ttl: 5000 });
			}
		};
		$scope.flowSubmit = function($flow) {
			$scope.$flow = $flow;
		//	if($scope.RassegnaStampa.id) $flow.upload();
		};
	}]);

})(angular, angular.module('app.admin.rassegnastampa', []));
