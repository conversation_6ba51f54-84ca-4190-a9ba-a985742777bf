// TODO: Load from .env file
function env(key, defaultValue = null) {
    return defaultValue
}

const mode = import.meta.env.MODE

//const isProduction = env("APP_ENV", "development") === "production"
export const isProduction = mode === 'production'

const baseUrl = env("BASE_URL", '/api');

if (!isProduction) {
    window.staticHost = 'static.vbox.portaleagendo.it';
}

const sessionCookieName = env("SESSION_COOKIE_NAME", "SESSION")
const xsrfCookieName = env("XSRF_COOKIE_NAME", "XSRF-TOKEN")

const config = {
    app: {
        isProduction,
        baseUrl,
        sessionCookieName,
        xsrfCookieName,
    }
}

export default config
