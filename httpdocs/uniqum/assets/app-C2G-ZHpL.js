(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();/**
* @vue/shared v3.5.6
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function la(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Ke={},Rr=[],fn=()=>{},d1=()=>!1,js=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),aa=e=>e.startsWith("onUpdate:"),wt=Object.assign,ua=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},f1=Object.prototype.hasOwnProperty,Be=(e,t)=>f1.call(e,t),we=Array.isArray,Mr=e=>zs(e)==="[object Map]",xd=e=>zs(e)==="[object Set]",$e=e=>typeof e=="function",nt=e=>typeof e=="string",Gn=e=>typeof e=="symbol",Je=e=>e!==null&&typeof e=="object",Ed=e=>(Je(e)||$e(e))&&$e(e.then)&&$e(e.catch),Cd=Object.prototype.toString,zs=e=>Cd.call(e),p1=e=>zs(e).slice(8,-1),Td=e=>zs(e)==="[object Object]",ca=e=>nt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,io=la(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Us=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},h1=/-(\w)/g,tn=Us(e=>e.replace(h1,(t,n)=>n?n.toUpperCase():"")),v1=/\B([A-Z])/g,Kn=Us(e=>e.replace(v1,"-$1").toLowerCase()),Zs=Us(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ii=Us(e=>e?`on${Zs(e)}`:""),qn=(e,t)=>!Object.is(e,t),Ni=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Od=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},m1=e=>{const t=parseFloat(e);return isNaN(t)?e:t},g1=e=>{const t=nt(e)?Number(e):NaN;return isNaN(t)?e:t};let cu;const Pd=()=>cu||(cu=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function hr(e){if(we(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=nt(r)?_1(r):hr(r);if(o)for(const s in o)t[s]=o[s]}return t}else if(nt(e)||Je(e))return e}const y1=/;(?![^(]*\))/g,b1=/:([^]+)/,w1=/\/\*[^]*?\*\//g;function _1(e){const t={};return e.replace(w1,"").split(y1).forEach(n=>{if(n){const r=n.split(b1);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function ot(e){let t="";if(nt(e))t=e;else if(we(e))for(let n=0;n<e.length;n++){const r=ot(e[n]);r&&(t+=r+" ")}else if(Je(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function S1(e){if(!e)return null;let{class:t,style:n}=e;return t&&!nt(t)&&(e.class=ot(t)),n&&(e.style=hr(n)),e}const $1="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",A1=la($1);function Rd(e){return!!e||e===""}const Md=e=>!!(e&&e.__v_isRef===!0),pe=e=>nt(e)?e:e==null?"":we(e)||Je(e)&&(e.toString===Cd||!$e(e.toString))?Md(e)?pe(e.value):JSON.stringify(e,kd,2):String(e),kd=(e,t)=>Md(t)?kd(e,t.value):Mr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[r,o],s)=>(n[Hi(r,s)+" =>"]=o,n),{})}:xd(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Hi(n))}:Gn(t)?Hi(t):Je(t)&&!we(t)&&!Td(t)?String(t):t,Hi=(e,t="")=>{var n;return Gn(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Tt;class Ld{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Tt,!t&&Tt&&(this.index=(Tt.scopes||(Tt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Tt;try{return Tt=this,t()}finally{Tt=n}}}on(){Tt=this}off(){Tt=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const o=this.parent.scopes.pop();o&&o!==this&&(this.parent.scopes[this.index]=o,o.index=this.index)}this.parent=void 0,this._active=!1}}}function Id(e){return new Ld(e)}function Nd(){return Tt}function x1(e,t=!1){Tt&&Tt.cleanups.push(e)}let Ge;const Fi=new WeakSet;class Hd{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Tt&&Tt.active&&Tt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Fi.has(this)&&(Fi.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Vd(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,du(this),Dd(this);const t=Ge,n=Qt;Ge=this,Qt=!0;try{return this.fn()}finally{Bd(this),Ge=t,Qt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)pa(t);this.deps=this.depsTail=void 0,du(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Fi.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){hl(this)&&this.run()}get dirty(){return hl(this)}}let Fd=0,lo;function Vd(e){e.flags|=8,e.next=lo,lo=e}function da(){Fd++}function fa(){if(--Fd>0)return;let e;for(;lo;){let t=lo;for(lo=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(r){e||(e=r)}t=n}}if(e)throw e}function Dd(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Bd(e){let t,n=e.depsTail,r=n;for(;r;){const o=r.prevDep;r.version===-1?(r===n&&(n=o),pa(r),E1(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=o}e.deps=t,e.depsTail=n}function hl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(jd(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function jd(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===_o))return;e.globalVersion=_o;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!hl(e)){e.flags&=-3;return}const n=Ge,r=Qt;Ge=e,Qt=!0;try{Dd(e);const o=e.fn(e._value);(t.version===0||qn(o,e._value))&&(e._value=o,t.version++)}catch(o){throw t.version++,o}finally{Ge=n,Qt=r,Bd(e),e.flags&=-3}}function pa(e){const{dep:t,prevSub:n,nextSub:r}=e;if(n&&(n.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=n,e.nextSub=void 0),t.subs===e&&(t.subs=n),!t.subs&&t.computed){t.computed.flags&=-5;for(let o=t.computed.deps;o;o=o.nextDep)pa(o)}}function E1(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Qt=!0;const zd=[];function Yn(){zd.push(Qt),Qt=!1}function Jn(){const e=zd.pop();Qt=e===void 0?!0:e}function du(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Ge;Ge=void 0;try{t()}finally{Ge=n}}}let _o=0;class C1{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ha{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0}track(t){if(!Ge||!Qt||Ge===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Ge)n=this.activeLink=new C1(Ge,this),Ge.deps?(n.prevDep=Ge.depsTail,Ge.depsTail.nextDep=n,Ge.depsTail=n):Ge.deps=Ge.depsTail=n,Ge.flags&4&&Ud(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const r=n.nextDep;r.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=r),n.prevDep=Ge.depsTail,n.nextDep=void 0,Ge.depsTail.nextDep=n,Ge.depsTail=n,Ge.deps===n&&(Ge.deps=r)}return n}trigger(t){this.version++,_o++,this.notify(t)}notify(t){da();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{fa()}}}function Ud(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let r=t.deps;r;r=r.nextDep)Ud(r)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}const Es=new WeakMap,vr=Symbol(""),vl=Symbol(""),So=Symbol("");function Et(e,t,n){if(Qt&&Ge){let r=Es.get(e);r||Es.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=new ha),o.track()}}function xn(e,t,n,r,o,s){const i=Es.get(e);if(!i){_o++;return}const l=a=>{a&&a.trigger()};if(da(),t==="clear")i.forEach(l);else{const a=we(e),u=a&&ca(n);if(a&&n==="length"){const c=Number(r);i.forEach((f,d)=>{(d==="length"||d===So||!Gn(d)&&d>=c)&&l(f)})}else switch(n!==void 0&&l(i.get(n)),u&&l(i.get(So)),t){case"add":a?u&&l(i.get("length")):(l(i.get(vr)),Mr(e)&&l(i.get(vl)));break;case"delete":a||(l(i.get(vr)),Mr(e)&&l(i.get(vl)));break;case"set":Mr(e)&&l(i.get(vr));break}}fa()}function T1(e,t){var n;return(n=Es.get(e))==null?void 0:n.get(t)}function xr(e){const t=xe(e);return t===e?t:(Et(t,"iterate",So),Kt(e)?t:t.map(At))}function qs(e){return Et(e=xe(e),"iterate",So),e}const O1={__proto__:null,[Symbol.iterator](){return Vi(this,Symbol.iterator,At)},concat(...e){return xr(this).concat(...e.map(t=>we(t)?xr(t):t))},entries(){return Vi(this,"entries",e=>(e[1]=At(e[1]),e))},every(e,t){return bn(this,"every",e,t,void 0,arguments)},filter(e,t){return bn(this,"filter",e,t,n=>n.map(At),arguments)},find(e,t){return bn(this,"find",e,t,At,arguments)},findIndex(e,t){return bn(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return bn(this,"findLast",e,t,At,arguments)},findLastIndex(e,t){return bn(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return bn(this,"forEach",e,t,void 0,arguments)},includes(...e){return Di(this,"includes",e)},indexOf(...e){return Di(this,"indexOf",e)},join(e){return xr(this).join(e)},lastIndexOf(...e){return Di(this,"lastIndexOf",e)},map(e,t){return bn(this,"map",e,t,void 0,arguments)},pop(){return Kr(this,"pop")},push(...e){return Kr(this,"push",e)},reduce(e,...t){return fu(this,"reduce",e,t)},reduceRight(e,...t){return fu(this,"reduceRight",e,t)},shift(){return Kr(this,"shift")},some(e,t){return bn(this,"some",e,t,void 0,arguments)},splice(...e){return Kr(this,"splice",e)},toReversed(){return xr(this).toReversed()},toSorted(e){return xr(this).toSorted(e)},toSpliced(...e){return xr(this).toSpliced(...e)},unshift(...e){return Kr(this,"unshift",e)},values(){return Vi(this,"values",At)}};function Vi(e,t,n){const r=qs(e),o=r[t]();return r!==e&&!Kt(e)&&(o._next=o.next,o.next=()=>{const s=o._next();return s.value&&(s.value=n(s.value)),s}),o}const P1=Array.prototype;function bn(e,t,n,r,o,s){const i=qs(e),l=i!==e&&!Kt(e),a=i[t];if(a!==P1[t]){const f=a.apply(e,s);return l?At(f):f}let u=n;i!==e&&(l?u=function(f,d){return n.call(this,At(f),d,e)}:n.length>2&&(u=function(f,d){return n.call(this,f,d,e)}));const c=a.call(i,u,r);return l&&o?o(c):c}function fu(e,t,n,r){const o=qs(e);let s=n;return o!==e&&(Kt(e)?n.length>3&&(s=function(i,l,a){return n.call(this,i,l,a,e)}):s=function(i,l,a){return n.call(this,i,At(l),a,e)}),o[t](s,...r)}function Di(e,t,n){const r=xe(e);Et(r,"iterate",So);const o=r[t](...n);return(o===-1||o===!1)&&ba(n[0])?(n[0]=xe(n[0]),r[t](...n)):o}function Kr(e,t,n=[]){Yn(),da();const r=xe(e)[t].apply(e,n);return fa(),Jn(),r}const R1=la("__proto__,__v_isRef,__isVue"),Zd=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Gn));function M1(e){Gn(e)||(e=String(e));const t=xe(this);return Et(t,"has",e),t.hasOwnProperty(e)}class qd{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,r){const o=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!o;if(n==="__v_isReadonly")return o;if(n==="__v_isShallow")return s;if(n==="__v_raw")return r===(o?s?Z1:Yd:s?Kd:Gd).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const i=we(t);if(!o){let a;if(i&&(a=O1[n]))return a;if(n==="hasOwnProperty")return M1}const l=Reflect.get(t,n,Ye(t)?t:r);return(Gn(n)?Zd.has(n):R1(n))||(o||Et(t,"get",n),s)?l:Ye(l)?i&&ca(n)?l:l.value:Je(l)?o?ga(l):_t(l):l}}class Wd extends qd{constructor(t=!1){super(!1,t)}set(t,n,r,o){let s=t[n];if(!this._isShallow){const a=br(s);if(!Kt(r)&&!br(r)&&(s=xe(s),r=xe(r)),!we(t)&&Ye(s)&&!Ye(r))return a?!1:(s.value=r,!0)}const i=we(t)&&ca(n)?Number(n)<t.length:Be(t,n),l=Reflect.set(t,n,r,Ye(t)?t:o);return t===xe(o)&&(i?qn(r,s)&&xn(t,"set",n,r):xn(t,"add",n,r)),l}deleteProperty(t,n){const r=Be(t,n);t[n];const o=Reflect.deleteProperty(t,n);return o&&r&&xn(t,"delete",n,void 0),o}has(t,n){const r=Reflect.has(t,n);return(!Gn(n)||!Zd.has(n))&&Et(t,"has",n),r}ownKeys(t){return Et(t,"iterate",we(t)?"length":vr),Reflect.ownKeys(t)}}class k1 extends qd{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const L1=new Wd,I1=new k1,N1=new Wd(!0);const va=e=>e,Ws=e=>Reflect.getPrototypeOf(e);function Qo(e,t,n=!1,r=!1){e=e.__v_raw;const o=xe(e),s=xe(t);n||(qn(t,s)&&Et(o,"get",t),Et(o,"get",s));const{has:i}=Ws(o),l=r?va:n?wa:At;if(i.call(o,t))return l(e.get(t));if(i.call(o,s))return l(e.get(s));e!==o&&e.get(t)}function es(e,t=!1){const n=this.__v_raw,r=xe(n),o=xe(e);return t||(qn(e,o)&&Et(r,"has",e),Et(r,"has",o)),e===o?n.has(e):n.has(e)||n.has(o)}function ts(e,t=!1){return e=e.__v_raw,!t&&Et(xe(e),"iterate",vr),Reflect.get(e,"size",e)}function pu(e,t=!1){!t&&!Kt(e)&&!br(e)&&(e=xe(e));const n=xe(this);return Ws(n).has.call(n,e)||(n.add(e),xn(n,"add",e,e)),this}function hu(e,t,n=!1){!n&&!Kt(t)&&!br(t)&&(t=xe(t));const r=xe(this),{has:o,get:s}=Ws(r);let i=o.call(r,e);i||(e=xe(e),i=o.call(r,e));const l=s.call(r,e);return r.set(e,t),i?qn(t,l)&&xn(r,"set",e,t):xn(r,"add",e,t),this}function vu(e){const t=xe(this),{has:n,get:r}=Ws(t);let o=n.call(t,e);o||(e=xe(e),o=n.call(t,e)),r&&r.call(t,e);const s=t.delete(e);return o&&xn(t,"delete",e,void 0),s}function mu(){const e=xe(this),t=e.size!==0,n=e.clear();return t&&xn(e,"clear",void 0,void 0),n}function ns(e,t){return function(r,o){const s=this,i=s.__v_raw,l=xe(i),a=t?va:e?wa:At;return!e&&Et(l,"iterate",vr),i.forEach((u,c)=>r.call(o,a(u),a(c),s))}}function rs(e,t,n){return function(...r){const o=this.__v_raw,s=xe(o),i=Mr(s),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=o[e](...r),c=n?va:t?wa:At;return!t&&Et(s,"iterate",a?vl:vr),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:l?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function Pn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function H1(){const e={get(s){return Qo(this,s)},get size(){return ts(this)},has:es,add:pu,set:hu,delete:vu,clear:mu,forEach:ns(!1,!1)},t={get(s){return Qo(this,s,!1,!0)},get size(){return ts(this)},has:es,add(s){return pu.call(this,s,!0)},set(s,i){return hu.call(this,s,i,!0)},delete:vu,clear:mu,forEach:ns(!1,!0)},n={get(s){return Qo(this,s,!0)},get size(){return ts(this,!0)},has(s){return es.call(this,s,!0)},add:Pn("add"),set:Pn("set"),delete:Pn("delete"),clear:Pn("clear"),forEach:ns(!0,!1)},r={get(s){return Qo(this,s,!0,!0)},get size(){return ts(this,!0)},has(s){return es.call(this,s,!0)},add:Pn("add"),set:Pn("set"),delete:Pn("delete"),clear:Pn("clear"),forEach:ns(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=rs(s,!1,!1),n[s]=rs(s,!0,!1),t[s]=rs(s,!1,!0),r[s]=rs(s,!0,!0)}),[e,n,t,r]}const[F1,V1,D1,B1]=H1();function ma(e,t){const n=t?e?B1:D1:e?V1:F1;return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(Be(n,o)&&o in r?n:r,o,s)}const j1={get:ma(!1,!1)},z1={get:ma(!1,!0)},U1={get:ma(!0,!1)};const Gd=new WeakMap,Kd=new WeakMap,Yd=new WeakMap,Z1=new WeakMap;function q1(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function W1(e){return e.__v_skip||!Object.isExtensible(e)?0:q1(p1(e))}function _t(e){return br(e)?e:ya(e,!1,L1,j1,Gd)}function Jd(e){return ya(e,!1,N1,z1,Kd)}function ga(e){return ya(e,!0,I1,U1,Yd)}function ya(e,t,n,r,o){if(!Je(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const i=W1(e);if(i===0)return e;const l=new Proxy(e,i===2?r:n);return o.set(e,l),l}function jn(e){return br(e)?jn(e.__v_raw):!!(e&&e.__v_isReactive)}function br(e){return!!(e&&e.__v_isReadonly)}function Kt(e){return!!(e&&e.__v_isShallow)}function ba(e){return e?!!e.__v_raw:!1}function xe(e){const t=e&&e.__v_raw;return t?xe(t):e}function Gs(e){return!Be(e,"__v_skip")&&Object.isExtensible(e)&&Od(e,"__v_skip",!0),e}const At=e=>Je(e)?_t(e):e,wa=e=>Je(e)?ga(e):e;function Ye(e){return e?e.__v_isRef===!0:!1}function F(e){return Xd(e,!1)}function _a(e){return Xd(e,!0)}function Xd(e,t){return Ye(e)?e:new G1(e,t)}class G1{constructor(t,n){this.dep=new ha,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:xe(t),this._value=n?t:At(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,r=this.__v_isShallow||Kt(t)||br(t);t=r?t:xe(t),qn(t,n)&&(this._rawValue=t,this._value=r?t:At(t),this.dep.trigger())}}function R(e){return Ye(e)?e.value:e}function Ne(e){return $e(e)?e():R(e)}const K1={get:(e,t,n)=>t==="__v_raw"?e:R(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Ye(o)&&!Ye(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Qd(e){return jn(e)?e:new Proxy(e,K1)}function Y1(e){const t=we(e)?new Array(e.length):{};for(const n in e)t[n]=ef(e,n);return t}class J1{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return T1(xe(this._object),this._key)}}class X1{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function lr(e,t,n){return Ye(e)?e:$e(e)?new X1(e):Je(e)&&arguments.length>1?ef(e,t,n):F(e)}function ef(e,t,n){const r=e[t];return Ye(r)?r:new J1(e,t,n)}class Q1{constructor(t,n,r){this.fn=t,this.setter=n,this._value=void 0,this.dep=new ha(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=_o-1,this.effect=this,this.__v_isReadonly=!n,this.isSSR=r}notify(){if(this.flags|=16,!(this.flags&8)&&Ge!==this)return Vd(this),!0}get value(){const t=this.dep.track();return jd(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function eh(e,t,n=!1){let r,o;return $e(e)?r=e:(r=e.get,o=e.set),new Q1(r,o,n)}const os={},Cs=new WeakMap;let sr;function th(e,t=!1,n=sr){if(n){let r=Cs.get(n);r||Cs.set(n,r=[]),r.push(e)}}function nh(e,t,n=Ke){const{immediate:r,deep:o,once:s,scheduler:i,augmentJob:l,call:a}=n,u=A=>o?A:Kt(A)||o===!1||o===0?$n(A,1):$n(A);let c,f,d,p,v=!1,m=!1;if(Ye(e)?(f=()=>e.value,v=Kt(e)):jn(e)?(f=()=>u(e),v=!0):we(e)?(m=!0,v=e.some(A=>jn(A)||Kt(A)),f=()=>e.map(A=>{if(Ye(A))return A.value;if(jn(A))return u(A);if($e(A))return a?a(A,2):A()})):$e(e)?t?f=a?()=>a(e,2):e:f=()=>{if(d){Yn();try{d()}finally{Jn()}}const A=sr;sr=c;try{return a?a(e,3,[p]):e(p)}finally{sr=A}}:f=fn,t&&o){const A=f,$=o===!0?1/0:o;f=()=>$n(A(),$)}const g=Nd(),b=()=>{c.stop(),g&&ua(g.effects,c)};if(s&&t){const A=t;t=(...$)=>{A(...$),b()}}let y=m?new Array(e.length).fill(os):os;const S=A=>{if(!(!(c.flags&1)||!c.dirty&&!A))if(t){const $=c.run();if(o||v||(m?$.some((I,H)=>qn(I,y[H])):qn($,y))){d&&d();const I=sr;sr=c;try{const H=[$,y===os?void 0:m&&y[0]===os?[]:y,p];a?a(t,3,H):t(...H),y=$}finally{sr=I}}}else c.run()};return l&&l(S),c=new Hd(f),c.scheduler=i?()=>i(S,!1):S,p=A=>th(A,!1,c),d=c.onStop=()=>{const A=Cs.get(c);if(A){if(a)a(A,4);else for(const $ of A)$();Cs.delete(c)}},t?r?S(!0):y=c.run():i?i(S.bind(null,!0),!0):c.run(),b.pause=c.pause.bind(c),b.resume=c.resume.bind(c),b.stop=b,b}function $n(e,t=1/0,n){if(t<=0||!Je(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Ye(e))$n(e.value,t,n);else if(we(e))for(let r=0;r<e.length;r++)$n(e[r],t,n);else if(xd(e)||Mr(e))e.forEach(r=>{$n(r,t,n)});else if(Td(e)){for(const r in e)$n(e[r],t,n);for(const r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&$n(e[r],t,n)}return e}/**
* @vue/runtime-core v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Do(e,t,n,r){try{return r?e(...r):e()}catch(o){Ks(o,t,n)}}function nn(e,t,n,r){if($e(e)){const o=Do(e,t,n,r);return o&&Ed(o)&&o.catch(s=>{Ks(s,t,n)}),o}if(we(e)){const o=[];for(let s=0;s<e.length;s++)o.push(nn(e[s],t,n,r));return o}}function Ks(e,t,n,r=!0){const o=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||Ke;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(s){Yn(),Do(s,null,10,[e,a,u]),Jn();return}}rh(e,n,o,r,i)}function rh(e,t,n,r=!0,o=!1){if(o)throw e;console.error(e)}let $o=!1,ml=!1;const Ot=[];let cn=0;const kr=[];let Hn=null,Cr=0;const tf=Promise.resolve();let Sa=null;function et(e){const t=Sa||tf;return e?t.then(this?e.bind(this):e):t}function oh(e){let t=$o?cn+1:0,n=Ot.length;for(;t<n;){const r=t+n>>>1,o=Ot[r],s=Ao(o);s<e||s===e&&o.flags&2?t=r+1:n=r}return t}function $a(e){if(!(e.flags&1)){const t=Ao(e),n=Ot[Ot.length-1];!n||!(e.flags&2)&&t>=Ao(n)?Ot.push(e):Ot.splice(oh(t),0,e),e.flags|=1,nf()}}function nf(){!$o&&!ml&&(ml=!0,Sa=tf.then(of))}function sh(e){we(e)?kr.push(...e):Hn&&e.id===-1?Hn.splice(Cr+1,0,e):e.flags&1||(kr.push(e),e.flags|=1),nf()}function gu(e,t,n=$o?cn+1:0){for(;n<Ot.length;n++){const r=Ot[n];if(r&&r.flags&2){if(e&&r.id!==e.uid)continue;Ot.splice(n,1),n--,r.flags&4&&(r.flags&=-2),r(),r.flags&=-2}}}function rf(e){if(kr.length){const t=[...new Set(kr)].sort((n,r)=>Ao(n)-Ao(r));if(kr.length=0,Hn){Hn.push(...t);return}for(Hn=t,Cr=0;Cr<Hn.length;Cr++){const n=Hn[Cr];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Hn=null,Cr=0}}const Ao=e=>e.id==null?e.flags&2?-1:1/0:e.id;function of(e){ml=!1,$o=!0;try{for(cn=0;cn<Ot.length;cn++){const t=Ot[cn];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Do(t,t.i,t.i?15:14),t.flags&=-2)}}finally{for(;cn<Ot.length;cn++){const t=Ot[cn];t&&(t.flags&=-2)}cn=0,Ot.length=0,rf(),$o=!1,Sa=null,(Ot.length||kr.length)&&of()}}let ft=null,Ys=null;function Ts(e){const t=ft;return ft=e,Ys=e&&e.type.__scopeId||null,t}function ih(e){Ys=e}function lh(){Ys=null}const ah=e=>X;function X(e,t=ft,n){if(!t||e._n)return e;const r=(...o)=>{r._d&&Pu(-1);const s=Ts(t);let i;try{i=e(...o)}finally{Ts(s),r._d&&Pu(1)}return i};return r._n=!0,r._c=!0,r._d=!0,r}function uh(e,t){if(ft===null)return e;const n=ni(ft),r=e.dirs||(e.dirs=[]);for(let o=0;o<t.length;o++){let[s,i,l,a=Ke]=t[o];s&&($e(s)&&(s={mounted:s,updated:s}),s.deep&&$n(i),r.push({dir:s,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function Qn(e,t,n,r){const o=e.dirs,s=t&&t.dirs;for(let i=0;i<o.length;i++){const l=o[i];s&&(l.oldValue=s[i].value);let a=l.dir[r];a&&(Yn(),nn(a,n,8,[e.el,l,e,t]),Jn())}}const sf=Symbol("_vte"),lf=e=>e.__isTeleport,ao=e=>e&&(e.disabled||e.disabled===""),ch=e=>e&&(e.defer||e.defer===""),yu=e=>typeof SVGElement<"u"&&e instanceof SVGElement,bu=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,gl=(e,t)=>{const n=e&&e.to;return nt(n)?t?t(n):null:n},dh={name:"Teleport",__isTeleport:!0,process(e,t,n,r,o,s,i,l,a,u){const{mc:c,pc:f,pbc:d,o:{insert:p,querySelector:v,createText:m,createComment:g}}=u,b=ao(t.props);let{shapeFlag:y,children:S,dynamicChildren:A}=t;if(e==null){const $=t.el=m(""),I=t.anchor=m("");p($,n,r),p(I,n,r);const H=(N,D)=>{y&16&&(o&&o.isCE&&(o.ce._teleportTarget=N),c(S,N,D,o,s,i,l,a))},U=()=>{const N=t.target=gl(t.props,v),D=af(N,t,m,p);N&&(i!=="svg"&&yu(N)?i="svg":i!=="mathml"&&bu(N)&&(i="mathml"),b||(H(N,D),hs(t)))};b&&(H(n,I),hs(t)),ch(t.props)?Lt(U,s):U()}else{t.el=e.el,t.targetStart=e.targetStart;const $=t.anchor=e.anchor,I=t.target=e.target,H=t.targetAnchor=e.targetAnchor,U=ao(e.props),N=U?n:I,D=U?$:H;if(i==="svg"||yu(I)?i="svg":(i==="mathml"||bu(I))&&(i="mathml"),A?(d(e.dynamicChildren,A,N,o,s,i,l),Ca(e,t,!0)):a||f(e,t,N,D,o,s,i,l,!1),b)U?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):ss(t,n,$,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const se=t.target=gl(t.props,v);se&&ss(t,se,null,u,0)}else U&&ss(t,I,H,u,1);hs(t)}},remove(e,t,n,{um:r,o:{remove:o}},s){const{shapeFlag:i,children:l,anchor:a,targetStart:u,targetAnchor:c,target:f,props:d}=e;if(f&&(o(u),o(c)),s&&o(a),i&16){const p=s||!ao(d);for(let v=0;v<l.length;v++){const m=l[v];r(m,t,n,p,!!m.dynamicChildren)}}},move:ss,hydrate:fh};function ss(e,t,n,{o:{insert:r},m:o},s=2){s===0&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:a,children:u,props:c}=e,f=s===2;if(f&&r(i,t,n),(!f||ao(c))&&a&16)for(let d=0;d<u.length;d++)o(u[d],t,n,2);f&&r(l,t,n)}function fh(e,t,n,r,o,s,{o:{nextSibling:i,parentNode:l,querySelector:a,insert:u,createText:c}},f){const d=t.target=gl(t.props,a);if(d){const p=d._lpa||d.firstChild;if(t.shapeFlag&16)if(ao(t.props))t.anchor=f(i(e),t,l(e),n,r,o,s),t.targetStart=p,t.targetAnchor=p&&i(p);else{t.anchor=i(e);let v=p;for(;v;){if(v&&v.nodeType===8){if(v.data==="teleport start anchor")t.targetStart=v;else if(v.data==="teleport anchor"){t.targetAnchor=v,d._lpa=t.targetAnchor&&i(t.targetAnchor);break}}v=i(v)}t.targetAnchor||af(d,t,c,u),f(p&&i(p),t,d,n,r,o,s)}hs(t)}return t.anchor&&i(t.anchor)}const ph=dh;function hs(e){const t=e.ctx;if(t&&t.ut){let n=e.targetStart;for(;n&&n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}function af(e,t,n,r){const o=t.targetStart=n(""),s=t.targetAnchor=n("");return o[sf]=s,e&&(r(o,e),r(s,e)),s}const Fn=Symbol("_leaveCb"),is=Symbol("_enterCb");function hh(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return We(()=>{e.isMounted=!0}),Aa(()=>{e.isUnmounting=!0}),e}const Wt=[Function,Array],uf={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Wt,onEnter:Wt,onAfterEnter:Wt,onEnterCancelled:Wt,onBeforeLeave:Wt,onLeave:Wt,onAfterLeave:Wt,onLeaveCancelled:Wt,onBeforeAppear:Wt,onAppear:Wt,onAfterAppear:Wt,onAppearCancelled:Wt},cf=e=>{const t=e.subTree;return t.component?cf(t.component):t},vh={name:"BaseTransition",props:uf,setup(e,{slots:t}){const n=$r(),r=hh();return()=>{const o=t.default&&pf(t.default(),!0);if(!o||!o.length)return;const s=df(o),i=xe(e),{mode:l}=i;if(r.isLeaving)return Bi(s);const a=wu(s);if(!a)return Bi(s);let u=yl(a,i,r,n,d=>u=d);a.type!==Pt&&xo(a,u);const c=n.subTree,f=c&&wu(c);if(f&&f.type!==Pt&&!ar(a,f)&&cf(n).type!==Pt){const d=yl(f,i,r,n);if(xo(f,d),l==="out-in"&&a.type!==Pt)return r.isLeaving=!0,d.afterLeave=()=>{r.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave},Bi(s);l==="in-out"&&a.type!==Pt&&(d.delayLeave=(p,v,m)=>{const g=ff(r,f);g[String(f.key)]=f,p[Fn]=()=>{v(),p[Fn]=void 0,delete u.delayedLeave},u.delayedLeave=m})}return s}}};function df(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Pt){t=n;break}}return t}const mh=vh;function ff(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function yl(e,t,n,r,o){const{appear:s,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:p,onAfterLeave:v,onLeaveCancelled:m,onBeforeAppear:g,onAppear:b,onAfterAppear:y,onAppearCancelled:S}=t,A=String(e.key),$=ff(n,e),I=(N,D)=>{N&&nn(N,r,9,D)},H=(N,D)=>{const se=D[1];I(N,D),we(N)?N.every(G=>G.length<=1)&&se():N.length<=1&&se()},U={mode:i,persisted:l,beforeEnter(N){let D=a;if(!n.isMounted)if(s)D=g||a;else return;N[Fn]&&N[Fn](!0);const se=$[A];se&&ar(e,se)&&se.el[Fn]&&se.el[Fn](),I(D,[N])},enter(N){let D=u,se=c,G=f;if(!n.isMounted)if(s)D=b||u,se=y||c,G=S||f;else return;let le=!1;const fe=N[is]=Se=>{le||(le=!0,Se?I(G,[N]):I(se,[N]),U.delayedLeave&&U.delayedLeave(),N[is]=void 0)};D?H(D,[N,fe]):fe()},leave(N,D){const se=String(e.key);if(N[is]&&N[is](!0),n.isUnmounting)return D();I(d,[N]);let G=!1;const le=N[Fn]=fe=>{G||(G=!0,D(),fe?I(m,[N]):I(v,[N]),N[Fn]=void 0,$[se]===e&&delete $[se])};$[se]=e,p?H(p,[N,le]):le()},clone(N){const D=yl(N,t,n,r,o);return o&&o(D),D}};return U}function Bi(e){if(Js(e))return e=vn(e),e.children=null,e}function wu(e){if(!Js(e))return lf(e.type)&&e.children?df(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&$e(n.default))return n.default()}}function xo(e,t){e.shapeFlag&6&&e.component?(e.transition=t,xo(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function pf(e,t=!1,n){let r=[],o=0;for(let s=0;s<e.length;s++){let i=e[s];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===he?(i.patchFlag&128&&o++,r=r.concat(pf(i.children,t,l))):(t||i.type!==Pt)&&r.push(l!=null?vn(i,{key:l}):i)}if(o>1)for(let s=0;s<r.length;s++)r[s].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function _e(e,t){return $e(e)?wt({name:e.name},t,{setup:e}):e}function gh(){const e=$r();if(e)return(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++}function hf(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function bl(e,t,n,r,o=!1){if(we(e)){e.forEach((v,m)=>bl(v,t&&(we(t)?t[m]:t),n,r,o));return}if(Lr(r)&&!o)return;const s=r.shapeFlag&4?ni(r.component):r.el,i=o?null:s,{i:l,r:a}=e,u=t&&t.r,c=l.refs===Ke?l.refs={}:l.refs,f=l.setupState,d=xe(f),p=f===Ke?()=>!1:v=>Be(d,v);if(u!=null&&u!==a&&(nt(u)?(c[u]=null,p(u)&&(f[u]=null)):Ye(u)&&(u.value=null)),$e(a))Do(a,l,12,[i,c]);else{const v=nt(a),m=Ye(a);if(v||m){const g=()=>{if(e.f){const b=v?p(a)?f[a]:c[a]:a.value;o?we(b)&&ua(b,s):we(b)?b.includes(s)||b.push(s):v?(c[a]=[s],p(a)&&(f[a]=c[a])):(a.value=[s],e.k&&(c[e.k]=a.value))}else v?(c[a]=i,p(a)&&(f[a]=i)):m&&(a.value=i,e.k&&(c[e.k]=i))};i?(g.id=-1,Lt(g,n)):g()}}}const Lr=e=>!!e.type.__asyncLoader,Js=e=>e.type.__isKeepAlive;function yh(e,t){vf(e,"a",t)}function bh(e,t){vf(e,"da",t)}function vf(e,t,n=mt){const r=e.__wdc||(e.__wdc=()=>{let o=n;for(;o;){if(o.isDeactivated)return;o=o.parent}return e()});if(Xs(t,r,n),n){let o=n.parent;for(;o&&o.parent;)Js(o.parent.vnode)&&wh(r,t,n,o),o=o.parent}}function wh(e,t,n,r){const o=Xs(t,e,r,!0);pt(()=>{ua(r[t],o)},n)}function Xs(e,t,n=mt,r=!1){if(n){const o=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{Yn();const l=Bo(n),a=nn(t,n,e,i);return l(),Jn(),a});return r?o.unshift(s):o.push(s),s}}const Tn=e=>(t,n=mt)=>{(!ti||e==="sp")&&Xs(e,(...r)=>t(...r),n)},_h=Tn("bm"),We=Tn("m"),Sh=Tn("bu"),$h=Tn("u"),Aa=Tn("bum"),pt=Tn("um"),Ah=Tn("sp"),xh=Tn("rtg"),Eh=Tn("rtc");function Ch(e,t=mt){Xs("ec",e,t)}const mf="components";function rn(e,t){return yf(mf,e,!0,t)||e}const gf=Symbol.for("v-ndc");function xa(e){return nt(e)?yf(mf,e,!1)||e:e||gf}function yf(e,t,n=!0,r=!1){const o=ft||mt;if(o){const s=o.type;{const l=fv(s,!1);if(l&&(l===t||l===tn(t)||l===Zs(tn(t))))return s}const i=_u(o[e]||s[e],t)||_u(o.appContext[e],t);return!i&&r?s:i}}function _u(e,t){return e&&(e[t]||e[tn(t)]||e[Zs(tn(t))])}function yt(e,t,n,r){let o;const s=n,i=we(e);if(i||nt(e)){const l=i&&jn(e);let a=!1;l&&(a=!Kt(e),e=qs(e)),o=new Array(e.length);for(let u=0,c=e.length;u<c;u++)o[u]=t(a?At(e[u]):e[u],u,void 0,s)}else if(typeof e=="number"){o=new Array(e);for(let l=0;l<e;l++)o[l]=t(l+1,l,void 0,s)}else if(Je(e))if(e[Symbol.iterator])o=Array.from(e,(l,a)=>t(l,a,void 0,s));else{const l=Object.keys(e);o=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];o[a]=t(e[c],c,a,s)}}else o=[];return o}function Ut(e,t,n={},r,o){if(ft.ce||ft.parent&&Lr(ft.parent)&&ft.parent.ce)return t!=="default"&&(n.name=t),x(),tt(he,null,[M("slot",n,r)],64);let s=e[t];s&&s._c&&(s._d=!1),x();const i=s&&bf(s(n)),l=tt(he,{key:(n.key||i&&i.key||`_${t}`)+""},i||[],i&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),s&&s._c&&(s._d=!0),l}function bf(e){return e.some(t=>Dr(t)?!(t.type===Pt||t.type===he&&!bf(t.children)):!0)?e:null}const wl=e=>e?Vf(e)?ni(e):wl(e.parent):null,uo=wt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>wl(e.parent),$root:e=>wl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>_f(e),$forceUpdate:e=>e.f||(e.f=()=>{$a(e.update)}),$nextTick:e=>e.n||(e.n=et.bind(e.proxy)),$watch:e=>Kh.bind(e)}),ji=(e,t)=>e!==Ke&&!e.__isScriptSetup&&Be(e,t),Th={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:r,data:o,props:s,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const p=i[t];if(p!==void 0)switch(p){case 1:return r[t];case 2:return o[t];case 4:return n[t];case 3:return s[t]}else{if(ji(r,t))return i[t]=1,r[t];if(o!==Ke&&Be(o,t))return i[t]=2,o[t];if((u=e.propsOptions[0])&&Be(u,t))return i[t]=3,s[t];if(n!==Ke&&Be(n,t))return i[t]=4,n[t];_l&&(i[t]=0)}}const c=uo[t];let f,d;if(c)return t==="$attrs"&&Et(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==Ke&&Be(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,Be(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:o,ctx:s}=e;return ji(o,t)?(o[t]=n,!0):r!==Ke&&Be(r,t)?(r[t]=n,!0):Be(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:o,propsOptions:s}},i){let l;return!!n[i]||e!==Ke&&Be(e,i)||ji(t,i)||(l=s[0])&&Be(l,i)||Be(r,i)||Be(uo,i)||Be(o.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Be(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Su(e){return we(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let _l=!0;function Oh(e){const t=_f(e),n=e.proxy,r=e.ctx;_l=!1,t.beforeCreate&&$u(t.beforeCreate,e,"bc");const{data:o,computed:s,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:p,updated:v,activated:m,deactivated:g,beforeDestroy:b,beforeUnmount:y,destroyed:S,unmounted:A,render:$,renderTracked:I,renderTriggered:H,errorCaptured:U,serverPrefetch:N,expose:D,inheritAttrs:se,components:G,directives:le,filters:fe}=t;if(u&&Ph(u,r,null),i)for(const K in i){const me=i[K];$e(me)&&(r[K]=me.bind(n))}if(o){const K=o.call(n,n);Je(K)&&(e.data=_t(K))}if(_l=!0,s)for(const K in s){const me=s[K],De=$e(me)?me.bind(n,n):$e(me.get)?me.get.bind(n,n):fn,ht=!$e(me)&&$e(me.set)?me.set.bind(n):fn,Ae=O({get:De,set:ht});Object.defineProperty(r,K,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:Re=>Ae.value=Re})}if(l)for(const K in l)wf(l[K],r,n,K);if(a){const K=$e(a)?a.call(n):a;Reflect.ownKeys(K).forEach(me=>{st(me,K[me])})}c&&$u(c,e,"c");function re(K,me){we(me)?me.forEach(De=>K(De.bind(n))):me&&K(me.bind(n))}if(re(_h,f),re(We,d),re(Sh,p),re($h,v),re(yh,m),re(bh,g),re(Ch,U),re(Eh,I),re(xh,H),re(Aa,y),re(pt,A),re(Ah,N),we(D))if(D.length){const K=e.exposed||(e.exposed={});D.forEach(me=>{Object.defineProperty(K,me,{get:()=>n[me],set:De=>n[me]=De})})}else e.exposed||(e.exposed={});$&&e.render===fn&&(e.render=$),se!=null&&(e.inheritAttrs=se),G&&(e.components=G),le&&(e.directives=le),N&&hf(e)}function Ph(e,t,n=fn){we(e)&&(e=Sl(e));for(const r in e){const o=e[r];let s;Je(o)?"default"in o?s=ae(o.from||r,o.default,!0):s=ae(o.from||r):s=ae(o),Ye(s)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[r]=s}}function $u(e,t,n){nn(we(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function wf(e,t,n,r){let o=r.includes(".")?kf(n,r):()=>n[r];if(nt(e)){const s=t[e];$e(s)&&ut(o,s)}else if($e(e))ut(o,e.bind(n));else if(Je(e))if(we(e))e.forEach(s=>wf(s,t,n,r));else{const s=$e(e.handler)?e.handler.bind(n):t[e.handler];$e(s)&&ut(o,s,e)}}function _f(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,l=s.get(t);let a;return l?a=l:!o.length&&!n&&!r?a=t:(a={},o.length&&o.forEach(u=>Os(a,u,i,!0)),Os(a,t,i)),Je(t)&&s.set(t,a),a}function Os(e,t,n,r=!1){const{mixins:o,extends:s}=t;s&&Os(e,s,n,!0),o&&o.forEach(i=>Os(e,i,n,!0));for(const i in t)if(!(r&&i==="expose")){const l=Rh[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Rh={data:Au,props:xu,emits:xu,methods:oo,computed:oo,beforeCreate:Ct,created:Ct,beforeMount:Ct,mounted:Ct,beforeUpdate:Ct,updated:Ct,beforeDestroy:Ct,beforeUnmount:Ct,destroyed:Ct,unmounted:Ct,activated:Ct,deactivated:Ct,errorCaptured:Ct,serverPrefetch:Ct,components:oo,directives:oo,watch:kh,provide:Au,inject:Mh};function Au(e,t){return t?e?function(){return wt($e(e)?e.call(this,this):e,$e(t)?t.call(this,this):t)}:t:e}function Mh(e,t){return oo(Sl(e),Sl(t))}function Sl(e){if(we(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ct(e,t){return e?[...new Set([].concat(e,t))]:t}function oo(e,t){return e?wt(Object.create(null),e,t):t}function xu(e,t){return e?we(e)&&we(t)?[...new Set([...e,...t])]:wt(Object.create(null),Su(e),Su(t??{})):t}function kh(e,t){if(!e)return t;if(!t)return e;const n=wt(Object.create(null),e);for(const r in t)n[r]=Ct(e[r],t[r]);return n}function Sf(){return{app:null,config:{isNativeTag:d1,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Lh=0;function Ih(e,t){return function(r,o=null){$e(r)||(r=wt({},r)),o!=null&&!Je(o)&&(o=null);const s=Sf(),i=new WeakSet,l=[];let a=!1;const u=s.app={_uid:Lh++,_component:r,_props:o,_container:null,_context:s,_instance:null,version:hv,get config(){return s.config},set config(c){},use(c,...f){return i.has(c)||(c&&$e(c.install)?(i.add(c),c.install(u,...f)):$e(c)&&(i.add(c),c(u,...f))),u},mixin(c){return s.mixins.includes(c)||s.mixins.push(c),u},component(c,f){return f?(s.components[c]=f,u):s.components[c]},directive(c,f){return f?(s.directives[c]=f,u):s.directives[c]},mount(c,f,d){if(!a){const p=u._ceVNode||M(r,o);return p.appContext=s,d===!0?d="svg":d===!1&&(d=void 0),e(p,c,d),a=!0,u._container=c,c.__vue_app__=u,ni(p.component)}},onUnmount(c){l.push(c)},unmount(){a&&(nn(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return s.provides[c]=f,u},runWithContext(c){const f=mr;mr=u;try{return c()}finally{mr=f}}};return u}}let mr=null;function st(e,t){if(mt){let n=mt.provides;const r=mt.parent&&mt.parent.provides;r===n&&(n=mt.provides=Object.create(r)),n[e]=t}}function ae(e,t,n=!1){const r=mt||ft;if(r||mr){const o=mr?mr._context.provides:r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(o&&e in o)return o[e];if(arguments.length>1)return n&&$e(t)?t.call(r&&r.proxy):t}}function Nh(){return!!(mt||ft||mr)}const $f={},Af=()=>Object.create($f),xf=e=>Object.getPrototypeOf(e)===$f;function Hh(e,t,n,r=!1){const o={},s=Af();e.propsDefaults=Object.create(null),Ef(e,t,o,s);for(const i in e.propsOptions[0])i in o||(o[i]=void 0);n?e.props=r?o:Jd(o):e.type.props?e.props=o:e.props=s,e.attrs=s}function Fh(e,t,n,r){const{props:o,attrs:s,vnode:{patchFlag:i}}=e,l=xe(o),[a]=e.propsOptions;let u=!1;if((r||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(Qs(e.emitsOptions,d))continue;const p=t[d];if(a)if(Be(s,d))p!==s[d]&&(s[d]=p,u=!0);else{const v=tn(d);o[v]=$l(a,l,v,p,e,!1)}else p!==s[d]&&(s[d]=p,u=!0)}}}else{Ef(e,t,o,s)&&(u=!0);let c;for(const f in l)(!t||!Be(t,f)&&((c=Kn(f))===f||!Be(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(o[f]=$l(a,l,f,void 0,e,!0)):delete o[f]);if(s!==l)for(const f in s)(!t||!Be(t,f))&&(delete s[f],u=!0)}u&&xn(e.attrs,"set","")}function Ef(e,t,n,r){const[o,s]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(io(a))continue;const u=t[a];let c;o&&Be(o,c=tn(a))?!s||!s.includes(c)?n[c]=u:(l||(l={}))[c]=u:Qs(e.emitsOptions,a)||(!(a in r)||u!==r[a])&&(r[a]=u,i=!0)}if(s){const a=xe(n),u=l||Ke;for(let c=0;c<s.length;c++){const f=s[c];n[f]=$l(o,a,f,u[f],e,!Be(u,f))}}return i}function $l(e,t,n,r,o,s){const i=e[n];if(i!=null){const l=Be(i,"default");if(l&&r===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&$e(a)){const{propsDefaults:u}=o;if(n in u)r=u[n];else{const c=Bo(o);r=u[n]=a.call(null,t),c()}}else r=a;o.ce&&o.ce._setProp(n,r)}i[0]&&(s&&!l?r=!1:i[1]&&(r===""||r===Kn(n))&&(r=!0))}return r}const Vh=new WeakMap;function Cf(e,t,n=!1){const r=n?Vh:t.propsCache,o=r.get(e);if(o)return o;const s=e.props,i={},l=[];let a=!1;if(!$e(e)){const c=f=>{a=!0;const[d,p]=Cf(f,t,!0);wt(i,d),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!s&&!a)return Je(e)&&r.set(e,Rr),Rr;if(we(s))for(let c=0;c<s.length;c++){const f=tn(s[c]);Eu(f)&&(i[f]=Ke)}else if(s)for(const c in s){const f=tn(c);if(Eu(f)){const d=s[c],p=i[f]=we(d)||$e(d)?{type:d}:wt({},d),v=p.type;let m=!1,g=!0;if(we(v))for(let b=0;b<v.length;++b){const y=v[b],S=$e(y)&&y.name;if(S==="Boolean"){m=!0;break}else S==="String"&&(g=!1)}else m=$e(v)&&v.name==="Boolean";p[0]=m,p[1]=g,(m||Be(p,"default"))&&l.push(f)}}const u=[i,l];return Je(e)&&r.set(e,u),u}function Eu(e){return e[0]!=="$"&&!io(e)}const Tf=e=>e[0]==="_"||e==="$stable",Ea=e=>we(e)?e.map(dn):[dn(e)],Dh=(e,t,n)=>{if(t._n)return t;const r=X((...o)=>Ea(t(...o)),n);return r._c=!1,r},Of=(e,t,n)=>{const r=e._ctx;for(const o in e){if(Tf(o))continue;const s=e[o];if($e(s))t[o]=Dh(o,s,r);else if(s!=null){const i=Ea(s);t[o]=()=>i}}},Pf=(e,t)=>{const n=Ea(t);e.slots.default=()=>n},Rf=(e,t,n)=>{for(const r in t)(n||r!=="_")&&(e[r]=t[r])},Bh=(e,t,n)=>{const r=e.slots=Af();if(e.vnode.shapeFlag&32){const o=t._;o?(Rf(r,t,n),n&&Od(r,"_",o,!0)):Of(t,r)}else t&&Pf(e,t)},jh=(e,t,n)=>{const{vnode:r,slots:o}=e;let s=!0,i=Ke;if(r.shapeFlag&32){const l=t._;l?n&&l===1?s=!1:Rf(o,t,n):(s=!t.$stable,Of(t,o)),i=t}else t&&(Pf(e,t),i={default:1});if(s)for(const l in o)!Tf(l)&&i[l]==null&&delete o[l]},Lt=nv;function zh(e){return Uh(e)}function Uh(e,t){const n=Pd();n.__VUE__=!0;const{insert:r,remove:o,patchProp:s,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:p=fn,insertStaticContent:v}=e,m=(w,_,P,V=null,z=null,B=null,te=void 0,J=null,ee=!!_.dynamicChildren)=>{if(w===_)return;w&&!ar(w,_)&&(V=k(w),Re(w,z,B,!0),w=null),_.patchFlag===-2&&(ee=!1,_.dynamicChildren=null);const{type:W,ref:ye,shapeFlag:ie}=_;switch(W){case ei:g(w,_,P,V);break;case Pt:b(w,_,P,V);break;case vs:w==null&&y(_,P,V,te);break;case he:G(w,_,P,V,z,B,te,J,ee);break;default:ie&1?$(w,_,P,V,z,B,te,J,ee):ie&6?le(w,_,P,V,z,B,te,J,ee):(ie&64||ie&128)&&W.process(w,_,P,V,z,B,te,J,ee,ue)}ye!=null&&z&&bl(ye,w&&w.ref,B,_||w,!_)},g=(w,_,P,V)=>{if(w==null)r(_.el=l(_.children),P,V);else{const z=_.el=w.el;_.children!==w.children&&u(z,_.children)}},b=(w,_,P,V)=>{w==null?r(_.el=a(_.children||""),P,V):_.el=w.el},y=(w,_,P,V)=>{[w.el,w.anchor]=v(w.children,_,P,V,w.el,w.anchor)},S=({el:w,anchor:_},P,V)=>{let z;for(;w&&w!==_;)z=d(w),r(w,P,V),w=z;r(_,P,V)},A=({el:w,anchor:_})=>{let P;for(;w&&w!==_;)P=d(w),o(w),w=P;o(_)},$=(w,_,P,V,z,B,te,J,ee)=>{_.type==="svg"?te="svg":_.type==="math"&&(te="mathml"),w==null?I(_,P,V,z,B,te,J,ee):N(w,_,z,B,te,J,ee)},I=(w,_,P,V,z,B,te,J)=>{let ee,W;const{props:ye,shapeFlag:ie,transition:ge,dirs:be}=w;if(ee=w.el=i(w.type,B,ye&&ye.is,ye),ie&8?c(ee,w.children):ie&16&&U(w.children,ee,null,V,z,zi(w,B),te,J),be&&Qn(w,null,V,"created"),H(ee,w,w.scopeId,te,V),ye){for(const Ze in ye)Ze!=="value"&&!io(Ze)&&s(ee,Ze,null,ye[Ze],B,V);"value"in ye&&s(ee,"value",null,ye.value,B),(W=ye.onVnodeBeforeMount)&&an(W,V,w)}be&&Qn(w,null,V,"beforeMount");const Le=Zh(z,ge);Le&&ge.beforeEnter(ee),r(ee,_,P),((W=ye&&ye.onVnodeMounted)||Le||be)&&Lt(()=>{W&&an(W,V,w),Le&&ge.enter(ee),be&&Qn(w,null,V,"mounted")},z)},H=(w,_,P,V,z)=>{if(P&&p(w,P),V)for(let B=0;B<V.length;B++)p(w,V[B]);if(z){let B=z.subTree;if(_===B||If(B.type)&&(B.ssContent===_||B.ssFallback===_)){const te=z.vnode;H(w,te,te.scopeId,te.slotScopeIds,z.parent)}}},U=(w,_,P,V,z,B,te,J,ee=0)=>{for(let W=ee;W<w.length;W++){const ye=w[W]=J?Vn(w[W]):dn(w[W]);m(null,ye,_,P,V,z,B,te,J)}},N=(w,_,P,V,z,B,te)=>{const J=_.el=w.el;let{patchFlag:ee,dynamicChildren:W,dirs:ye}=_;ee|=w.patchFlag&16;const ie=w.props||Ke,ge=_.props||Ke;let be;if(P&&er(P,!1),(be=ge.onVnodeBeforeUpdate)&&an(be,P,_,w),ye&&Qn(_,w,P,"beforeUpdate"),P&&er(P,!0),(ie.innerHTML&&ge.innerHTML==null||ie.textContent&&ge.textContent==null)&&c(J,""),W?D(w.dynamicChildren,W,J,P,V,zi(_,z),B):te||me(w,_,J,null,P,V,zi(_,z),B,!1),ee>0){if(ee&16)se(J,ie,ge,P,z);else if(ee&2&&ie.class!==ge.class&&s(J,"class",null,ge.class,z),ee&4&&s(J,"style",ie.style,ge.style,z),ee&8){const Le=_.dynamicProps;for(let Ze=0;Ze<Le.length;Ze++){const Ve=Le[Ze],C=ie[Ve],E=ge[Ve];(E!==C||Ve==="value")&&s(J,Ve,C,E,z,P)}}ee&1&&w.children!==_.children&&c(J,_.children)}else!te&&W==null&&se(J,ie,ge,P,z);((be=ge.onVnodeUpdated)||ye)&&Lt(()=>{be&&an(be,P,_,w),ye&&Qn(_,w,P,"updated")},V)},D=(w,_,P,V,z,B,te)=>{for(let J=0;J<_.length;J++){const ee=w[J],W=_[J],ye=ee.el&&(ee.type===he||!ar(ee,W)||ee.shapeFlag&70)?f(ee.el):P;m(ee,W,ye,null,V,z,B,te,!0)}},se=(w,_,P,V,z)=>{if(_!==P){if(_!==Ke)for(const B in _)!io(B)&&!(B in P)&&s(w,B,_[B],null,z,V);for(const B in P){if(io(B))continue;const te=P[B],J=_[B];te!==J&&B!=="value"&&s(w,B,J,te,z,V)}"value"in P&&s(w,"value",_.value,P.value,z)}},G=(w,_,P,V,z,B,te,J,ee)=>{const W=_.el=w?w.el:l(""),ye=_.anchor=w?w.anchor:l("");let{patchFlag:ie,dynamicChildren:ge,slotScopeIds:be}=_;be&&(J=J?J.concat(be):be),w==null?(r(W,P,V),r(ye,P,V),U(_.children||[],P,ye,z,B,te,J,ee)):ie>0&&ie&64&&ge&&w.dynamicChildren?(D(w.dynamicChildren,ge,P,z,B,te,J),(_.key!=null||z&&_===z.subTree)&&Ca(w,_,!0)):me(w,_,P,ye,z,B,te,J,ee)},le=(w,_,P,V,z,B,te,J,ee)=>{_.slotScopeIds=J,w==null?_.shapeFlag&512?z.ctx.activate(_,P,V,te,ee):fe(_,P,V,z,B,te,ee):Se(w,_,ee)},fe=(w,_,P,V,z,B,te)=>{const J=w.component=lv(w,V,z);if(Js(w)&&(J.ctx.renderer=ue),av(J,!1,te),J.asyncDep){if(z&&z.registerDep(J,re,te),!w.el){const ee=J.subTree=M(Pt);b(null,ee,_,P)}}else re(J,w,_,P,z,B,te)},Se=(w,_,P)=>{const V=_.component=w.component;if(ev(w,_,P))if(V.asyncDep&&!V.asyncResolved){K(V,_,P);return}else V.next=_,V.update();else _.el=w.el,V.vnode=_},re=(w,_,P,V,z,B,te)=>{const J=()=>{if(w.isMounted){let{next:ie,bu:ge,u:be,parent:Le,vnode:Ze}=w;{const ne=Mf(w);if(ne){ie&&(ie.el=Ze.el,K(w,ie,te)),ne.asyncDep.then(()=>{w.isUnmounted||J()});return}}let Ve=ie,C;er(w,!1),ie?(ie.el=Ze.el,K(w,ie,te)):ie=Ze,ge&&Ni(ge),(C=ie.props&&ie.props.onVnodeBeforeUpdate)&&an(C,Le,ie,Ze),er(w,!0);const E=Tu(w),j=w.subTree;w.subTree=E,m(j,E,f(j.el),k(j),w,z,B),ie.el=E.el,Ve===null&&tv(w,E.el),be&&Lt(be,z),(C=ie.props&&ie.props.onVnodeUpdated)&&Lt(()=>an(C,Le,ie,Ze),z)}else{let ie;const{el:ge,props:be}=_,{bm:Le,m:Ze,parent:Ve,root:C,type:E}=w,j=Lr(_);er(w,!1),Le&&Ni(Le),!j&&(ie=be&&be.onVnodeBeforeMount)&&an(ie,Ve,_),er(w,!0);{C.ce&&C.ce._injectChildStyle(E);const ne=w.subTree=Tu(w);m(null,ne,P,V,w,z,B),_.el=ne.el}if(Ze&&Lt(Ze,z),!j&&(ie=be&&be.onVnodeMounted)){const ne=_;Lt(()=>an(ie,Ve,ne),z)}(_.shapeFlag&256||Ve&&Lr(Ve.vnode)&&Ve.vnode.shapeFlag&256)&&w.a&&Lt(w.a,z),w.isMounted=!0,_=P=V=null}};w.scope.on();const ee=w.effect=new Hd(J);w.scope.off();const W=w.update=ee.run.bind(ee),ye=w.job=ee.runIfDirty.bind(ee);ye.i=w,ye.id=w.uid,ee.scheduler=()=>$a(ye),er(w,!0),W()},K=(w,_,P)=>{_.component=w;const V=w.vnode.props;w.vnode=_,w.next=null,Fh(w,_.props,V,P),jh(w,_.children,P),Yn(),gu(w),Jn()},me=(w,_,P,V,z,B,te,J,ee=!1)=>{const W=w&&w.children,ye=w?w.shapeFlag:0,ie=_.children,{patchFlag:ge,shapeFlag:be}=_;if(ge>0){if(ge&128){ht(W,ie,P,V,z,B,te,J,ee);return}else if(ge&256){De(W,ie,P,V,z,B,te,J,ee);return}}be&8?(ye&16&&Q(W,z,B),ie!==W&&c(P,ie)):ye&16?be&16?ht(W,ie,P,V,z,B,te,J,ee):Q(W,z,B,!0):(ye&8&&c(P,""),be&16&&U(ie,P,V,z,B,te,J,ee))},De=(w,_,P,V,z,B,te,J,ee)=>{w=w||Rr,_=_||Rr;const W=w.length,ye=_.length,ie=Math.min(W,ye);let ge;for(ge=0;ge<ie;ge++){const be=_[ge]=ee?Vn(_[ge]):dn(_[ge]);m(w[ge],be,P,null,z,B,te,J,ee)}W>ye?Q(w,z,B,!0,!1,ie):U(_,P,V,z,B,te,J,ee,ie)},ht=(w,_,P,V,z,B,te,J,ee)=>{let W=0;const ye=_.length;let ie=w.length-1,ge=ye-1;for(;W<=ie&&W<=ge;){const be=w[W],Le=_[W]=ee?Vn(_[W]):dn(_[W]);if(ar(be,Le))m(be,Le,P,null,z,B,te,J,ee);else break;W++}for(;W<=ie&&W<=ge;){const be=w[ie],Le=_[ge]=ee?Vn(_[ge]):dn(_[ge]);if(ar(be,Le))m(be,Le,P,null,z,B,te,J,ee);else break;ie--,ge--}if(W>ie){if(W<=ge){const be=ge+1,Le=be<ye?_[be].el:V;for(;W<=ge;)m(null,_[W]=ee?Vn(_[W]):dn(_[W]),P,Le,z,B,te,J,ee),W++}}else if(W>ge)for(;W<=ie;)Re(w[W],z,B,!0),W++;else{const be=W,Le=W,Ze=new Map;for(W=Le;W<=ge;W++){const de=_[W]=ee?Vn(_[W]):dn(_[W]);de.key!=null&&Ze.set(de.key,W)}let Ve,C=0;const E=ge-Le+1;let j=!1,ne=0;const ve=new Array(E);for(W=0;W<E;W++)ve[W]=0;for(W=be;W<=ie;W++){const de=w[W];if(C>=E){Re(de,z,B,!0);continue}let Ie;if(de.key!=null)Ie=Ze.get(de.key);else for(Ve=Le;Ve<=ge;Ve++)if(ve[Ve-Le]===0&&ar(de,_[Ve])){Ie=Ve;break}Ie===void 0?Re(de,z,B,!0):(ve[Ie-Le]=W+1,Ie>=ne?ne=Ie:j=!0,m(de,_[Ie],P,null,z,B,te,J,ee),C++)}const Ee=j?qh(ve):Rr;for(Ve=Ee.length-1,W=E-1;W>=0;W--){const de=Le+W,Ie=_[de],rt=de+1<ye?_[de+1].el:V;ve[W]===0?m(null,Ie,P,rt,z,B,te,J,ee):j&&(Ve<0||W!==Ee[Ve]?Ae(Ie,P,rt,2):Ve--)}}},Ae=(w,_,P,V,z=null)=>{const{el:B,type:te,transition:J,children:ee,shapeFlag:W}=w;if(W&6){Ae(w.component.subTree,_,P,V);return}if(W&128){w.suspense.move(_,P,V);return}if(W&64){te.move(w,_,P,ue);return}if(te===he){r(B,_,P);for(let ie=0;ie<ee.length;ie++)Ae(ee[ie],_,P,V);r(w.anchor,_,P);return}if(te===vs){S(w,_,P);return}if(V!==2&&W&1&&J)if(V===0)J.beforeEnter(B),r(B,_,P),Lt(()=>J.enter(B),z);else{const{leave:ie,delayLeave:ge,afterLeave:be}=J,Le=()=>r(B,_,P),Ze=()=>{ie(B,()=>{Le(),be&&be()})};ge?ge(B,Le,Ze):Ze()}else r(B,_,P)},Re=(w,_,P,V=!1,z=!1)=>{const{type:B,props:te,ref:J,children:ee,dynamicChildren:W,shapeFlag:ye,patchFlag:ie,dirs:ge,cacheIndex:be}=w;if(ie===-2&&(z=!1),J!=null&&bl(J,null,P,w,!0),be!=null&&(_.renderCache[be]=void 0),ye&256){_.ctx.deactivate(w);return}const Le=ye&1&&ge,Ze=!Lr(w);let Ve;if(Ze&&(Ve=te&&te.onVnodeBeforeUnmount)&&an(Ve,_,w),ye&6)Zt(w.component,P,V);else{if(ye&128){w.suspense.unmount(P,V);return}Le&&Qn(w,null,_,"beforeUnmount"),ye&64?w.type.remove(w,_,P,ue,V):W&&!W.hasOnce&&(B!==he||ie>0&&ie&64)?Q(W,_,P,!1,!0):(B===he&&ie&384||!z&&ye&16)&&Q(ee,_,P),V&&Xe(w)}(Ze&&(Ve=te&&te.onVnodeUnmounted)||Le)&&Lt(()=>{Ve&&an(Ve,_,w),Le&&Qn(w,null,_,"unmounted")},P)},Xe=w=>{const{type:_,el:P,anchor:V,transition:z}=w;if(_===he){Fe(P,V);return}if(_===vs){A(w);return}const B=()=>{o(P),z&&!z.persisted&&z.afterLeave&&z.afterLeave()};if(w.shapeFlag&1&&z&&!z.persisted){const{leave:te,delayLeave:J}=z,ee=()=>te(P,B);J?J(w.el,B,ee):ee()}else B()},Fe=(w,_)=>{let P;for(;w!==_;)P=d(w),o(w),w=P;o(_)},Zt=(w,_,P)=>{const{bum:V,scope:z,job:B,subTree:te,um:J,m:ee,a:W}=w;Cu(ee),Cu(W),V&&Ni(V),z.stop(),B&&(B.flags|=8,Re(te,w,_,P)),J&&Lt(J,_),Lt(()=>{w.isUnmounted=!0},_),_&&_.pendingBranch&&!_.isUnmounted&&w.asyncDep&&!w.asyncResolved&&w.suspenseId===_.pendingId&&(_.deps--,_.deps===0&&_.resolve())},Q=(w,_,P,V=!1,z=!1,B=0)=>{for(let te=B;te<w.length;te++)Re(w[te],_,P,V,z)},k=w=>{if(w.shapeFlag&6)return k(w.component.subTree);if(w.shapeFlag&128)return w.suspense.next();const _=d(w.anchor||w.el),P=_&&_[sf];return P?d(P):_};let Y=!1;const Z=(w,_,P)=>{w==null?_._vnode&&Re(_._vnode,null,null,!0):m(_._vnode||null,w,_,null,null,null,P),_._vnode=w,Y||(Y=!0,gu(),rf(),Y=!1)},ue={p:m,um:Re,m:Ae,r:Xe,mt:fe,mc:U,pc:me,pbc:D,n:k,o:e};return{render:Z,hydrate:void 0,createApp:Ih(Z)}}function zi({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function er({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Zh(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ca(e,t,n=!1){const r=e.children,o=t.children;if(we(r)&&we(o))for(let s=0;s<r.length;s++){const i=r[s];let l=o[s];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=o[s]=Vn(o[s]),l.el=i.el),!n&&l.patchFlag!==-2&&Ca(i,l)),l.type===ei&&(l.el=i.el)}}function qh(e){const t=e.slice(),n=[0];let r,o,s,i,l;const a=e.length;for(r=0;r<a;r++){const u=e[r];if(u!==0){if(o=n[n.length-1],e[o]<u){t[r]=o,n.push(r);continue}for(s=0,i=n.length-1;s<i;)l=s+i>>1,e[n[l]]<u?s=l+1:i=l;u<e[n[s]]&&(s>0&&(t[r]=n[s-1]),n[s]=r)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function Mf(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Mf(t)}function Cu(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Wh=Symbol.for("v-scx"),Gh=()=>ae(Wh);function it(e,t){return Ta(e,null,t)}function ut(e,t,n){return Ta(e,t,n)}function Ta(e,t,n=Ke){const{immediate:r,deep:o,flush:s,once:i}=n,l=wt({},n);let a;if(ti)if(s==="sync"){const d=Gh();a=d.__watcherHandles||(d.__watcherHandles=[])}else if(!t||r)l.once=!0;else{const d=()=>{};return d.stop=fn,d.resume=fn,d.pause=fn,d}const u=mt;l.call=(d,p,v)=>nn(d,u,p,v);let c=!1;s==="post"?l.scheduler=d=>{Lt(d,u&&u.suspense)}:s!=="sync"&&(c=!0,l.scheduler=(d,p)=>{p?d():$a(d)}),l.augmentJob=d=>{t&&(d.flags|=4),c&&(d.flags|=2,u&&(d.id=u.uid,d.i=u))};const f=nh(e,t,l);return a&&a.push(f),f}function Kh(e,t,n){const r=this.proxy,o=nt(e)?e.includes(".")?kf(r,e):()=>r[e]:e.bind(r,r);let s;$e(t)?s=t:(s=t.handler,n=t);const i=Bo(this),l=Ta(o,s.bind(r),n);return i(),l}function kf(e,t){const n=t.split(".");return()=>{let r=e;for(let o=0;o<n.length&&r;o++)r=r[n[o]];return r}}const Yh=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${tn(t)}Modifiers`]||e[`${Kn(t)}Modifiers`];function Jh(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||Ke;let o=n;const s=t.startsWith("update:"),i=s&&Yh(r,t.slice(7));i&&(i.trim&&(o=n.map(c=>nt(c)?c.trim():c)),i.number&&(o=n.map(m1)));let l,a=r[l=Ii(t)]||r[l=Ii(tn(t))];!a&&s&&(a=r[l=Ii(Kn(t))]),a&&nn(a,e,6,o);const u=r[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,nn(u,e,6,o)}}function Lf(e,t,n=!1){const r=t.emitsCache,o=r.get(e);if(o!==void 0)return o;const s=e.emits;let i={},l=!1;if(!$e(e)){const a=u=>{const c=Lf(u,t,!0);c&&(l=!0,wt(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!s&&!l?(Je(e)&&r.set(e,null),null):(we(s)?s.forEach(a=>i[a]=null):wt(i,s),Je(e)&&r.set(e,i),i)}function Qs(e,t){return!e||!js(t)?!1:(t=t.slice(2).replace(/Once$/,""),Be(e,t[0].toLowerCase()+t.slice(1))||Be(e,Kn(t))||Be(e,t))}function Tu(e){const{type:t,vnode:n,proxy:r,withProxy:o,propsOptions:[s],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:d,setupState:p,ctx:v,inheritAttrs:m}=e,g=Ts(e);let b,y;try{if(n.shapeFlag&4){const A=o||r,$=A;b=dn(u.call($,A,c,f,p,d,v)),y=l}else{const A=t;b=dn(A.length>1?A(f,{attrs:l,slots:i,emit:a}):A(f,null)),y=t.props?l:Xh(l)}}catch(A){co.length=0,Ks(A,e,1),b=M(Pt)}let S=b;if(y&&m!==!1){const A=Object.keys(y),{shapeFlag:$}=S;A.length&&$&7&&(s&&A.some(aa)&&(y=Qh(y,s)),S=vn(S,y,!1,!0))}return n.dirs&&(S=vn(S,null,!1,!0),S.dirs=S.dirs?S.dirs.concat(n.dirs):n.dirs),n.transition&&xo(S,n.transition),b=S,Ts(g),b}const Xh=e=>{let t;for(const n in e)(n==="class"||n==="style"||js(n))&&((t||(t={}))[n]=e[n]);return t},Qh=(e,t)=>{const n={};for(const r in e)(!aa(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function ev(e,t,n){const{props:r,children:o,component:s}=e,{props:i,children:l,patchFlag:a}=t,u=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?Ou(r,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(i[d]!==r[d]&&!Qs(u,d))return!0}}}else return(o||l)&&(!l||!l.$stable)?!0:r===i?!1:r?i?Ou(r,i,u):!0:!!i;return!1}function Ou(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const s=r[o];if(t[s]!==e[s]&&!Qs(n,s))return!0}return!1}function tv({vnode:e,parent:t},n){for(;t;){const r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const If=e=>e.__isSuspense;function nv(e,t){t&&t.pendingBranch?we(e)?t.effects.push(...e):t.effects.push(e):sh(e)}const he=Symbol.for("v-fgt"),ei=Symbol.for("v-txt"),Pt=Symbol.for("v-cmt"),vs=Symbol.for("v-stc"),co=[];let Bt=null;function x(e=!1){co.push(Bt=e?null:[])}function rv(){co.pop(),Bt=co[co.length-1]||null}let Eo=1;function Pu(e){Eo+=e,e<0&&Bt&&(Bt.hasOnce=!0)}function Nf(e){return e.dynamicChildren=Eo>0?Bt||Rr:null,rv(),Eo>0&&Bt&&Bt.push(e),e}function T(e,t,n,r,o,s){return Nf(h(e,t,n,r,o,s,!0))}function tt(e,t,n,r,o){return Nf(M(e,t,n,r,o,!0))}function Dr(e){return e?e.__v_isVNode===!0:!1}function ar(e,t){return e.type===t.type&&e.key===t.key}const Hf=({key:e})=>e??null,ms=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?nt(e)||Ye(e)||$e(e)?{i:ft,r:e,k:t,f:!!n}:e:null);function h(e,t=null,n=null,r=0,o=null,s=e===he?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hf(t),ref:t&&ms(t),scopeId:Ys,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:r,dynamicProps:o,dynamicChildren:null,appContext:null,ctx:ft};return l?(Oa(a,n),s&128&&e.normalize(a)):n&&(a.shapeFlag|=nt(n)?8:16),Eo>0&&!i&&Bt&&(a.patchFlag>0||s&6)&&a.patchFlag!==32&&Bt.push(a),a}const M=ov;function ov(e,t=null,n=null,r=0,o=null,s=!1){if((!e||e===gf)&&(e=Pt),Dr(e)){const l=vn(e,t,!0);return n&&Oa(l,n),Eo>0&&!s&&Bt&&(l.shapeFlag&6?Bt[Bt.indexOf(e)]=l:Bt.push(l)),l.patchFlag=-2,l}if(pv(e)&&(e=e.__vccOpts),t){t=Ff(t);let{class:l,style:a}=t;l&&!nt(l)&&(t.class=ot(l)),Je(a)&&(ba(a)&&!we(a)&&(a=wt({},a)),t.style=hr(a))}const i=nt(e)?1:If(e)?128:lf(e)?64:Je(e)?4:$e(e)?2:0;return h(e,t,n,r,o,i,s,!0)}function Ff(e){return e?ba(e)||xf(e)?wt({},e):e:null}function vn(e,t,n=!1,r=!1){const{props:o,ref:s,patchFlag:i,children:l,transition:a}=e,u=t?dt(o||{},t):o,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Hf(u),ref:t&&t.ref?n&&s?we(s)?s.concat(ms(t)):[s,ms(t)]:ms(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==he?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&vn(e.ssContent),ssFallback:e.ssFallback&&vn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&r&&xo(c,a.clone(c)),c}function oe(e=" ",t=0){return M(ei,null,e,t)}function Al(e,t){const n=M(vs,null,e);return n.staticCount=t,n}function ce(e="",t=!1){return t?(x(),tt(Pt,null,e)):M(Pt,null,e)}function dn(e){return e==null||typeof e=="boolean"?M(Pt):we(e)?M(he,null,e.slice()):typeof e=="object"?Vn(e):M(ei,null,String(e))}function Vn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:vn(e)}function Oa(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(we(t))n=16;else if(typeof t=="object")if(r&65){const o=t.default;o&&(o._c&&(o._d=!1),Oa(e,o()),o._c&&(o._d=!0));return}else{n=32;const o=t._;!o&&!xf(t)?t._ctx=ft:o===3&&ft&&(ft.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else $e(t)?(t={default:t,_ctx:ft},n=32):(t=String(t),r&64?(n=16,t=[oe(t)]):n=8);e.children=t,e.shapeFlag|=n}function dt(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const o in r)if(o==="class")t.class!==r.class&&(t.class=ot([t.class,r.class]));else if(o==="style")t.style=hr([t.style,r.style]);else if(js(o)){const s=t[o],i=r[o];i&&s!==i&&!(we(s)&&s.includes(i))&&(t[o]=s?[].concat(s,i):i)}else o!==""&&(t[o]=r[o])}return t}function an(e,t,n,r=null){nn(e,t,7,[n,r])}const sv=Sf();let iv=0;function lv(e,t,n){const r=e.type,o=(t?t.appContext:e.appContext)||sv,s={uid:iv++,vnode:e,type:r,parent:t,appContext:o,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ld(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(o.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Cf(r,o),emitsOptions:Lf(r,o),emit:null,emitted:null,propsDefaults:Ke,inheritAttrs:r.inheritAttrs,ctx:Ke,data:Ke,props:Ke,attrs:Ke,slots:Ke,refs:Ke,setupState:Ke,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=Jh.bind(null,s),e.ce&&e.ce(s),s}let mt=null;const $r=()=>mt||ft;let Ps,xl;{const e=Pd(),t=(n,r)=>{let o;return(o=e[n])||(o=e[n]=[]),o.push(r),s=>{o.length>1?o.forEach(i=>i(s)):o[0](s)}};Ps=t("__VUE_INSTANCE_SETTERS__",n=>mt=n),xl=t("__VUE_SSR_SETTERS__",n=>ti=n)}const Bo=e=>{const t=mt;return Ps(e),e.scope.on(),()=>{e.scope.off(),Ps(t)}},Ru=()=>{mt&&mt.scope.off(),Ps(null)};function Vf(e){return e.vnode.shapeFlag&4}let ti=!1;function av(e,t=!1,n=!1){t&&xl(t);const{props:r,children:o}=e.vnode,s=Vf(e);Hh(e,r,s,t),Bh(e,o,n);const i=s?uv(e,t):void 0;return t&&xl(!1),i}function uv(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Th);const{setup:r}=n;if(r){const o=e.setupContext=r.length>1?dv(e):null,s=Bo(e);Yn();const i=Do(r,e,0,[e.props,o]);if(Jn(),s(),Ed(i)){if(Lr(e)||hf(e),i.then(Ru,Ru),t)return i.then(l=>{Mu(e,l)}).catch(l=>{Ks(l,e,0)});e.asyncDep=i}else Mu(e,i)}else Df(e)}function Mu(e,t,n){$e(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Je(t)&&(e.setupState=Qd(t)),Df(e)}function Df(e,t,n){const r=e.type;e.render||(e.render=r.render||fn);{const o=Bo(e);Yn();try{Oh(e)}finally{Jn(),o()}}}const cv={get(e,t){return Et(e,"get",""),e[t]}};function dv(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,cv),slots:e.slots,emit:e.emit,expose:t}}function ni(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Qd(Gs(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in uo)return uo[n](e)},has(t,n){return n in t||n in uo}})):e.proxy}function fv(e,t=!0){return $e(e)?e.displayName||e.name:e.name||t&&e.__name}function pv(e){return $e(e)&&"__vccOpts"in e}const O=(e,t)=>eh(e,t,ti);function Oe(e,t,n){const r=arguments.length;return r===2?Je(t)&&!we(t)?Dr(t)?M(e,null,[t]):M(e,t):M(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&Dr(n)&&(n=[n]),M(e,t,n))}const hv="3.5.6";/**
* @vue/runtime-dom v3.5.6
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let El;const ku=typeof window<"u"&&window.trustedTypes;if(ku)try{El=ku.createPolicy("vue",{createHTML:e=>e})}catch{}const Bf=El?e=>El.createHTML(e):e=>e,vv="http://www.w3.org/2000/svg",mv="http://www.w3.org/1998/Math/MathML",Sn=typeof document<"u"?document:null,Lu=Sn&&Sn.createElement("template"),gv={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t==="svg"?Sn.createElementNS(vv,e):t==="mathml"?Sn.createElementNS(mv,e):n?Sn.createElement(e,{is:n}):Sn.createElement(e);return e==="select"&&r&&r.multiple!=null&&o.setAttribute("multiple",r.multiple),o},createText:e=>Sn.createTextNode(e),createComment:e=>Sn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Sn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,o,s){const i=n?n.previousSibling:t.lastChild;if(o&&(o===s||o.nextSibling))for(;t.insertBefore(o.cloneNode(!0),n),!(o===s||!(o=o.nextSibling)););else{Lu.innerHTML=Bf(r==="svg"?`<svg>${e}</svg>`:r==="mathml"?`<math>${e}</math>`:e);const l=Lu.content;if(r==="svg"||r==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Rn="transition",Yr="animation",Co=Symbol("_vtc"),jf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},yv=wt({},uf,jf),bv=e=>(e.displayName="Transition",e.props=yv,e),ri=bv((e,{slots:t})=>Oe(mh,wv(e),t)),tr=(e,t=[])=>{we(e)?e.forEach(n=>n(...t)):e&&e(...t)},Iu=e=>e?we(e)?e.some(t=>t.length>1):e.length>1:!1;function wv(e){const t={};for(const G in e)G in jf||(t[G]=e[G]);if(e.css===!1)return t;const{name:n="v",type:r,duration:o,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=s,appearActiveClass:u=i,appearToClass:c=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,v=_v(o),m=v&&v[0],g=v&&v[1],{onBeforeEnter:b,onEnter:y,onEnterCancelled:S,onLeave:A,onLeaveCancelled:$,onBeforeAppear:I=b,onAppear:H=y,onAppearCancelled:U=S}=t,N=(G,le,fe)=>{nr(G,le?c:l),nr(G,le?u:i),fe&&fe()},D=(G,le)=>{G._isLeaving=!1,nr(G,f),nr(G,p),nr(G,d),le&&le()},se=G=>(le,fe)=>{const Se=G?H:y,re=()=>N(le,G,fe);tr(Se,[le,re]),Nu(()=>{nr(le,G?a:s),Mn(le,G?c:l),Iu(Se)||Hu(le,r,m,re)})};return wt(t,{onBeforeEnter(G){tr(b,[G]),Mn(G,s),Mn(G,i)},onBeforeAppear(G){tr(I,[G]),Mn(G,a),Mn(G,u)},onEnter:se(!1),onAppear:se(!0),onLeave(G,le){G._isLeaving=!0;const fe=()=>D(G,le);Mn(G,f),Mn(G,d),Av(),Nu(()=>{G._isLeaving&&(nr(G,f),Mn(G,p),Iu(A)||Hu(G,r,g,fe))}),tr(A,[G,fe])},onEnterCancelled(G){N(G,!1),tr(S,[G])},onAppearCancelled(G){N(G,!0),tr(U,[G])},onLeaveCancelled(G){D(G),tr($,[G])}})}function _v(e){if(e==null)return null;if(Je(e))return[Ui(e.enter),Ui(e.leave)];{const t=Ui(e);return[t,t]}}function Ui(e){return g1(e)}function Mn(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Co]||(e[Co]=new Set)).add(t)}function nr(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const n=e[Co];n&&(n.delete(t),n.size||(e[Co]=void 0))}function Nu(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Sv=0;function Hu(e,t,n,r){const o=e._endId=++Sv,s=()=>{o===e._endId&&r()};if(n)return setTimeout(s,n);const{type:i,timeout:l,propCount:a}=$v(e,t);if(!i)return r();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,d),s()},d=p=>{p.target===e&&++c>=a&&f()};setTimeout(()=>{c<a&&f()},l+1),e.addEventListener(u,d)}function $v(e,t){const n=window.getComputedStyle(e),r=v=>(n[v]||"").split(", "),o=r(`${Rn}Delay`),s=r(`${Rn}Duration`),i=Fu(o,s),l=r(`${Yr}Delay`),a=r(`${Yr}Duration`),u=Fu(l,a);let c=null,f=0,d=0;t===Rn?i>0&&(c=Rn,f=i,d=s.length):t===Yr?u>0&&(c=Yr,f=u,d=a.length):(f=Math.max(i,u),c=f>0?i>u?Rn:Yr:null,d=c?c===Rn?s.length:a.length:0);const p=c===Rn&&/\b(transform|all)(,|$)/.test(r(`${Rn}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:p}}function Fu(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>Vu(n)+Vu(e[r])))}function Vu(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Av(){return document.body.offsetHeight}function xv(e,t,n){const r=e[Co];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Rs=Symbol("_vod"),zf=Symbol("_vsh"),Ev={beforeMount(e,{value:t},{transition:n}){e[Rs]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Jr(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),Jr(e,!0),r.enter(e)):r.leave(e,()=>{Jr(e,!1)}):Jr(e,t))},beforeUnmount(e,{value:t}){Jr(e,t)}};function Jr(e,t){e.style.display=t?e[Rs]:"none",e[zf]=!t}const Cv=Symbol(""),Tv=/(^|;)\s*display\s*:/;function Ov(e,t,n){const r=e.style,o=nt(n);let s=!1;if(n&&!o){if(t)if(nt(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&gs(r,l,"")}else for(const i in t)n[i]==null&&gs(r,i,"");for(const i in n)i==="display"&&(s=!0),gs(r,i,n[i])}else if(o){if(t!==n){const i=r[Cv];i&&(n+=";"+i),r.cssText=n,s=Tv.test(n)}}else t&&e.removeAttribute("style");Rs in e&&(e[Rs]=s?r.display:"",e[zf]&&(r.display="none"))}const Du=/\s*!important$/;function gs(e,t,n){if(we(n))n.forEach(r=>gs(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Pv(e,t);Du.test(n)?e.setProperty(Kn(r),n.replace(Du,""),"important"):e[r]=n}}const Bu=["Webkit","Moz","ms"],Zi={};function Pv(e,t){const n=Zi[t];if(n)return n;let r=tn(t);if(r!=="filter"&&r in e)return Zi[t]=r;r=Zs(r);for(let o=0;o<Bu.length;o++){const s=Bu[o]+r;if(s in e)return Zi[t]=s}return t}const ju="http://www.w3.org/1999/xlink";function zu(e,t,n,r,o,s=A1(t)){r&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ju,t.slice(6,t.length)):e.setAttributeNS(ju,t,n):n==null||s&&!Rd(n)?e.removeAttribute(t):e.setAttribute(t,s?"":Gn(n)?String(n):n)}function Rv(e,t,n,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Bf(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const i=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(i!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let s=!1;if(n===""||n==null){const i=typeof e[t];i==="boolean"?n=Rd(n):n==null&&i==="string"?(n="",s=!0):i==="number"&&(n=0,s=!0)}try{e[t]=n}catch{}s&&e.removeAttribute(t)}function Mv(e,t,n,r){e.addEventListener(t,n,r)}function kv(e,t,n,r){e.removeEventListener(t,n,r)}const Uu=Symbol("_vei");function Lv(e,t,n,r,o=null){const s=e[Uu]||(e[Uu]={}),i=s[t];if(r&&i)i.value=r;else{const[l,a]=Iv(t);if(r){const u=s[t]=Fv(r,o);Mv(e,l,u,a)}else i&&(kv(e,l,i,a),s[t]=void 0)}}const Zu=/(?:Once|Passive|Capture)$/;function Iv(e){let t;if(Zu.test(e)){t={};let r;for(;r=e.match(Zu);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Kn(e.slice(2)),t]}let qi=0;const Nv=Promise.resolve(),Hv=()=>qi||(Nv.then(()=>qi=0),qi=Date.now());function Fv(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;nn(Vv(r,n.value),t,5,[r])};return n.value=e,n.attached=Hv(),n}function Vv(e,t){if(we(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>o=>!o._stopped&&r&&r(o))}else return t}const qu=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Dv=(e,t,n,r,o,s)=>{const i=o==="svg";t==="class"?xv(e,r,i):t==="style"?Ov(e,n,r):js(t)?aa(t)||Lv(e,t,n,r,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Bv(e,t,r,i))?(Rv(e,t,r),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&zu(e,t,r,i,s,t!=="value")):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),zu(e,t,r,i))};function Bv(e,t,n,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&qu(t)&&$e(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const o=e.tagName;if(o==="IMG"||o==="VIDEO"||o==="CANVAS"||o==="SOURCE")return!1}return qu(t)&&nt(n)?!1:!!(t in e||e._isVueCE&&(/[A-Z]/.test(t)||!nt(n)))}const jv={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},zv=(e,t)=>{const n=e._withKeys||(e._withKeys={}),r=t.join(".");return n[r]||(n[r]=o=>{if(!("key"in o))return;const s=Kn(o.key);if(t.some(i=>i===s||jv[i]===s))return e(o)})},Uv=wt({patchProp:Dv},gv);let Wu;function Zv(){return Wu||(Wu=zh(Uv))}const Pa=(...e)=>{const t=Zv().createApp(...e),{mount:n}=t;return t.mount=r=>{const o=Wv(r);if(!o)return;const s=t._component;!$e(s)&&!s.render&&!s.template&&(s.template=o.innerHTML),o.nodeType===1&&(o.textContent="");const i=n(o,!1,qv(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function qv(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Wv(e){return nt(e)?document.querySelector(e):e}/*!
  * vue-router v4.4.5
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const Tr=typeof document<"u";function Uf(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Gv(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Uf(e.default)}const Ue=Object.assign;function Wi(e,t){const n={};for(const r in t){const o=t[r];n[r]=on(o)?o.map(e):e(o)}return n}const fo=()=>{},on=Array.isArray,Zf=/#/g,Kv=/&/g,Yv=/\//g,Jv=/=/g,Xv=/\?/g,qf=/\+/g,Qv=/%5B/g,em=/%5D/g,Wf=/%5E/g,tm=/%60/g,Gf=/%7B/g,nm=/%7C/g,Kf=/%7D/g,rm=/%20/g;function Ra(e){return encodeURI(""+e).replace(nm,"|").replace(Qv,"[").replace(em,"]")}function om(e){return Ra(e).replace(Gf,"{").replace(Kf,"}").replace(Wf,"^")}function Cl(e){return Ra(e).replace(qf,"%2B").replace(rm,"+").replace(Zf,"%23").replace(Kv,"%26").replace(tm,"`").replace(Gf,"{").replace(Kf,"}").replace(Wf,"^")}function sm(e){return Cl(e).replace(Jv,"%3D")}function im(e){return Ra(e).replace(Zf,"%23").replace(Xv,"%3F")}function lm(e){return e==null?"":im(e).replace(Yv,"%2F")}function To(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const am=/\/$/,um=e=>e.replace(am,"");function Gi(e,t,n="/"){let r,o={},s="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),s=t.slice(a+1,l>-1?l:t.length),o=e(s)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=pm(r??t,n),{fullPath:r+(s&&"?")+s+i,path:r,query:o,hash:To(i)}}function cm(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Gu(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function dm(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&Br(t.matched[r],n.matched[o])&&Yf(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Br(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Yf(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!fm(e[n],t[n]))return!1;return!0}function fm(e,t){return on(e)?Ku(e,t):on(t)?Ku(t,e):e===t}function Ku(e,t){return on(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function pm(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),o=r[r.length-1];(o===".."||o===".")&&r.push("");let s=n.length-1,i,l;for(i=0;i<r.length;i++)if(l=r[i],l!==".")if(l==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+r.slice(i).join("/")}const kn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Oo;(function(e){e.pop="pop",e.push="push"})(Oo||(Oo={}));var po;(function(e){e.back="back",e.forward="forward",e.unknown=""})(po||(po={}));function hm(e){if(!e)if(Tr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),um(e)}const vm=/^[^#]+#/;function mm(e,t){return e.replace(vm,"#")+t}function gm(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const oi=()=>({left:window.scrollX,top:window.scrollY});function ym(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),o=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=gm(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Yu(e,t){return(history.state?history.state.position-t:-1)+e}const Tl=new Map;function bm(e,t){Tl.set(e,t)}function wm(e){const t=Tl.get(e);return Tl.delete(e),t}let _m=()=>location.protocol+"//"+location.host;function Jf(e,t){const{pathname:n,search:r,hash:o}=t,s=e.indexOf("#");if(s>-1){let l=o.includes(e.slice(s))?e.slice(s).length:1,a=o.slice(l);return a[0]!=="/"&&(a="/"+a),Gu(a,"")}return Gu(n,e)+r+o}function Sm(e,t,n,r){let o=[],s=[],i=null;const l=({state:d})=>{const p=Jf(e,location),v=n.value,m=t.value;let g=0;if(d){if(n.value=p,t.value=d,i&&i===v){i=null;return}g=m?d.position-m.position:0}else r(p);o.forEach(b=>{b(n.value,v,{delta:g,type:Oo.pop,direction:g?g>0?po.forward:po.back:po.unknown})})};function a(){i=n.value}function u(d){o.push(d);const p=()=>{const v=o.indexOf(d);v>-1&&o.splice(v,1)};return s.push(p),p}function c(){const{history:d}=window;d.state&&d.replaceState(Ue({},d.state,{scroll:oi()}),"")}function f(){for(const d of s)d();s=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function Ju(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?oi():null}}function $m(e){const{history:t,location:n}=window,r={value:Jf(e,n)},o={value:t.state};o.value||s(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(a,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:_m()+e+a;try{t[c?"replaceState":"pushState"](u,"",d),o.value=u}catch(p){console.error(p),n[c?"replace":"assign"](d)}}function i(a,u){const c=Ue({},t.state,Ju(o.value.back,a,o.value.forward,!0),u,{position:o.value.position});s(a,c,!0),r.value=a}function l(a,u){const c=Ue({},o.value,t.state,{forward:a,scroll:oi()});s(c.current,c,!0);const f=Ue({},Ju(r.value,a,null),{position:c.position+1},u);s(a,f,!1),r.value=a}return{location:r,state:o,push:l,replace:i}}function Am(e){e=hm(e);const t=$m(e),n=Sm(e,t.state,t.location,t.replace);function r(s,i=!0){i||n.pauseListeners(),history.go(s)}const o=Ue({location:"",base:e,go:r,createHref:mm.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function xm(e){return typeof e=="string"||e&&typeof e=="object"}function Xf(e){return typeof e=="string"||typeof e=="symbol"}const Qf=Symbol("");var Xu;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Xu||(Xu={}));function jr(e,t){return Ue(new Error,{type:e,[Qf]:!0},t)}function wn(e,t){return e instanceof Error&&Qf in e&&(t==null||!!(e.type&t))}const Qu="[^/]+?",Em={sensitive:!1,strict:!1,start:!0,end:!0},Cm=/[.+*?^${}()[\]/\\]/g;function Tm(e,t){const n=Ue({},Em,t),r=[];let o=n.start?"^":"";const s=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(o+="/");for(let f=0;f<u.length;f++){const d=u[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(o+="/"),o+=d.value.replace(Cm,"\\$&"),p+=40;else if(d.type===1){const{value:v,repeatable:m,optional:g,regexp:b}=d;s.push({name:v,repeatable:m,optional:g});const y=b||Qu;if(y!==Qu){p+=10;try{new RegExp(`(${y})`)}catch(A){throw new Error(`Invalid custom RegExp for param "${v}" (${y}): `+A.message)}}let S=m?`((?:${y})(?:/(?:${y}))*)`:`(${y})`;f||(S=g&&u.length<2?`(?:/${S})`:"/"+S),g&&(S+="?"),o+=S,p+=20,g&&(p+=-8),m&&(p+=-20),y===".*"&&(p+=-50)}c.push(p)}r.push(c)}if(n.strict&&n.end){const u=r.length-1;r[u][r[u].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let d=1;d<c.length;d++){const p=c[d]||"",v=s[d-1];f[v.name]=p&&v.repeatable?p.split("/"):p}return f}function a(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const p of d)if(p.type===0)c+=p.value;else if(p.type===1){const{value:v,repeatable:m,optional:g}=p,b=v in u?u[v]:"";if(on(b)&&!m)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const y=on(b)?b.join("/"):b;if(!y)if(g)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${v}"`);c+=y}}return c||"/"}return{re:i,score:r,keys:s,parse:l,stringify:a}}function Om(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function e0(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const s=Om(r[n],o[n]);if(s)return s;n++}if(Math.abs(o.length-r.length)===1){if(ec(r))return 1;if(ec(o))return-1}return o.length-r.length}function ec(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Pm={type:0,value:""},Rm=/[a-zA-Z0-9_]/;function Mm(e){if(!e)return[[]];if(e==="/")return[[Pm]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${u}": ${p}`)}let n=0,r=n;const o=[];let s;function i(){s&&o.push(s),s=[]}let l=0,a,u="",c="";function f(){u&&(n===0?s.push({type:0,value:u}):n===1||n===2||n===3?(s.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:Rm.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),o}function km(e,t,n){const r=Tm(Mm(e.path),n),o=Ue(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function Lm(e,t){const n=[],r=new Map;t=oc({strict:!1,end:!0,sensitive:!1},t);function o(f){return r.get(f)}function s(f,d,p){const v=!p,m=nc(f);m.aliasOf=p&&p.record;const g=oc(t,f),b=[m];if("alias"in f){const A=typeof f.alias=="string"?[f.alias]:f.alias;for(const $ of A)b.push(nc(Ue({},m,{components:p?p.record.components:m.components,path:$,aliasOf:p?p.record:m})))}let y,S;for(const A of b){const{path:$}=A;if(d&&$[0]!=="/"){const I=d.record.path,H=I[I.length-1]==="/"?"":"/";A.path=d.record.path+($&&H+$)}if(y=km(A,d,g),p?p.alias.push(y):(S=S||y,S!==y&&S.alias.push(y),v&&f.name&&!rc(y)&&i(f.name)),t0(y)&&a(y),m.children){const I=m.children;for(let H=0;H<I.length;H++)s(I[H],y,p&&p.children[H])}p=p||y}return S?()=>{i(S)}:fo}function i(f){if(Xf(f)){const d=r.get(f);d&&(r.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&r.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const d=Hm(f,n);n.splice(d,0,f),f.record.name&&!rc(f)&&r.set(f.record.name,f)}function u(f,d){let p,v={},m,g;if("name"in f&&f.name){if(p=r.get(f.name),!p)throw jr(1,{location:f});g=p.record.name,v=Ue(tc(d.params,p.keys.filter(S=>!S.optional).concat(p.parent?p.parent.keys.filter(S=>S.optional):[]).map(S=>S.name)),f.params&&tc(f.params,p.keys.map(S=>S.name))),m=p.stringify(v)}else if(f.path!=null)m=f.path,p=n.find(S=>S.re.test(m)),p&&(v=p.parse(m),g=p.record.name);else{if(p=d.name?r.get(d.name):n.find(S=>S.re.test(d.path)),!p)throw jr(1,{location:f,currentLocation:d});g=p.record.name,v=Ue({},d.params,f.params),m=p.stringify(v)}const b=[];let y=p;for(;y;)b.unshift(y.record),y=y.parent;return{name:g,path:m,params:v,matched:b,meta:Nm(b)}}e.forEach(f=>s(f));function c(){n.length=0,r.clear()}return{addRoute:s,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:o}}function tc(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function nc(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Im(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Im(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function rc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Nm(e){return e.reduce((t,n)=>Ue(t,n.meta),{})}function oc(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function Hm(e,t){let n=0,r=t.length;for(;n!==r;){const s=n+r>>1;e0(e,t[s])<0?r=s:n=s+1}const o=Fm(e);return o&&(r=t.lastIndexOf(o,r-1)),r}function Fm(e){let t=e;for(;t=t.parent;)if(t0(t)&&e0(e,t)===0)return t}function t0({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Vm(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const s=r[o].replace(qf," "),i=s.indexOf("="),l=To(i<0?s:s.slice(0,i)),a=i<0?null:To(s.slice(i+1));if(l in t){let u=t[l];on(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function sc(e){let t="";for(let n in e){const r=e[n];if(n=sm(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(on(r)?r.map(s=>s&&Cl(s)):[r&&Cl(r)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function Dm(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=on(r)?r.map(o=>o==null?null:""+o):r==null?r:""+r)}return t}const Bm=Symbol(""),ic=Symbol(""),si=Symbol(""),Ma=Symbol(""),Ol=Symbol("");function Xr(){let e=[];function t(r){return e.push(r),()=>{const o=e.indexOf(r);o>-1&&e.splice(o,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Dn(e,t,n,r,o,s=i=>i()){const i=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise((l,a)=>{const u=d=>{d===!1?a(jr(4,{from:n,to:t})):d instanceof Error?a(d):xm(d)?a(jr(2,{from:t,to:d})):(i&&r.enterCallbacks[o]===i&&typeof d=="function"&&i.push(d),l())},c=s(()=>e.call(r&&r.instances[o],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>a(d))})}function Ki(e,t,n,r,o=s=>s()){const s=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Uf(a)){const c=(a.__vccOpts||a)[t];c&&s.push(Dn(c,n,r,i,l,o))}else{let u=a();s.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=Gv(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const p=(f.__vccOpts||f)[t];return p&&Dn(p,n,r,i,l,o)()}))}}return s}function lc(e){const t=ae(si),n=ae(Ma),r=O(()=>{const a=R(e.to);return t.resolve(a)}),o=O(()=>{const{matched:a}=r.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(Br.bind(null,c));if(d>-1)return d;const p=ac(a[u-2]);return u>1&&ac(c)===p&&f[f.length-1].path!==p?f.findIndex(Br.bind(null,a[u-2])):d}),s=O(()=>o.value>-1&&Zm(n.params,r.value.params)),i=O(()=>o.value>-1&&o.value===n.matched.length-1&&Yf(n.params,r.value.params));function l(a={}){return Um(a)?t[R(e.replace)?"replace":"push"](R(e.to)).catch(fo):Promise.resolve()}return{route:r,href:O(()=>r.value.href),isActive:s,isExactActive:i,navigate:l}}const jm=_e({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:lc,setup(e,{slots:t}){const n=_t(lc(e)),{options:r}=ae(si),o=O(()=>({[uc(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[uc(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&t.default(n);return e.custom?s:Oe("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:o.value},s)}}}),zm=jm;function Um(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Zm(e,t){for(const n in t){const r=t[n],o=e[n];if(typeof r=="string"){if(r!==o)return!1}else if(!on(o)||o.length!==r.length||r.some((s,i)=>s!==o[i]))return!1}return!0}function ac(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const uc=(e,t,n)=>e??t??n,qm=_e({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=ae(Ol),o=O(()=>e.route||r.value),s=ae(ic,0),i=O(()=>{let u=R(s);const{matched:c}=o.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=O(()=>o.value.matched[i.value]);st(ic,O(()=>i.value+1)),st(Bm,l),st(Ol,o);const a=F();return ut(()=>[a.value,l.value,e.name],([u,c,f],[d,p,v])=>{c&&(c.instances[f]=u,p&&p!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!Br(c,p)||!d)&&(c.enterCallbacks[f]||[]).forEach(m=>m(u))},{flush:"post"}),()=>{const u=o.value,c=e.name,f=l.value,d=f&&f.components[c];if(!d)return cc(n.default,{Component:d,route:u});const p=f.props[c],v=p?p===!0?u.params:typeof p=="function"?p(u):p:null,g=Oe(d,Ue({},v,t,{onVnodeUnmounted:b=>{b.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return cc(n.default,{Component:g,route:u})||g}}});function cc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const n0=qm;function Wm(e){const t=Lm(e.routes,e),n=e.parseQuery||Vm,r=e.stringifyQuery||sc,o=e.history,s=Xr(),i=Xr(),l=Xr(),a=_a(kn);let u=kn;Tr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=Wi.bind(null,k=>""+k),f=Wi.bind(null,lm),d=Wi.bind(null,To);function p(k,Y){let Z,ue;return Xf(k)?(Z=t.getRecordMatcher(k),ue=Y):ue=k,t.addRoute(ue,Z)}function v(k){const Y=t.getRecordMatcher(k);Y&&t.removeRoute(Y)}function m(){return t.getRoutes().map(k=>k.record)}function g(k){return!!t.getRecordMatcher(k)}function b(k,Y){if(Y=Ue({},Y||a.value),typeof k=="string"){const P=Gi(n,k,Y.path),V=t.resolve({path:P.path},Y),z=o.createHref(P.fullPath);return Ue(P,V,{params:d(V.params),hash:To(P.hash),redirectedFrom:void 0,href:z})}let Z;if(k.path!=null)Z=Ue({},k,{path:Gi(n,k.path,Y.path).path});else{const P=Ue({},k.params);for(const V in P)P[V]==null&&delete P[V];Z=Ue({},k,{params:f(P)}),Y.params=f(Y.params)}const ue=t.resolve(Z,Y),Me=k.hash||"";ue.params=c(d(ue.params));const w=cm(r,Ue({},k,{hash:om(Me),path:ue.path})),_=o.createHref(w);return Ue({fullPath:w,hash:Me,query:r===sc?Dm(k.query):k.query||{}},ue,{redirectedFrom:void 0,href:_})}function y(k){return typeof k=="string"?Gi(n,k,a.value.path):Ue({},k)}function S(k,Y){if(u!==k)return jr(8,{from:Y,to:k})}function A(k){return H(k)}function $(k){return A(Ue(y(k),{replace:!0}))}function I(k){const Y=k.matched[k.matched.length-1];if(Y&&Y.redirect){const{redirect:Z}=Y;let ue=typeof Z=="function"?Z(k):Z;return typeof ue=="string"&&(ue=ue.includes("?")||ue.includes("#")?ue=y(ue):{path:ue},ue.params={}),Ue({query:k.query,hash:k.hash,params:ue.path!=null?{}:k.params},ue)}}function H(k,Y){const Z=u=b(k),ue=a.value,Me=k.state,w=k.force,_=k.replace===!0,P=I(Z);if(P)return H(Ue(y(P),{state:typeof P=="object"?Ue({},Me,P.state):Me,force:w,replace:_}),Y||Z);const V=Z;V.redirectedFrom=Y;let z;return!w&&dm(r,ue,Z)&&(z=jr(16,{to:V,from:ue}),Ae(ue,ue,!0,!1)),(z?Promise.resolve(z):D(V,ue)).catch(B=>wn(B)?wn(B,2)?B:ht(B):me(B,V,ue)).then(B=>{if(B){if(wn(B,2))return H(Ue({replace:_},y(B.to),{state:typeof B.to=="object"?Ue({},Me,B.to.state):Me,force:w}),Y||V)}else B=G(V,ue,!0,_,Me);return se(V,ue,B),B})}function U(k,Y){const Z=S(k,Y);return Z?Promise.reject(Z):Promise.resolve()}function N(k){const Y=Fe.values().next().value;return Y&&typeof Y.runWithContext=="function"?Y.runWithContext(k):k()}function D(k,Y){let Z;const[ue,Me,w]=Gm(k,Y);Z=Ki(ue.reverse(),"beforeRouteLeave",k,Y);for(const P of ue)P.leaveGuards.forEach(V=>{Z.push(Dn(V,k,Y))});const _=U.bind(null,k,Y);return Z.push(_),Q(Z).then(()=>{Z=[];for(const P of s.list())Z.push(Dn(P,k,Y));return Z.push(_),Q(Z)}).then(()=>{Z=Ki(Me,"beforeRouteUpdate",k,Y);for(const P of Me)P.updateGuards.forEach(V=>{Z.push(Dn(V,k,Y))});return Z.push(_),Q(Z)}).then(()=>{Z=[];for(const P of w)if(P.beforeEnter)if(on(P.beforeEnter))for(const V of P.beforeEnter)Z.push(Dn(V,k,Y));else Z.push(Dn(P.beforeEnter,k,Y));return Z.push(_),Q(Z)}).then(()=>(k.matched.forEach(P=>P.enterCallbacks={}),Z=Ki(w,"beforeRouteEnter",k,Y,N),Z.push(_),Q(Z))).then(()=>{Z=[];for(const P of i.list())Z.push(Dn(P,k,Y));return Z.push(_),Q(Z)}).catch(P=>wn(P,8)?P:Promise.reject(P))}function se(k,Y,Z){l.list().forEach(ue=>N(()=>ue(k,Y,Z)))}function G(k,Y,Z,ue,Me){const w=S(k,Y);if(w)return w;const _=Y===kn,P=Tr?history.state:{};Z&&(ue||_?o.replace(k.fullPath,Ue({scroll:_&&P&&P.scroll},Me)):o.push(k.fullPath,Me)),a.value=k,Ae(k,Y,Z,_),ht()}let le;function fe(){le||(le=o.listen((k,Y,Z)=>{if(!Zt.listening)return;const ue=b(k),Me=I(ue);if(Me){H(Ue(Me,{replace:!0}),ue).catch(fo);return}u=ue;const w=a.value;Tr&&bm(Yu(w.fullPath,Z.delta),oi()),D(ue,w).catch(_=>wn(_,12)?_:wn(_,2)?(H(_.to,ue).then(P=>{wn(P,20)&&!Z.delta&&Z.type===Oo.pop&&o.go(-1,!1)}).catch(fo),Promise.reject()):(Z.delta&&o.go(-Z.delta,!1),me(_,ue,w))).then(_=>{_=_||G(ue,w,!1),_&&(Z.delta&&!wn(_,8)?o.go(-Z.delta,!1):Z.type===Oo.pop&&wn(_,20)&&o.go(-1,!1)),se(ue,w,_)}).catch(fo)}))}let Se=Xr(),re=Xr(),K;function me(k,Y,Z){ht(k);const ue=re.list();return ue.length?ue.forEach(Me=>Me(k,Y,Z)):console.error(k),Promise.reject(k)}function De(){return K&&a.value!==kn?Promise.resolve():new Promise((k,Y)=>{Se.add([k,Y])})}function ht(k){return K||(K=!k,fe(),Se.list().forEach(([Y,Z])=>k?Z(k):Y()),Se.reset()),k}function Ae(k,Y,Z,ue){const{scrollBehavior:Me}=e;if(!Tr||!Me)return Promise.resolve();const w=!Z&&wm(Yu(k.fullPath,0))||(ue||!Z)&&history.state&&history.state.scroll||null;return et().then(()=>Me(k,Y,w)).then(_=>_&&ym(_)).catch(_=>me(_,k,Y))}const Re=k=>o.go(k);let Xe;const Fe=new Set,Zt={currentRoute:a,listening:!0,addRoute:p,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:g,getRoutes:m,resolve:b,options:e,push:A,replace:$,go:Re,back:()=>Re(-1),forward:()=>Re(1),beforeEach:s.add,beforeResolve:i.add,afterEach:l.add,onError:re.add,isReady:De,install(k){const Y=this;k.component("RouterLink",zm),k.component("RouterView",n0),k.config.globalProperties.$router=Y,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>R(a)}),Tr&&!Xe&&a.value===kn&&(Xe=!0,A(o.location).catch(Me=>{}));const Z={};for(const Me in kn)Object.defineProperty(Z,Me,{get:()=>a.value[Me],enumerable:!0});k.provide(si,Y),k.provide(Ma,Jd(Z)),k.provide(Ol,a);const ue=k.unmount;Fe.add(k),k.unmount=function(){Fe.delete(k),Fe.size<1&&(u=kn,le&&le(),le=null,a.value=kn,Xe=!1,K=!1),ue()}}};function Q(k){return k.reduce((Y,Z)=>Y.then(()=>N(Z)),Promise.resolve())}return Zt}function Gm(e,t){const n=[],r=[],o=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const l=t.matched[i];l&&(e.matched.find(u=>Br(u,l))?r.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>Br(u,a))||o.push(a))}return[n,r,o]}function r0(){return ae(si)}function ka(e){return ae(Ma)}function Km(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M3 6.75A.75.75 0 0 1 3.75 6h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 6.75ZM3 12a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 12Zm0 5.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75a.75.75 0 0 1-.75-.75Z","clip-rule":"evenodd"})])}function o0(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z","clip-rule":"evenodd"})])}function Ym(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm14.024-.983a1.125 1.125 0 0 1 0 1.966l-5.603 3.113A1.125 1.125 0 0 1 9 15.113V8.887c0-.857.921-1.4 1.671-.983l5.603 3.113Z","clip-rule":"evenodd"})])}function Jm(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25ZM12.75 9a.75.75 0 0 0-1.5 0v2.25H9a.75.75 0 0 0 0 1.5h2.25V15a.75.75 0 0 0 1.5 0v-2.25H15a.75.75 0 0 0 0-1.5h-2.25V9Z","clip-rule":"evenodd"})])}function s0(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M5.47 5.47a.75.75 0 0 1 1.06 0L12 10.94l5.47-5.47a.75.75 0 1 1 1.06 1.06L13.06 12l5.47 5.47a.75.75 0 1 1-1.06 1.06L12 13.06l-5.47 5.47a.75.75 0 0 1-1.06-1.06L10.94 12 5.47 6.53a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}function Xm(e,t,n){let r=F(n==null?void 0:n.value),o=O(()=>e.value!==void 0);return[O(()=>o.value?e.value:r.value),function(s){return o.value||(r.value=s),t==null?void 0:t(s)}]}function ii(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function jo(){let e=[],t={addEventListener(n,r,o,s){return n.addEventListener(r,o,s),t.add(()=>n.removeEventListener(r,o,s))},requestAnimationFrame(...n){let r=requestAnimationFrame(...n);t.add(()=>cancelAnimationFrame(r))},nextFrame(...n){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...n)})},setTimeout(...n){let r=setTimeout(...n);t.add(()=>clearTimeout(r))},microTask(...n){let r={current:!0};return ii(()=>{r.current&&n[0]()}),t.add(()=>{r.current=!1})},style(n,r,o){let s=n.style.getPropertyValue(r);return Object.assign(n.style,{[r]:o}),this.add(()=>{Object.assign(n.style,{[r]:s})})},group(n){let r=jo();return n(r),this.add(()=>r.dispose())},add(n){return e.push(n),()=>{let r=e.indexOf(n);if(r>=0)for(let o of e.splice(r,1))o()}},dispose(){for(let n of e.splice(0))n()}};return t}var dc;let Qm=Symbol("headlessui.useid"),e2=0;const Nt=(dc=gh)!=null?dc:function(){return ae(Qm,()=>`${++e2}`)()};function q(e){var t;if(e==null||e.value==null)return null;let n=(t=e.value.$el)!=null?t:e.value;return n instanceof Node?n:null}function ct(e,t,...n){if(e in t){let o=t[e];return typeof o=="function"?o(...n):o}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ct),r}var t2=Object.defineProperty,n2=(e,t,n)=>t in e?t2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,fc=(e,t,n)=>(n2(e,typeof t!="symbol"?t+"":t,n),n);let r2=class{constructor(){fc(this,"current",this.detect()),fc(this,"currentId",0)}set(t){this.current!==t&&(this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}},zo=new r2;function Yt(e){if(zo.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let t=q(e);if(t)return t.ownerDocument}return document}let Pl=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var qe=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(qe||{}),An=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(An||{}),o2=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(o2||{});function Uo(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(Pl)).sort((t,n)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(n.tabIndex||Number.MAX_SAFE_INTEGER)))}var li=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(li||{});function ai(e,t=0){var n;return e===((n=Yt(e))==null?void 0:n.body)?!1:ct(t,{0(){return e.matches(Pl)},1(){let r=e;for(;r!==null;){if(r.matches(Pl))return!0;r=r.parentElement}return!1}})}function i0(e){let t=Yt(e);et(()=>{t&&!ai(t.activeElement,0)&&zn(e)})}var s2=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(s2||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function zn(e){e==null||e.focus({preventScroll:!0})}let i2=["textarea","input"].join(",");function l2(e){var t,n;return(n=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,i2))!=null?n:!1}function ur(e,t=n=>n){return e.slice().sort((n,r)=>{let o=t(n),s=t(r);if(o===null||s===null)return 0;let i=o.compareDocumentPosition(s);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function a2(e,t){return vt(Uo(),t,{relativeTo:e})}function vt(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:o=[]}={}){var s;let i=(s=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?s:document,l=Array.isArray(e)?n?ur(e):e:Uo(e);o.length>0&&l.length>1&&(l=l.filter(v=>!o.includes(v))),r=r??i.activeElement;let a=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,l.indexOf(r))-1;if(t&4)return Math.max(0,l.indexOf(r))+1;if(t&8)return l.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=t&32?{preventScroll:!0}:{},f=0,d=l.length,p;do{if(f>=d||f+d<=0)return 0;let v=u+f;if(t&16)v=(v+d)%d;else{if(v<0)return 3;if(v>=d)return 1}p=l[v],p==null||p.focus(c),f+=a}while(p!==i.activeElement);return t&6&&l2(p)&&p.select(),2}function l0(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function u2(){return/Android/gi.test(window.navigator.userAgent)}function c2(){return l0()||u2()}function ls(e,t,n){zo.isServer||it(r=>{document.addEventListener(e,t,n),r(()=>document.removeEventListener(e,t,n))})}function a0(e,t,n){zo.isServer||it(r=>{window.addEventListener(e,t,n),r(()=>window.removeEventListener(e,t,n))})}function La(e,t,n=O(()=>!0)){function r(s,i){if(!n.value||s.defaultPrevented)return;let l=i(s);if(l===null||!l.getRootNode().contains(l))return;let a=function u(c){return typeof c=="function"?u(c()):Array.isArray(c)||c instanceof Set?c:[c]}(e);for(let u of a){if(u===null)continue;let c=u instanceof HTMLElement?u:q(u);if(c!=null&&c.contains(l)||s.composed&&s.composedPath().includes(c))return}return!ai(l,li.Loose)&&l.tabIndex!==-1&&s.preventDefault(),t(s,l)}let o=F(null);ls("pointerdown",s=>{var i,l;n.value&&(o.value=((l=(i=s.composedPath)==null?void 0:i.call(s))==null?void 0:l[0])||s.target)},!0),ls("mousedown",s=>{var i,l;n.value&&(o.value=((l=(i=s.composedPath)==null?void 0:i.call(s))==null?void 0:l[0])||s.target)},!0),ls("click",s=>{c2()||o.value&&(r(s,()=>o.value),o.value=null)},!0),ls("touchend",s=>r(s,()=>s.target instanceof HTMLElement?s.target:null),!0),a0("blur",s=>r(s,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function pc(e,t){if(e)return e;let n=t??"button";if(typeof n=="string"&&n.toLowerCase()==="button")return"button"}function ui(e,t){let n=F(pc(e.value.type,e.value.as));return We(()=>{n.value=pc(e.value.type,e.value.as)}),it(()=>{var r;n.value||q(t)&&q(t)instanceof HTMLButtonElement&&!((r=q(t))!=null&&r.hasAttribute("type"))&&(n.value="button")}),n}function hc(e){return[e.screenX,e.screenY]}function d2(){let e=F([-1,-1]);return{wasMoved(t){let n=hc(t);return e.value[0]===n[0]&&e.value[1]===n[1]?!1:(e.value=n,!0)},update(t){e.value=hc(t)}}}function f2({container:e,accept:t,walk:n,enabled:r}){it(()=>{let o=e.value;if(!o||r!==void 0&&!r.value)return;let s=Yt(e);if(!s)return;let i=Object.assign(a=>t(a),{acceptNode:t}),l=s.createTreeWalker(o,NodeFilter.SHOW_ELEMENT,i,!1);for(;l.nextNode();)n(l.currentNode)})}var mn=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(mn||{}),Bn=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(Bn||{});function lt({visible:e=!0,features:t=0,ourProps:n,theirProps:r,...o}){var s;let i=c0(r,n),l=Object.assign(o,{props:i});if(e||t&2&&i.static)return Yi(l);if(t&1){let a=(s=i.unmount)==null||s?0:1;return ct(a,{0(){return null},1(){return Yi({...o,props:{...i,hidden:!0,style:{display:"none"}}})}})}return Yi(l)}function Yi({props:e,attrs:t,slots:n,slot:r,name:o}){var s,i;let{as:l,...a}=ci(e,["unmount","static"]),u=(s=n.default)==null?void 0:s.call(n,r),c={};if(r){let f=!1,d=[];for(let[p,v]of Object.entries(r))typeof v=="boolean"&&(f=!0),v===!0&&d.push(p);f&&(c["data-headlessui-state"]=d.join(" "))}if(l==="template"){if(u=u0(u??[]),Object.keys(a).length>0||Object.keys(t).length>0){let[f,...d]=u??[];if(!h2(f)||d.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${o} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(a).concat(Object.keys(t)).map(m=>m.trim()).filter((m,g,b)=>b.indexOf(m)===g).sort((m,g)=>m.localeCompare(g)).map(m=>`  - ${m}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(m=>`  - ${m}`).join(`
`)].join(`
`));let p=c0((i=f.props)!=null?i:{},a,c),v=vn(f,p,!0);for(let m in p)m.startsWith("on")&&(v.props||(v.props={}),v.props[m]=p[m]);return v}return Array.isArray(u)&&u.length===1?u[0]:u}return Oe(l,Object.assign({},a,c),{default:()=>u})}function u0(e){return e.flatMap(t=>t.type===he?u0(t.children):[t])}function c0(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},n={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(n[o]!=null||(n[o]=[]),n[o].push(r[o])):t[o]=r[o];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map(r=>[r,void 0])));for(let r in n)Object.assign(t,{[r](o,...s){let i=n[r];for(let l of i){if(o instanceof Event&&o.defaultPrevented)return;l(o,...s)}}});return t}function p2(e){let t=Object.assign({},e);for(let n in t)t[n]===void 0&&delete t[n];return t}function ci(e,t=[]){let n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function h2(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}var En=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(En||{});let Cn=_e({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:t,attrs:n}){return()=>{var r;let{features:o,...s}=e,i={"aria-hidden":(o&2)===2?!0:(r=s["aria-hidden"])!=null?r:void 0,hidden:(o&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(o&4)===4&&(o&2)!==2&&{display:"none"}}};return lt({ourProps:i,theirProps:s,slot:{},attrs:n,slots:t,name:"Hidden"})}}}),d0=Symbol("Context");var at=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(at||{});function v2(){return Zo()!==null}function Zo(){return ae(d0,null)}function Ia(e){st(d0,e)}var ke=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(ke||{});function m2(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",t),t())}let cr=[];m2(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&cr[0]!==t.target&&(cr.unshift(t.target),cr=cr.filter(n=>n!=null&&n.isConnected),cr.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function g2(e){throw new Error("Unexpected object: "+e)}var jt=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(jt||{});function y2(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),o=r??-1;switch(e.focus){case 0:{for(let s=0;s<n.length;++s)if(!t.resolveDisabled(n[s],s,n))return s;return r}case 1:{o===-1&&(o=n.length);for(let s=o-1;s>=0;--s)if(!t.resolveDisabled(n[s],s,n))return s;return r}case 2:{for(let s=o+1;s<n.length;++s)if(!t.resolveDisabled(n[s],s,n))return s;return r}case 3:{for(let s=n.length-1;s>=0;--s)if(!t.resolveDisabled(n[s],s,n))return s;return r}case 4:{for(let s=0;s<n.length;++s)if(t.resolveId(n[s],s,n)===e.id)return s;return r}case 5:return null;default:g2(e)}}function b2(e){var t,n;let r=(t=e==null?void 0:e.form)!=null?t:e.closest("form");if(r){for(let o of r.elements)if(o!==e&&(o.tagName==="INPUT"&&o.type==="submit"||o.tagName==="BUTTON"&&o.type==="submit"||o.nodeName==="INPUT"&&o.type==="image")){o.click();return}(n=r.requestSubmit)==null||n.call(r)}}function Na(e,t,n,r){zo.isServer||it(o=>{e=e??window,e.addEventListener(t,n,r),o(()=>e.removeEventListener(t,n,r))})}var zt=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(zt||{});function Ha(){let e=F(0);return a0("keydown",t=>{t.key==="Tab"&&(e.value=t.shiftKey?1:0)}),e}function f0(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let n of e.value){let r=q(n);r instanceof HTMLElement&&t.add(r)}return t}var p0=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(p0||{});let Qr=Object.assign(_e({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:F(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:r}){let o=F(null);r({el:o,$el:o});let s=O(()=>Yt(o)),i=F(!1);We(()=>i.value=!0),pt(()=>i.value=!1),_2({ownerDocument:s},O(()=>i.value&&!!(e.features&16)));let l=S2({ownerDocument:s,container:o,initialFocus:O(()=>e.initialFocus)},O(()=>i.value&&!!(e.features&2)));$2({ownerDocument:s,container:o,containers:e.containers,previousActiveElement:l},O(()=>i.value&&!!(e.features&8)));let a=Ha();function u(p){let v=q(o);v&&(m=>m())(()=>{ct(a.value,{[zt.Forwards]:()=>{vt(v,qe.First,{skipElements:[p.relatedTarget]})},[zt.Backwards]:()=>{vt(v,qe.Last,{skipElements:[p.relatedTarget]})}})})}let c=F(!1);function f(p){p.key==="Tab"&&(c.value=!0,requestAnimationFrame(()=>{c.value=!1}))}function d(p){if(!i.value)return;let v=f0(e.containers);q(o)instanceof HTMLElement&&v.add(q(o));let m=p.relatedTarget;m instanceof HTMLElement&&m.dataset.headlessuiFocusGuard!=="true"&&(h0(v,m)||(c.value?vt(q(o),ct(a.value,{[zt.Forwards]:()=>qe.Next,[zt.Backwards]:()=>qe.Previous})|qe.WrapAround,{relativeTo:p.target}):p.target instanceof HTMLElement&&zn(p.target)))}return()=>{let p={},v={ref:o,onKeydown:f,onFocusout:d},{features:m,initialFocus:g,containers:b,...y}=e;return Oe(he,[!!(m&4)&&Oe(Cn,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:En.Focusable}),lt({ourProps:v,theirProps:{...t,...y},slot:p,attrs:t,slots:n,name:"FocusTrap"}),!!(m&4)&&Oe(Cn,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:En.Focusable})])}}}),{features:p0});function w2(e){let t=F(cr.slice());return ut([e],([n],[r])=>{r===!0&&n===!1?ii(()=>{t.value.splice(0)}):r===!1&&n===!0&&(t.value=cr.slice())},{flush:"post"}),()=>{var n;return(n=t.value.find(r=>r!=null&&r.isConnected))!=null?n:null}}function _2({ownerDocument:e},t){let n=w2(t);We(()=>{it(()=>{var r,o;t.value||((r=e.value)==null?void 0:r.activeElement)===((o=e.value)==null?void 0:o.body)&&zn(n())},{flush:"post"})}),pt(()=>{t.value&&zn(n())})}function S2({ownerDocument:e,container:t,initialFocus:n},r){let o=F(null),s=F(!1);return We(()=>s.value=!0),pt(()=>s.value=!1),We(()=>{ut([t,n,r],(i,l)=>{if(i.every((u,c)=>(l==null?void 0:l[c])===u)||!r.value)return;let a=q(t);a&&ii(()=>{var u,c;if(!s.value)return;let f=q(n),d=(u=e.value)==null?void 0:u.activeElement;if(f){if(f===d){o.value=d;return}}else if(a.contains(d)){o.value=d;return}f?zn(f):vt(a,qe.First|qe.NoScroll)===An.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.value=(c=e.value)==null?void 0:c.activeElement})},{immediate:!0,flush:"post"})}),o}function $2({ownerDocument:e,container:t,containers:n,previousActiveElement:r},o){var s;Na((s=e.value)==null?void 0:s.defaultView,"focus",i=>{if(!o.value)return;let l=f0(n);q(t)instanceof HTMLElement&&l.add(q(t));let a=r.value;if(!a)return;let u=i.target;u&&u instanceof HTMLElement?h0(l,u)?(r.value=u,zn(u)):(i.preventDefault(),i.stopPropagation(),zn(a)):zn(r.value)},!0)}function h0(e,t){for(let n of e)if(n.contains(t))return!0;return!1}function A2(e){let t=_a(e.getSnapshot());return pt(e.subscribe(()=>{t.value=e.getSnapshot()})),t}function x2(e,t){let n=e(),r=new Set;return{getSnapshot(){return n},subscribe(o){return r.add(o),()=>r.delete(o)},dispatch(o,...s){let i=t[o].call(n,...s);i&&(n=i,r.forEach(l=>l()))}}}function E2(){let e;return{before({doc:t}){var n;let r=t.documentElement;e=((n=t.defaultView)!=null?n:window).innerWidth-r.clientWidth},after({doc:t,d:n}){let r=t.documentElement,o=r.clientWidth-r.offsetWidth,s=e-o;n.style(r,"paddingRight",`${s}px`)}}}function C2(){return l0()?{before({doc:e,d:t,meta:n}){function r(o){return n.containers.flatMap(s=>s()).some(s=>s.contains(o))}t.microTask(()=>{var o;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let l=jo();l.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>l.dispose()))}let s=(o=window.scrollY)!=null?o:window.pageYOffset,i=null;t.addEventListener(e,"click",l=>{if(l.target instanceof HTMLElement)try{let a=l.target.closest("a");if(!a)return;let{hash:u}=new URL(a.href),c=e.querySelector(u);c&&!r(c)&&(i=c)}catch{}},!0),t.addEventListener(e,"touchstart",l=>{if(l.target instanceof HTMLElement)if(r(l.target)){let a=l.target;for(;a.parentElement&&r(a.parentElement);)a=a.parentElement;t.style(a,"overscrollBehavior","contain")}else t.style(l.target,"touchAction","none")}),t.addEventListener(e,"touchmove",l=>{if(l.target instanceof HTMLElement){if(l.target.tagName==="INPUT")return;if(r(l.target)){let a=l.target;for(;a.parentElement&&a.dataset.headlessuiPortal!==""&&!(a.scrollHeight>a.clientHeight||a.scrollWidth>a.clientWidth);)a=a.parentElement;a.dataset.headlessuiPortal===""&&l.preventDefault()}else l.preventDefault()}},{passive:!1}),t.add(()=>{var l;let a=(l=window.scrollY)!=null?l:window.pageYOffset;s!==a&&window.scrollTo(0,s),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function T2(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function O2(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let dr=x2(()=>new Map,{PUSH(e,t){var n;let r=(n=this.get(e))!=null?n:{doc:e,count:0,d:jo(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r={doc:e,d:t,meta:O2(n)},o=[C2(),E2(),T2()];o.forEach(({before:s})=>s==null?void 0:s(r)),o.forEach(({after:s})=>s==null?void 0:s(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});dr.subscribe(()=>{let e=dr.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let r=t.get(n.doc)==="hidden",o=n.count!==0;(o&&!r||!o&&r)&&dr.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),n.count===0&&dr.dispatch("TEARDOWN",n)}});function P2(e,t,n){let r=A2(dr),o=O(()=>{let s=e.value?r.value.get(e.value):void 0;return s?s.count>0:!1});return ut([e,t],([s,i],[l],a)=>{if(!s||!i)return;dr.dispatch("PUSH",s,n);let u=!1;a(()=>{u||(dr.dispatch("POP",l??s,n),u=!0)})},{immediate:!0}),o}let Ji=new Map,eo=new Map;function vc(e,t=F(!0)){it(n=>{var r;if(!t.value)return;let o=q(e);if(!o)return;n(function(){var i;if(!o)return;let l=(i=eo.get(o))!=null?i:1;if(l===1?eo.delete(o):eo.set(o,l-1),l!==1)return;let a=Ji.get(o);a&&(a["aria-hidden"]===null?o.removeAttribute("aria-hidden"):o.setAttribute("aria-hidden",a["aria-hidden"]),o.inert=a.inert,Ji.delete(o))});let s=(r=eo.get(o))!=null?r:0;eo.set(o,s+1),s===0&&(Ji.set(o,{"aria-hidden":o.getAttribute("aria-hidden"),inert:o.inert}),o.setAttribute("aria-hidden","true"),o.inert=!0)})}function v0({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){let r=F(null),o=Yt(r);function s(){var i,l,a;let u=[];for(let c of e)c!==null&&(c instanceof HTMLElement?u.push(c):"value"in c&&c.value instanceof HTMLElement&&u.push(c.value));if(t!=null&&t.value)for(let c of t.value)u.push(c);for(let c of(i=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?i:[])c!==document.body&&c!==document.head&&c instanceof HTMLElement&&c.id!=="headlessui-portal-root"&&(c.contains(q(r))||c.contains((a=(l=q(r))==null?void 0:l.getRootNode())==null?void 0:a.host)||u.some(f=>c.contains(f))||u.push(c));return u}return{resolveContainers:s,contains(i){return s().some(l=>l.contains(i))},mainTreeNodeRef:r,MainTreeNode(){return n!=null?null:Oe(Cn,{features:En.Hidden,ref:r})}}}let m0=Symbol("ForcePortalRootContext");function R2(){return ae(m0,!1)}let mc=_e({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:t,attrs:n}){return st(m0,e.force),()=>{let{force:r,...o}=e;return lt({theirProps:o,ourProps:{},slot:{},slots:t,attrs:n,name:"ForcePortalRoot"})}}}),g0=Symbol("StackContext");var Rl=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(Rl||{});function M2(){return ae(g0,()=>{})}function k2({type:e,enabled:t,element:n,onUpdate:r}){let o=M2();function s(...i){r==null||r(...i),o(...i)}We(()=>{ut(t,(i,l)=>{i?s(0,e,n):l===!0&&s(1,e,n)},{immediate:!0,flush:"sync"})}),pt(()=>{t.value&&s(1,e,n)}),st(g0,s)}let L2=Symbol("DescriptionContext");function y0({slot:e=F({}),name:t="Description",props:n={}}={}){let r=F([]);function o(s){return r.value.push(s),()=>{let i=r.value.indexOf(s);i!==-1&&r.value.splice(i,1)}}return st(L2,{register:o,slot:e,name:t,props:n}),O(()=>r.value.length>0?r.value.join(" "):void 0)}function I2(e){let t=Yt(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let n=t.getElementById("headlessui-portal-root");if(n)return n;let r=t.createElement("div");return r.setAttribute("id","headlessui-portal-root"),t.body.appendChild(r)}const Ml=new WeakMap;function N2(e){var t;return(t=Ml.get(e))!=null?t:0}function gc(e,t){let n=t(N2(e));return n<=0?Ml.delete(e):Ml.set(e,n),n}let H2=_e({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let r=F(null),o=O(()=>Yt(r)),s=R2(),i=ae(w0,null),l=F(s===!0||i==null?I2(r.value):i.resolveTarget());l.value&&gc(l.value,d=>d+1);let a=F(!1);We(()=>{a.value=!0}),it(()=>{s||i!=null&&(l.value=i.resolveTarget())});let u=ae(kl,null),c=!1,f=$r();return ut(r,()=>{if(c||!u)return;let d=q(r);d&&(pt(u.register(d),f),c=!0)}),pt(()=>{var d,p;let v=(d=o.value)==null?void 0:d.getElementById("headlessui-portal-root");!v||l.value!==v||gc(l.value,m=>m-1)||l.value.children.length>0||(p=l.value.parentElement)==null||p.removeChild(l.value)}),()=>{if(!a.value||l.value===null)return null;let d={ref:r,"data-headlessui-portal":""};return Oe(ph,{to:l.value},lt({ourProps:d,theirProps:e,slot:{},attrs:n,slots:t,name:"Portal"}))}}}),kl=Symbol("PortalParentContext");function b0(){let e=ae(kl,null),t=F([]);function n(s){return t.value.push(s),e&&e.register(s),()=>r(s)}function r(s){let i=t.value.indexOf(s);i!==-1&&t.value.splice(i,1),e&&e.unregister(s)}let o={register:n,unregister:r,portals:t};return[t,_e({name:"PortalWrapper",setup(s,{slots:i}){return st(kl,o),()=>{var l;return(l=i.default)==null?void 0:l.call(i)}}})]}let w0=Symbol("PortalGroupContext"),F2=_e({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:n}){let r=_t({resolveTarget(){return e.target}});return st(w0,r),()=>{let{target:o,...s}=e;return lt({theirProps:s,ourProps:{},slot:{},attrs:t,slots:n,name:"PortalGroup"})}}});var V2=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(V2||{});let yc=Symbol("DialogContext"),as="DC8F892D-2EBD-447C-A4C8-A03058436FF4",qo=_e({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:as},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:n,slots:r,expose:o}){var s,i;let l=(s=e.id)!=null?s:`headlessui-dialog-${Nt()}`,a=F(!1);We(()=>{a.value=!0});let u=!1,c=O(()=>e.role==="dialog"||e.role==="alertdialog"?e.role:(u||(u=!0,console.warn(`Invalid role [${c}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),f=F(0),d=Zo(),p=O(()=>e.open===as&&d!==null?(d.value&at.Open)===at.Open:e.open),v=F(null),m=O(()=>Yt(v));if(o({el:v,$el:v}),!(e.open!==as||d!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof p.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${p.value===as?void 0:e.open}`);let g=O(()=>a.value&&p.value?0:1),b=O(()=>g.value===0),y=O(()=>f.value>1),S=ae(yc,null)!==null,[A,$]=b0(),{resolveContainers:I,mainTreeNodeRef:H,MainTreeNode:U}=v0({portals:A,defaultContainers:[O(()=>{var Ae;return(Ae=K.panelRef.value)!=null?Ae:v.value})]}),N=O(()=>y.value?"parent":"leaf"),D=O(()=>d!==null?(d.value&at.Closing)===at.Closing:!1),se=O(()=>S||D.value?!1:b.value),G=O(()=>{var Ae,Re,Xe;return(Xe=Array.from((Re=(Ae=m.value)==null?void 0:Ae.querySelectorAll("body > *"))!=null?Re:[]).find(Fe=>Fe.id==="headlessui-portal-root"?!1:Fe.contains(q(H))&&Fe instanceof HTMLElement))!=null?Xe:null});vc(G,se);let le=O(()=>y.value?!0:b.value),fe=O(()=>{var Ae,Re,Xe;return(Xe=Array.from((Re=(Ae=m.value)==null?void 0:Ae.querySelectorAll("[data-headlessui-portal]"))!=null?Re:[]).find(Fe=>Fe.contains(q(H))&&Fe instanceof HTMLElement))!=null?Xe:null});vc(fe,le),k2({type:"Dialog",enabled:O(()=>g.value===0),element:v,onUpdate:(Ae,Re)=>{if(Re==="Dialog")return ct(Ae,{[Rl.Add]:()=>f.value+=1,[Rl.Remove]:()=>f.value-=1})}});let Se=y0({name:"DialogDescription",slot:O(()=>({open:p.value}))}),re=F(null),K={titleId:re,panelRef:F(null),dialogState:g,setTitleId(Ae){re.value!==Ae&&(re.value=Ae)},close(){t("close",!1)}};st(yc,K);let me=O(()=>!(!b.value||y.value));La(I,(Ae,Re)=>{Ae.preventDefault(),K.close(),et(()=>Re==null?void 0:Re.focus())},me);let De=O(()=>!(y.value||g.value!==0));Na((i=m.value)==null?void 0:i.defaultView,"keydown",Ae=>{De.value&&(Ae.defaultPrevented||Ae.key===ke.Escape&&(Ae.preventDefault(),Ae.stopPropagation(),K.close()))});let ht=O(()=>!(D.value||g.value!==0||S));return P2(m,ht,Ae=>{var Re;return{containers:[...(Re=Ae.containers)!=null?Re:[],I]}}),it(Ae=>{if(g.value!==0)return;let Re=q(v);if(!Re)return;let Xe=new ResizeObserver(Fe=>{for(let Zt of Fe){let Q=Zt.target.getBoundingClientRect();Q.x===0&&Q.y===0&&Q.width===0&&Q.height===0&&K.close()}});Xe.observe(Re),Ae(()=>Xe.disconnect())}),()=>{let{open:Ae,initialFocus:Re,...Xe}=e,Fe={...n,ref:v,id:l,role:c.value,"aria-modal":g.value===0?!0:void 0,"aria-labelledby":re.value,"aria-describedby":Se.value},Zt={open:g.value===0};return Oe(mc,{force:!0},()=>[Oe(H2,()=>Oe(F2,{target:v.value},()=>Oe(mc,{force:!1},()=>Oe(Qr,{initialFocus:Re,containers:I,features:b.value?ct(N.value,{parent:Qr.features.RestoreFocus,leaf:Qr.features.All&~Qr.features.FocusLock}):Qr.features.None},()=>Oe($,{},()=>lt({ourProps:Fe,theirProps:{...Xe,...n},slot:Zt,attrs:n,slots:r,visible:g.value===0,features:mn.RenderStrategy|mn.Static,name:"Dialog"})))))),Oe(U)])}}}),bc=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function wc(e){var t,n;let r=(t=e.innerText)!=null?t:"",o=e.cloneNode(!0);if(!(o instanceof HTMLElement))return r;let s=!1;for(let l of o.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))l.remove(),s=!0;let i=s?(n=o.innerText)!=null?n:"":r;return bc.test(i)&&(i=i.replace(bc,"")),i}function D2(e){let t=e.getAttribute("aria-label");if(typeof t=="string")return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let r=n.split(" ").map(o=>{let s=document.getElementById(o);if(s){let i=s.getAttribute("aria-label");return typeof i=="string"?i.trim():wc(s).trim()}return null}).filter(Boolean);if(r.length>0)return r.join(", ")}return wc(e).trim()}function B2(e){let t=F(""),n=F("");return()=>{let r=q(e);if(!r)return"";let o=r.innerText;if(t.value===o)return n.value;let s=D2(r).trim().toLowerCase();return t.value=o,n.value=s,s}}var j2=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(j2||{}),z2=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(z2||{});function U2(e){requestAnimationFrame(()=>requestAnimationFrame(e))}let _0=Symbol("MenuContext");function di(e){let t=ae(_0,null);if(t===null){let n=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,di),n}return t}let S0=_e({name:"Menu",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:n}){let r=F(1),o=F(null),s=F(null),i=F([]),l=F(""),a=F(null),u=F(1);function c(d=p=>p){let p=a.value!==null?i.value[a.value]:null,v=ur(d(i.value.slice()),g=>q(g.dataRef.domRef)),m=p?v.indexOf(p):null;return m===-1&&(m=null),{items:v,activeItemIndex:m}}let f={menuState:r,buttonRef:o,itemsRef:s,items:i,searchQuery:l,activeItemIndex:a,activationTrigger:u,closeMenu:()=>{r.value=1,a.value=null},openMenu:()=>r.value=0,goToItem(d,p,v){let m=c(),g=y2(d===jt.Specific?{focus:jt.Specific,id:p}:{focus:d},{resolveItems:()=>m.items,resolveActiveIndex:()=>m.activeItemIndex,resolveId:b=>b.id,resolveDisabled:b=>b.dataRef.disabled});l.value="",a.value=g,u.value=v??1,i.value=m.items},search(d){let p=l.value!==""?0:1;l.value+=d.toLowerCase();let v=(a.value!==null?i.value.slice(a.value+p).concat(i.value.slice(0,a.value+p)):i.value).find(g=>g.dataRef.textValue.startsWith(l.value)&&!g.dataRef.disabled),m=v?i.value.indexOf(v):-1;m===-1||m===a.value||(a.value=m,u.value=1)},clearSearch(){l.value=""},registerItem(d,p){let v=c(m=>[...m,{id:d,dataRef:p}]);i.value=v.items,a.value=v.activeItemIndex,u.value=1},unregisterItem(d){let p=c(v=>{let m=v.findIndex(g=>g.id===d);return m!==-1&&v.splice(m,1),v});i.value=p.items,a.value=p.activeItemIndex,u.value=1}};return La([o,s],(d,p)=>{var v;f.closeMenu(),ai(p,li.Loose)||(d.preventDefault(),(v=q(o))==null||v.focus())},O(()=>r.value===0)),st(_0,f),Ia(O(()=>ct(r.value,{0:at.Open,1:at.Closed}))),()=>{let d={open:r.value===0,close:f.closeMenu};return lt({ourProps:{},theirProps:e,slot:d,slots:t,attrs:n,name:"Menu"})}}}),$0=_e({name:"MenuButton",props:{disabled:{type:Boolean,default:!1},as:{type:[Object,String],default:"button"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-menu-button-${Nt()}`,i=di("MenuButton");r({el:i.buttonRef,$el:i.buttonRef});function l(f){switch(f.key){case ke.Space:case ke.Enter:case ke.ArrowDown:f.preventDefault(),f.stopPropagation(),i.openMenu(),et(()=>{var d;(d=q(i.itemsRef))==null||d.focus({preventScroll:!0}),i.goToItem(jt.First)});break;case ke.ArrowUp:f.preventDefault(),f.stopPropagation(),i.openMenu(),et(()=>{var d;(d=q(i.itemsRef))==null||d.focus({preventScroll:!0}),i.goToItem(jt.Last)});break}}function a(f){switch(f.key){case ke.Space:f.preventDefault();break}}function u(f){e.disabled||(i.menuState.value===0?(i.closeMenu(),et(()=>{var d;return(d=q(i.buttonRef))==null?void 0:d.focus({preventScroll:!0})})):(f.preventDefault(),i.openMenu(),U2(()=>{var d;return(d=q(i.itemsRef))==null?void 0:d.focus({preventScroll:!0})})))}let c=ui(O(()=>({as:e.as,type:t.type})),i.buttonRef);return()=>{var f;let d={open:i.menuState.value===0},{...p}=e,v={ref:i.buttonRef,id:s,type:c.value,"aria-haspopup":"menu","aria-controls":(f=q(i.itemsRef))==null?void 0:f.id,"aria-expanded":i.menuState.value===0,onKeydown:l,onKeyup:a,onClick:u};return lt({ourProps:v,theirProps:p,slot:d,attrs:t,slots:n,name:"MenuButton"})}}}),A0=_e({name:"MenuItems",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-menu-items-${Nt()}`,i=di("MenuItems"),l=F(null);r({el:i.itemsRef,$el:i.itemsRef}),f2({container:O(()=>q(i.itemsRef)),enabled:O(()=>i.menuState.value===0),accept(d){return d.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:d.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(d){d.setAttribute("role","none")}});function a(d){var p;switch(l.value&&clearTimeout(l.value),d.key){case ke.Space:if(i.searchQuery.value!=="")return d.preventDefault(),d.stopPropagation(),i.search(d.key);case ke.Enter:if(d.preventDefault(),d.stopPropagation(),i.activeItemIndex.value!==null){let v=i.items.value[i.activeItemIndex.value];(p=q(v.dataRef.domRef))==null||p.click()}i.closeMenu(),i0(q(i.buttonRef));break;case ke.ArrowDown:return d.preventDefault(),d.stopPropagation(),i.goToItem(jt.Next);case ke.ArrowUp:return d.preventDefault(),d.stopPropagation(),i.goToItem(jt.Previous);case ke.Home:case ke.PageUp:return d.preventDefault(),d.stopPropagation(),i.goToItem(jt.First);case ke.End:case ke.PageDown:return d.preventDefault(),d.stopPropagation(),i.goToItem(jt.Last);case ke.Escape:d.preventDefault(),d.stopPropagation(),i.closeMenu(),et(()=>{var v;return(v=q(i.buttonRef))==null?void 0:v.focus({preventScroll:!0})});break;case ke.Tab:d.preventDefault(),d.stopPropagation(),i.closeMenu(),et(()=>a2(q(i.buttonRef),d.shiftKey?qe.Previous:qe.Next));break;default:d.key.length===1&&(i.search(d.key),l.value=setTimeout(()=>i.clearSearch(),350));break}}function u(d){switch(d.key){case ke.Space:d.preventDefault();break}}let c=Zo(),f=O(()=>c!==null?(c.value&at.Open)===at.Open:i.menuState.value===0);return()=>{var d,p;let v={open:i.menuState.value===0},{...m}=e,g={"aria-activedescendant":i.activeItemIndex.value===null||(d=i.items.value[i.activeItemIndex.value])==null?void 0:d.id,"aria-labelledby":(p=q(i.buttonRef))==null?void 0:p.id,id:s,onKeydown:a,onKeyup:u,role:"menu",tabIndex:0,ref:i.itemsRef};return lt({ourProps:g,theirProps:m,slot:v,attrs:t,slots:n,features:mn.RenderStrategy|mn.Static,visible:f.value,name:"MenuItems"})}}}),Ir=_e({name:"MenuItem",inheritAttrs:!1,props:{as:{type:[Object,String],default:"template"},disabled:{type:Boolean,default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-menu-item-${Nt()}`,i=di("MenuItem"),l=F(null);r({el:l,$el:l});let a=O(()=>i.activeItemIndex.value!==null?i.items.value[i.activeItemIndex.value].id===s:!1),u=B2(l),c=O(()=>({disabled:e.disabled,get textValue(){return u()},domRef:l}));We(()=>i.registerItem(s,c)),pt(()=>i.unregisterItem(s)),it(()=>{i.menuState.value===0&&a.value&&i.activationTrigger.value!==0&&et(()=>{var b,y;return(y=(b=q(l))==null?void 0:b.scrollIntoView)==null?void 0:y.call(b,{block:"nearest"})})});function f(b){if(e.disabled)return b.preventDefault();i.closeMenu(),i0(q(i.buttonRef))}function d(){if(e.disabled)return i.goToItem(jt.Nothing);i.goToItem(jt.Specific,s)}let p=d2();function v(b){p.update(b)}function m(b){p.wasMoved(b)&&(e.disabled||a.value||i.goToItem(jt.Specific,s,0))}function g(b){p.wasMoved(b)&&(e.disabled||a.value&&i.goToItem(jt.Nothing))}return()=>{let{disabled:b,...y}=e,S={active:a.value,disabled:b,close:i.closeMenu};return lt({ourProps:{id:s,ref:l,role:"menuitem",tabIndex:b===!0?void 0:-1,"aria-disabled":b===!0?!0:void 0,onClick:f,onFocus:d,onPointerenter:v,onMouseenter:v,onPointermove:m,onMousemove:m,onPointerleave:g,onMouseleave:g},theirProps:{...n,...y},slot:S,attrs:n,slots:t,name:"MenuItem"})}}});var Z2=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Z2||{});let x0=Symbol("PopoverContext");function Fa(e){let t=ae(x0,null);if(t===null){let n=new Error(`<${e} /> is missing a parent <${T0.name} /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Fa),n}return t}let q2=Symbol("PopoverGroupContext");function E0(){return ae(q2,null)}let C0=Symbol("PopoverPanelContext");function W2(){return ae(C0,null)}let T0=_e({name:"Popover",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n,expose:r}){var o;let s=F(null);r({el:s,$el:s});let i=F(1),l=F(null),a=F(null),u=F(null),c=F(null),f=O(()=>Yt(s)),d=O(()=>{var $,I;if(!q(l)||!q(c))return!1;for(let le of document.querySelectorAll("body > *"))if(Number(le==null?void 0:le.contains(q(l)))^Number(le==null?void 0:le.contains(q(c))))return!0;let H=Uo(),U=H.indexOf(q(l)),N=(U+H.length-1)%H.length,D=(U+1)%H.length,se=H[N],G=H[D];return!(($=q(c))!=null&&$.contains(se))&&!((I=q(c))!=null&&I.contains(G))}),p={popoverState:i,buttonId:F(null),panelId:F(null),panel:c,button:l,isPortalled:d,beforePanelSentinel:a,afterPanelSentinel:u,togglePopover(){i.value=ct(i.value,{0:1,1:0})},closePopover(){i.value!==1&&(i.value=1)},close($){p.closePopover();let I=$?$ instanceof HTMLElement?$:$.value instanceof HTMLElement?q($):q(p.button):q(p.button);I==null||I.focus()}};st(x0,p),Ia(O(()=>ct(i.value,{0:at.Open,1:at.Closed})));let v={buttonId:p.buttonId,panelId:p.panelId,close(){p.closePopover()}},m=E0(),g=m==null?void 0:m.registerPopover,[b,y]=b0(),S=v0({mainTreeNodeRef:m==null?void 0:m.mainTreeNodeRef,portals:b,defaultContainers:[l,c]});function A(){var $,I,H,U;return(U=m==null?void 0:m.isFocusWithinPopoverGroup())!=null?U:(($=f.value)==null?void 0:$.activeElement)&&(((I=q(l))==null?void 0:I.contains(f.value.activeElement))||((H=q(c))==null?void 0:H.contains(f.value.activeElement)))}return it(()=>g==null?void 0:g(v)),Na((o=f.value)==null?void 0:o.defaultView,"focus",$=>{var I,H;$.target!==window&&$.target instanceof HTMLElement&&i.value===0&&(A()||l&&c&&(S.contains($.target)||(I=q(p.beforePanelSentinel))!=null&&I.contains($.target)||(H=q(p.afterPanelSentinel))!=null&&H.contains($.target)||p.closePopover()))},!0),La(S.resolveContainers,($,I)=>{var H;p.closePopover(),ai(I,li.Loose)||($.preventDefault(),(H=q(l))==null||H.focus())},O(()=>i.value===0)),()=>{let $={open:i.value===0,close:p.close};return Oe(he,[Oe(y,{},()=>lt({theirProps:{...e,...n},ourProps:{ref:s},slot:$,slots:t,attrs:n,name:"Popover"})),Oe(S.MainTreeNode)])}}}),G2=_e({name:"PopoverButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-popover-button-${Nt()}`,i=Fa("PopoverButton"),l=O(()=>Yt(i.button));r({el:i.button,$el:i.button}),We(()=>{i.buttonId.value=s}),pt(()=>{i.buttonId.value=null});let a=E0(),u=a==null?void 0:a.closeOthers,c=W2(),f=O(()=>c===null?!1:c.value===i.panelId.value),d=F(null),p=`headlessui-focus-sentinel-${Nt()}`;f.value||it(()=>{i.button.value=q(d)});let v=ui(O(()=>({as:e.as,type:t.type})),d);function m($){var I,H,U,N,D;if(f.value){if(i.popoverState.value===1)return;switch($.key){case ke.Space:case ke.Enter:$.preventDefault(),(H=(I=$.target).click)==null||H.call(I),i.closePopover(),(U=q(i.button))==null||U.focus();break}}else switch($.key){case ke.Space:case ke.Enter:$.preventDefault(),$.stopPropagation(),i.popoverState.value===1&&(u==null||u(i.buttonId.value)),i.togglePopover();break;case ke.Escape:if(i.popoverState.value!==0)return u==null?void 0:u(i.buttonId.value);if(!q(i.button)||(N=l.value)!=null&&N.activeElement&&!((D=q(i.button))!=null&&D.contains(l.value.activeElement)))return;$.preventDefault(),$.stopPropagation(),i.closePopover();break}}function g($){f.value||$.key===ke.Space&&$.preventDefault()}function b($){var I,H;e.disabled||(f.value?(i.closePopover(),(I=q(i.button))==null||I.focus()):($.preventDefault(),$.stopPropagation(),i.popoverState.value===1&&(u==null||u(i.buttonId.value)),i.togglePopover(),(H=q(i.button))==null||H.focus()))}function y($){$.preventDefault(),$.stopPropagation()}let S=Ha();function A(){let $=q(i.panel);if(!$)return;function I(){ct(S.value,{[zt.Forwards]:()=>vt($,qe.First),[zt.Backwards]:()=>vt($,qe.Last)})===An.Error&&vt(Uo().filter(H=>H.dataset.headlessuiFocusGuard!=="true"),ct(S.value,{[zt.Forwards]:qe.Next,[zt.Backwards]:qe.Previous}),{relativeTo:q(i.button)})}I()}return()=>{let $=i.popoverState.value===0,I={open:$},{...H}=e,U=f.value?{ref:d,type:v.value,onKeydown:m,onClick:b}:{ref:d,id:s,type:v.value,"aria-expanded":i.popoverState.value===0,"aria-controls":q(i.panel)?i.panelId.value:void 0,disabled:e.disabled?!0:void 0,onKeydown:m,onKeyup:g,onClick:b,onMousedown:y};return Oe(he,[lt({ourProps:U,theirProps:{...t,...H},slot:I,attrs:t,slots:n,name:"PopoverButton"}),$&&!f.value&&i.isPortalled.value&&Oe(Cn,{id:p,features:En.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:A})])}}}),K2=_e({name:"PopoverPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-popover-panel-${Nt()}`,{focus:i}=e,l=Fa("PopoverPanel"),a=O(()=>Yt(l.panel)),u=`headlessui-focus-sentinel-before-${Nt()}`,c=`headlessui-focus-sentinel-after-${Nt()}`;r({el:l.panel,$el:l.panel}),We(()=>{l.panelId.value=s}),pt(()=>{l.panelId.value=null}),st(C0,l.panelId),it(()=>{var y,S;if(!i||l.popoverState.value!==0||!l.panel)return;let A=(y=a.value)==null?void 0:y.activeElement;(S=q(l.panel))!=null&&S.contains(A)||vt(q(l.panel),qe.First)});let f=Zo(),d=O(()=>f!==null?(f.value&at.Open)===at.Open:l.popoverState.value===0);function p(y){var S,A;switch(y.key){case ke.Escape:if(l.popoverState.value!==0||!q(l.panel)||a.value&&!((S=q(l.panel))!=null&&S.contains(a.value.activeElement)))return;y.preventDefault(),y.stopPropagation(),l.closePopover(),(A=q(l.button))==null||A.focus();break}}function v(y){var S,A,$,I,H;let U=y.relatedTarget;U&&q(l.panel)&&((S=q(l.panel))!=null&&S.contains(U)||(l.closePopover(),(($=(A=q(l.beforePanelSentinel))==null?void 0:A.contains)!=null&&$.call(A,U)||(H=(I=q(l.afterPanelSentinel))==null?void 0:I.contains)!=null&&H.call(I,U))&&U.focus({preventScroll:!0})))}let m=Ha();function g(){let y=q(l.panel);if(!y)return;function S(){ct(m.value,{[zt.Forwards]:()=>{var A;vt(y,qe.First)===An.Error&&((A=q(l.afterPanelSentinel))==null||A.focus())},[zt.Backwards]:()=>{var A;(A=q(l.button))==null||A.focus({preventScroll:!0})}})}S()}function b(){let y=q(l.panel);if(!y)return;function S(){ct(m.value,{[zt.Forwards]:()=>{let A=q(l.button),$=q(l.panel);if(!A)return;let I=Uo(),H=I.indexOf(A),U=I.slice(0,H+1),N=[...I.slice(H+1),...U];for(let D of N.slice())if(D.dataset.headlessuiFocusGuard==="true"||$!=null&&$.contains(D)){let se=N.indexOf(D);se!==-1&&N.splice(se,1)}vt(N,qe.First,{sorted:!1})},[zt.Backwards]:()=>{var A;vt(y,qe.Previous)===An.Error&&((A=q(l.button))==null||A.focus())}})}S()}return()=>{let y={open:l.popoverState.value===0,close:l.close},{focus:S,...A}=e,$={ref:l.panel,id:s,onKeydown:p,onFocusout:i&&l.popoverState.value===0?v:void 0,tabIndex:-1};return lt({ourProps:$,theirProps:{...t,...A},attrs:t,slot:y,slots:{...n,default:(...I)=>{var H;return[Oe(he,[d.value&&l.isPortalled.value&&Oe(Cn,{id:u,ref:l.beforePanelSentinel,features:En.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:g}),(H=n.default)==null?void 0:H.call(n,...I),d.value&&l.isPortalled.value&&Oe(Cn,{id:c,ref:l.afterPanelSentinel,features:En.Focusable,"data-headlessui-focus-guard":!0,as:"button",type:"button",onFocus:b})])]}},features:mn.RenderStrategy|mn.Static,visible:d.value,name:"PopoverPanel"})}}}),O0=Symbol("LabelContext");function P0(){let e=ae(O0,null);if(e===null){let t=new Error("You used a <Label /> component, but it is not inside a parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,P0),t}return e}function Y2({slot:e={},name:t="Label",props:n={}}={}){let r=F([]);function o(s){return r.value.push(s),()=>{let i=r.value.indexOf(s);i!==-1&&r.value.splice(i,1)}}return st(O0,{register:o,slot:e,name:t,props:n}),O(()=>r.value.length>0?r.value.join(" "):void 0)}let J2=_e({name:"Label",props:{as:{type:[Object,String],default:"label"},passive:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{slots:t,attrs:n}){var r;let o=(r=e.id)!=null?r:`headlessui-label-${Nt()}`,s=P0();return We(()=>pt(s.register(o))),()=>{let{name:i="Label",slot:l={},props:a={}}=s,{passive:u,...c}=e,f={...Object.entries(a).reduce((d,[p,v])=>Object.assign(d,{[p]:R(v)}),{}),id:o};return u&&(delete f.onClick,delete f.htmlFor,delete c.onClick),lt({ourProps:f,theirProps:c,slot:l,attrs:n,slots:t,name:i})}}}),R0=Symbol("GroupContext"),X2=_e({name:"SwitchGroup",props:{as:{type:[Object,String],default:"template"}},setup(e,{slots:t,attrs:n}){let r=F(null),o=Y2({name:"SwitchLabel",props:{htmlFor:O(()=>{var i;return(i=r.value)==null?void 0:i.id}),onClick(i){r.value&&(i.currentTarget.tagName==="LABEL"&&i.preventDefault(),r.value.click(),r.value.focus({preventScroll:!0}))}}}),s=y0({name:"SwitchDescription"});return st(R0,{switchRef:r,labelledby:o,describedby:s}),()=>lt({theirProps:e,ourProps:{},slot:{},slots:t,attrs:n,name:"SwitchGroup"})}}),Q2=_e({name:"Switch",emits:{"update:modelValue":e=>!0},props:{as:{type:[Object,String],default:"button"},modelValue:{type:Boolean,default:void 0},defaultChecked:{type:Boolean,optional:!0},form:{type:String,optional:!0},name:{type:String,optional:!0},value:{type:String,optional:!0},id:{type:String,default:null},disabled:{type:Boolean,default:!1},tabIndex:{type:Number,default:0}},inheritAttrs:!1,setup(e,{emit:t,attrs:n,slots:r,expose:o}){var s;let i=(s=e.id)!=null?s:`headlessui-switch-${Nt()}`,l=ae(R0,null),[a,u]=Xm(O(()=>e.modelValue),y=>t("update:modelValue",y),O(()=>e.defaultChecked));function c(){u(!a.value)}let f=F(null),d=l===null?f:l.switchRef,p=ui(O(()=>({as:e.as,type:n.type})),d);o({el:d,$el:d});function v(y){y.preventDefault(),c()}function m(y){y.key===ke.Space?(y.preventDefault(),c()):y.key===ke.Enter&&b2(y.currentTarget)}function g(y){y.preventDefault()}let b=O(()=>{var y,S;return(S=(y=q(d))==null?void 0:y.closest)==null?void 0:S.call(y,"form")});return We(()=>{ut([b],()=>{if(!b.value||e.defaultChecked===void 0)return;function y(){u(e.defaultChecked)}return b.value.addEventListener("reset",y),()=>{var S;(S=b.value)==null||S.removeEventListener("reset",y)}},{immediate:!0})}),()=>{let{name:y,value:S,form:A,tabIndex:$,...I}=e,H={checked:a.value},U={id:i,ref:d,role:"switch",type:p.value,tabIndex:$===-1?0:$,"aria-checked":a.value,"aria-labelledby":l==null?void 0:l.labelledby.value,"aria-describedby":l==null?void 0:l.describedby.value,onClick:v,onKeyup:m,onKeypress:g};return Oe(he,[y!=null&&a.value!=null?Oe(Cn,p2({features:En.Hidden,as:"input",type:"checkbox",hidden:!0,readOnly:!0,checked:a.value,form:A,disabled:I.disabled,name:y,value:S})):null,lt({ourProps:U,theirProps:{...n,...ci(I,["modelValue","defaultChecked"])},slot:H,attrs:n,slots:r,name:"Switch"})])}}}),_c=J2,eg=_e({props:{onFocus:{type:Function,required:!0}},setup(e){let t=F(!0);return()=>t.value?Oe(Cn,{as:"button",type:"button",features:En.Focusable,onFocus(n){n.preventDefault();let r,o=50;function s(){var i;if(o--<=0){r&&cancelAnimationFrame(r);return}if((i=e.onFocus)!=null&&i.call(e)){t.value=!1,cancelAnimationFrame(r);return}r=requestAnimationFrame(s)}r=requestAnimationFrame(s)}}):null}});var tg=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(tg||{}),ng=(e=>(e[e.Less=-1]="Less",e[e.Equal=0]="Equal",e[e.Greater=1]="Greater",e))(ng||{});let M0=Symbol("TabsContext");function Wo(e){let t=ae(M0,null);if(t===null){let n=new Error(`<${e} /> is missing a parent <TabGroup /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,Wo),n}return t}let Va=Symbol("TabsSSRContext"),k0=_e({name:"TabGroup",emits:{change:e=>!0},props:{as:{type:[Object,String],default:"template"},selectedIndex:{type:[Number],default:null},defaultIndex:{type:[Number],default:0},vertical:{type:[Boolean],default:!1},manual:{type:[Boolean],default:!1}},inheritAttrs:!1,setup(e,{slots:t,attrs:n,emit:r}){var o;let s=F((o=e.selectedIndex)!=null?o:e.defaultIndex),i=F([]),l=F([]),a=O(()=>e.selectedIndex!==null),u=O(()=>a.value?e.selectedIndex:s.value);function c(m){var g;let b=ur(f.tabs.value,q),y=ur(f.panels.value,q),S=b.filter(A=>{var $;return!(($=q(A))!=null&&$.hasAttribute("disabled"))});if(m<0||m>b.length-1){let A=ct(s.value===null?0:Math.sign(m-s.value),{[-1]:()=>1,0:()=>ct(Math.sign(m),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0}),$=ct(A,{0:()=>b.indexOf(S[0]),1:()=>b.indexOf(S[S.length-1])});$!==-1&&(s.value=$),f.tabs.value=b,f.panels.value=y}else{let A=b.slice(0,m),$=[...b.slice(m),...A].find(H=>S.includes(H));if(!$)return;let I=(g=b.indexOf($))!=null?g:f.selectedIndex.value;I===-1&&(I=f.selectedIndex.value),s.value=I,f.tabs.value=b,f.panels.value=y}}let f={selectedIndex:O(()=>{var m,g;return(g=(m=s.value)!=null?m:e.defaultIndex)!=null?g:null}),orientation:O(()=>e.vertical?"vertical":"horizontal"),activation:O(()=>e.manual?"manual":"auto"),tabs:i,panels:l,setSelectedIndex(m){u.value!==m&&r("change",m),a.value||c(m)},registerTab(m){var g;if(i.value.includes(m))return;let b=i.value[s.value];if(i.value.push(m),i.value=ur(i.value,q),!a.value){let y=(g=i.value.indexOf(b))!=null?g:s.value;y!==-1&&(s.value=y)}},unregisterTab(m){let g=i.value.indexOf(m);g!==-1&&i.value.splice(g,1)},registerPanel(m){l.value.includes(m)||(l.value.push(m),l.value=ur(l.value,q))},unregisterPanel(m){let g=l.value.indexOf(m);g!==-1&&l.value.splice(g,1)}};st(M0,f);let d=F({tabs:[],panels:[]}),p=F(!1);We(()=>{p.value=!0}),st(Va,O(()=>p.value?null:d.value));let v=O(()=>e.selectedIndex);return We(()=>{ut([v],()=>{var m;return c((m=e.selectedIndex)!=null?m:e.defaultIndex)},{immediate:!0})}),it(()=>{if(!a.value||u.value==null||f.tabs.value.length<=0)return;let m=ur(f.tabs.value,q);m.some((g,b)=>q(f.tabs.value[b])!==q(g))&&f.setSelectedIndex(m.findIndex(g=>q(g)===q(f.tabs.value[u.value])))}),()=>{let m={selectedIndex:s.value};return Oe(he,[i.value.length<=0&&Oe(eg,{onFocus:()=>{for(let g of i.value){let b=q(g);if((b==null?void 0:b.tabIndex)===0)return b.focus(),!0}return!1}}),lt({theirProps:{...n,...ci(e,["selectedIndex","defaultIndex","manual","vertical","onChange"])},ourProps:{},slot:m,slots:t,attrs:n,name:"TabGroup"})])}}}),L0=_e({name:"TabList",props:{as:{type:[Object,String],default:"div"}},setup(e,{attrs:t,slots:n}){let r=Wo("TabList");return()=>{let o={selectedIndex:r.selectedIndex.value},s={role:"tablist","aria-orientation":r.orientation.value};return lt({ourProps:s,theirProps:e,slot:o,attrs:t,slots:n,name:"TabList"})}}}),Or=_e({name:"Tab",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-tabs-tab-${Nt()}`,i=Wo("Tab"),l=F(null);r({el:l,$el:l}),We(()=>i.registerTab(l)),pt(()=>i.unregisterTab(l));let a=ae(Va),u=O(()=>{if(a.value){let y=a.value.tabs.indexOf(s);return y===-1?a.value.tabs.push(s)-1:y}return-1}),c=O(()=>{let y=i.tabs.value.indexOf(l);return y===-1?u.value:y}),f=O(()=>c.value===i.selectedIndex.value);function d(y){var S;let A=y();if(A===An.Success&&i.activation.value==="auto"){let $=(S=Yt(l))==null?void 0:S.activeElement,I=i.tabs.value.findIndex(H=>q(H)===$);I!==-1&&i.setSelectedIndex(I)}return A}function p(y){let S=i.tabs.value.map(A=>q(A)).filter(Boolean);if(y.key===ke.Space||y.key===ke.Enter){y.preventDefault(),y.stopPropagation(),i.setSelectedIndex(c.value);return}switch(y.key){case ke.Home:case ke.PageUp:return y.preventDefault(),y.stopPropagation(),d(()=>vt(S,qe.First));case ke.End:case ke.PageDown:return y.preventDefault(),y.stopPropagation(),d(()=>vt(S,qe.Last))}if(d(()=>ct(i.orientation.value,{vertical(){return y.key===ke.ArrowUp?vt(S,qe.Previous|qe.WrapAround):y.key===ke.ArrowDown?vt(S,qe.Next|qe.WrapAround):An.Error},horizontal(){return y.key===ke.ArrowLeft?vt(S,qe.Previous|qe.WrapAround):y.key===ke.ArrowRight?vt(S,qe.Next|qe.WrapAround):An.Error}}))===An.Success)return y.preventDefault()}let v=F(!1);function m(){var y;v.value||(v.value=!0,!e.disabled&&((y=q(l))==null||y.focus({preventScroll:!0}),i.setSelectedIndex(c.value),ii(()=>{v.value=!1})))}function g(y){y.preventDefault()}let b=ui(O(()=>({as:e.as,type:t.type})),l);return()=>{var y,S;let A={selected:f.value,disabled:(y=e.disabled)!=null?y:!1},{...$}=e,I={ref:l,onKeydown:p,onMousedown:g,onClick:m,id:s,role:"tab",type:b.value,"aria-controls":(S=q(i.panels.value[c.value]))==null?void 0:S.id,"aria-selected":f.value,tabIndex:f.value?0:-1,disabled:e.disabled?!0:void 0};return lt({ourProps:I,theirProps:$,slot:A,attrs:t,slots:n,name:"Tab"})}}}),I0=_e({name:"TabPanels",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let r=Wo("TabPanels");return()=>{let o={selectedIndex:r.selectedIndex.value};return lt({theirProps:e,ourProps:{},slot:o,attrs:n,slots:t,name:"TabPanels"})}}}),Pr=_e({name:"TabPanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:null},tabIndex:{type:Number,default:0}},setup(e,{attrs:t,slots:n,expose:r}){var o;let s=(o=e.id)!=null?o:`headlessui-tabs-panel-${Nt()}`,i=Wo("TabPanel"),l=F(null);r({el:l,$el:l}),We(()=>i.registerPanel(l)),pt(()=>i.unregisterPanel(l));let a=ae(Va),u=O(()=>{if(a.value){let d=a.value.panels.indexOf(s);return d===-1?a.value.panels.push(s)-1:d}return-1}),c=O(()=>{let d=i.panels.value.indexOf(l);return d===-1?u.value:d}),f=O(()=>c.value===i.selectedIndex.value);return()=>{var d;let p={selected:f.value},{tabIndex:v,...m}=e,g={ref:l,id:s,role:"tabpanel","aria-labelledby":(d=q(i.tabs.value[c.value]))==null?void 0:d.id,tabIndex:f.value?v:-1};return!f.value&&e.unmount&&!e.static?Oe(Cn,{as:"span","aria-hidden":!0,...g}):lt({ourProps:g,theirProps:m,slot:p,attrs:t,slots:n,features:mn.Static|mn.RenderStrategy,visible:f.value,name:"TabPanel"})}}});function rg(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}function Xi(e,...t){e&&t.length>0&&e.classList.add(...t)}function us(e,...t){e&&t.length>0&&e.classList.remove(...t)}var Ll=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(Ll||{});function og(e,t){let n=jo();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[s,i]=[r,o].map(l=>{let[a=0]=l.split(",").filter(Boolean).map(u=>u.includes("ms")?parseFloat(u):parseFloat(u)*1e3).sort((u,c)=>c-u);return a});return s!==0?n.setTimeout(()=>t("finished"),s+i):t("finished"),n.add(()=>t("cancelled")),n.dispose}function Sc(e,t,n,r,o,s){let i=jo(),l=s!==void 0?rg(s):()=>{};return us(e,...o),Xi(e,...t,...n),i.nextFrame(()=>{us(e,...n),Xi(e,...r),i.add(og(e,a=>(us(e,...r,...t),Xi(e,...o),l(a))))}),i.add(()=>us(e,...t,...n,...r,...o)),i.add(()=>l("cancelled")),i.dispose}function rr(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let Da=Symbol("TransitionContext");var sg=(e=>(e.Visible="visible",e.Hidden="hidden",e))(sg||{});function ig(){return ae(Da,null)!==null}function lg(){let e=ae(Da,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function ag(){let e=ae(Ba,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let Ba=Symbol("NestingContext");function fi(e){return"children"in e?fi(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function N0(e){let t=F([]),n=F(!1);We(()=>n.value=!0),pt(()=>n.value=!1);function r(s,i=Bn.Hidden){let l=t.value.findIndex(({id:a})=>a===s);l!==-1&&(ct(i,{[Bn.Unmount](){t.value.splice(l,1)},[Bn.Hidden](){t.value[l].state="hidden"}}),!fi(t)&&n.value&&(e==null||e()))}function o(s){let i=t.value.find(({id:l})=>l===s);return i?i.state!=="visible"&&(i.state="visible"):t.value.push({id:s,state:"visible"}),()=>r(s,Bn.Unmount)}return{children:t,register:o,unregister:r}}let H0=mn.RenderStrategy,sn=_e({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:r,expose:o}){let s=F(0);function i(){s.value|=at.Opening,t("beforeEnter")}function l(){s.value&=~at.Opening,t("afterEnter")}function a(){s.value|=at.Closing,t("beforeLeave")}function u(){s.value&=~at.Closing,t("afterLeave")}if(!ig()&&v2())return()=>Oe(Zr,{...e,onBeforeEnter:i,onAfterEnter:l,onBeforeLeave:a,onAfterLeave:u},r);let c=F(null),f=O(()=>e.unmount?Bn.Unmount:Bn.Hidden);o({el:c,$el:c});let{show:d,appear:p}=lg(),{register:v,unregister:m}=ag(),g=F(d.value?"visible":"hidden"),b={value:!0},y=Nt(),S={value:!1},A=N0(()=>{!S.value&&g.value!=="hidden"&&(g.value="hidden",m(y),u())});We(()=>{let le=v(y);pt(le)}),it(()=>{if(f.value===Bn.Hidden&&y){if(d.value&&g.value!=="visible"){g.value="visible";return}ct(g.value,{hidden:()=>m(y),visible:()=>v(y)})}});let $=rr(e.enter),I=rr(e.enterFrom),H=rr(e.enterTo),U=rr(e.entered),N=rr(e.leave),D=rr(e.leaveFrom),se=rr(e.leaveTo);We(()=>{it(()=>{if(g.value==="visible"){let le=q(c);if(le instanceof Comment&&le.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function G(le){let fe=b.value&&!p.value,Se=q(c);!Se||!(Se instanceof HTMLElement)||fe||(S.value=!0,d.value&&i(),d.value||a(),le(d.value?Sc(Se,$,I,H,U,re=>{S.value=!1,re===Ll.Finished&&l()}):Sc(Se,N,D,se,U,re=>{S.value=!1,re===Ll.Finished&&(fi(A)||(g.value="hidden",m(y),u()))})))}return We(()=>{ut([d],(le,fe,Se)=>{G(Se),b.value=!1},{immediate:!0})}),st(Ba,A),Ia(O(()=>ct(g.value,{visible:at.Open,hidden:at.Closed})|s.value)),()=>{let{appear:le,show:fe,enter:Se,enterFrom:re,enterTo:K,entered:me,leave:De,leaveFrom:ht,leaveTo:Ae,...Re}=e,Xe={ref:c},Fe={...Re,...p.value&&d.value&&zo.isServer?{class:ot([n.class,Re.class,...$,...I])}:{}};return lt({theirProps:Fe,ourProps:Xe,slot:{},slots:r,attrs:n,features:H0,visible:g.value==="visible",name:"TransitionChild"})}}}),ug=sn,Zr=_e({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:r}){let o=Zo(),s=O(()=>e.show===null&&o!==null?(o.value&at.Open)===at.Open:e.show);it(()=>{if(![!0,!1].includes(s.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let i=F(s.value?"visible":"hidden"),l=N0(()=>{i.value="hidden"}),a=F(!0),u={show:s,appear:O(()=>e.appear||!a.value)};return We(()=>{it(()=>{a.value=!1,s.value?i.value="visible":fi(l)||(i.value="hidden")})}),st(Ba,l),st(Da,u),()=>{let c=ci(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),f={unmount:e.unmount};return lt({ourProps:{...f,as:"template"},theirProps:{},slot:{},slots:{...r,default:()=>[Oe(ug,{onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave"),...n,...f,...c},r.default)]},attrs:{},features:H0,visible:i.value==="visible",name:"Transition"})}}});const cg=["width","height","fill","transform"],dg={key:0},fg=h("path",{d:"M208,20H72A36,36,0,0,0,36,56V224a12,12,0,0,0,12,12H192a12,12,0,0,0,0-24H60v-4a12,12,0,0,1,12-12H208a12,12,0,0,0,12-12V32A12,12,0,0,0,208,20ZM120,44h36v59l-10.51-8.41a12,12,0,0,0-15,0L120,103Zm76,128H72a35.59,35.59,0,0,0-12,2.06V56A12,12,0,0,1,72,44H96v84a12,12,0,0,0,19.5,9.37l22.49-18,22.51,18A12,12,0,0,0,180,128V44h16Z"},null,-1),pg=[fg],hg={key:1},vg=h("path",{d:"M208,32V192H72a24,24,0,0,0-24,24V56A24,24,0,0,1,72,32h40v96l32-24,32,24V32Z",opacity:"0.2"},null,-1),mg=h("path",{d:"M208,24H72A32,32,0,0,0,40,56V224a8,8,0,0,0,8,8H192a8,8,0,0,0,0-16H56a16,16,0,0,1,16-16H208a8,8,0,0,0,8-8V32A8,8,0,0,0,208,24ZM120,40h48v72L148.79,97.6a8,8,0,0,0-9.6,0L120,112Zm80,144H72a31.82,31.82,0,0,0-16,4.29V56A16,16,0,0,1,72,40h32v88a8,8,0,0,0,12.8,6.4L144,114l27.21,20.4A8,8,0,0,0,176,136a8.1,8.1,0,0,0,3.58-.84A8,8,0,0,0,184,128V40h16Z"},null,-1),gg=[vg,mg],yg={key:2},bg=h("path",{d:"M208,24H72A32,32,0,0,0,40,56V224a8,8,0,0,0,8,8H192a8,8,0,0,0,0-16H56a16,16,0,0,1,16-16H208a8,8,0,0,0,8-8V32A8,8,0,0,0,208,24Zm-24,96-25.61-19.2a4,4,0,0,0-4.8,0L128,120V40h56Z"},null,-1),wg=[bg],_g={key:3},Sg=h("path",{d:"M208,26H72A30,30,0,0,0,42,56V224a6,6,0,0,0,6,6H192a6,6,0,0,0,0-12H54v-2a18,18,0,0,1,18-18H208a6,6,0,0,0,6-6V32A6,6,0,0,0,208,26ZM118,38h52v78L147.59,99.2a6,6,0,0,0-7.2,0L118,116Zm84,148H72a29.87,29.87,0,0,0-18,6V56A18,18,0,0,1,72,38h34v90a6,6,0,0,0,9.6,4.8L144,111.5l28.41,21.3A6,6,0,0,0,182,128V38h20Z"},null,-1),$g=[Sg],Ag={key:4},xg=h("path",{d:"M208,24H72A32,32,0,0,0,40,56V224a8,8,0,0,0,8,8H192a8,8,0,0,0,0-16H56a16,16,0,0,1,16-16H208a8,8,0,0,0,8-8V32A8,8,0,0,0,208,24ZM120,40h48v72L148.79,97.6a8,8,0,0,0-9.6,0L120,112Zm80,144H72a31.82,31.82,0,0,0-16,4.29V56A16,16,0,0,1,72,40h32v88a8,8,0,0,0,12.8,6.4L144,114l27.21,20.4A8,8,0,0,0,176,136a8,8,0,0,0,8-8V40h16Z"},null,-1),Eg=[xg],Cg={key:5},Tg=h("path",{d:"M208,28H72A28,28,0,0,0,44,56V224a4,4,0,0,0,4,4H192a4,4,0,0,0,0-8H52v-4a20,20,0,0,1,20-20H208a4,4,0,0,0,4-4V32A4,4,0,0,0,208,28Zm-92,8h56v84l-25.61-19.2a4,4,0,0,0-4.8,0L116,120Zm88,152H72a27.94,27.94,0,0,0-20,8.42V56A20,20,0,0,1,72,36h36v92a4,4,0,0,0,6.4,3.2L144,109l29.61,22.2a4,4,0,0,0,2.4.8,4,4,0,0,0,4-4V36h24Z"},null,-1),Og=[Tg],Pg={name:"PhBookBookmark"},ja=_e({...Pg,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",dg,pg)):i.value==="duotone"?(x(),T("g",hg,gg)):i.value==="fill"?(x(),T("g",yg,wg)):i.value==="light"?(x(),T("g",_g,$g)):i.value==="regular"?(x(),T("g",Ag,Eg)):i.value==="thin"?(x(),T("g",Cg,Og)):ce("",!0)],16,cg))}}),Rg=["width","height","fill","transform"],Mg={key:0},kg=h("path",{d:"M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"},null,-1),Lg=[kg],Ig={key:1},Ng=h("path",{d:"M208,96l-80,80L48,96Z",opacity:"0.2"},null,-1),Hg=h("path",{d:"M215.39,92.94A8,8,0,0,0,208,88H48a8,8,0,0,0-5.66,13.66l80,80a8,8,0,0,0,11.32,0l80-80A8,8,0,0,0,215.39,92.94ZM128,164.69,67.31,104H188.69Z"},null,-1),Fg=[Ng,Hg],Vg={key:2},Dg=h("path",{d:"M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,48,88H208a8,8,0,0,1,5.66,13.66Z"},null,-1),Bg=[Dg],jg={key:3},zg=h("path",{d:"M212.24,100.24l-80,80a6,6,0,0,1-8.48,0l-80-80a6,6,0,0,1,8.48-8.48L128,167.51l75.76-75.75a6,6,0,0,1,8.48,8.48Z"},null,-1),Ug=[zg],Zg={key:4},qg=h("path",{d:"M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"},null,-1),Wg=[qg],Gg={key:5},Kg=h("path",{d:"M210.83,98.83l-80,80a4,4,0,0,1-5.66,0l-80-80a4,4,0,0,1,5.66-5.66L128,170.34l77.17-77.17a4,4,0,1,1,5.66,5.66Z"},null,-1),Yg=[Kg],Jg={name:"PhCaretDown"},Xg=_e({...Jg,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",Mg,Lg)):i.value==="duotone"?(x(),T("g",Ig,Fg)):i.value==="fill"?(x(),T("g",Vg,Bg)):i.value==="light"?(x(),T("g",jg,Ug)):i.value==="regular"?(x(),T("g",Zg,Wg)):i.value==="thin"?(x(),T("g",Gg,Yg)):ce("",!0)],16,Rg))}}),Qg=["width","height","fill","transform"],ey={key:0},ty=h("path",{d:"M232,116h-4.72A100.21,100.21,0,0,0,140,28.72V24a12,12,0,0,0-24,0v4.72A100.21,100.21,0,0,0,28.72,116H24a12,12,0,0,0,0,24h4.72A100.21,100.21,0,0,0,116,227.28V232a12,12,0,0,0,24,0v-4.72A100.21,100.21,0,0,0,227.28,140H232a12,12,0,0,0,0-24Zm-92,87v-3a12,12,0,0,0-24,0v3a76.15,76.15,0,0,1-63-63h3a12,12,0,0,0,0-24H53a76.15,76.15,0,0,1,63-63v3a12,12,0,0,0,24,0V53a76.15,76.15,0,0,1,63,63h-3a12,12,0,0,0,0,24h3A76.15,76.15,0,0,1,140,203ZM128,84a44,44,0,1,0,44,44A44.05,44.05,0,0,0,128,84Zm0,64a20,20,0,1,1,20-20A20,20,0,0,1,128,148Z"},null,-1),ny=[ty],ry={key:1},oy=h("path",{d:"M160,128a32,32,0,1,1-32-32A32,32,0,0,1,160,128Z",opacity:"0.2"},null,-1),sy=h("path",{d:"M232,120h-8.34A96.14,96.14,0,0,0,136,32.34V24a8,8,0,0,0-16,0v8.34A96.14,96.14,0,0,0,32.34,120H24a8,8,0,0,0,0,16h8.34A96.14,96.14,0,0,0,120,223.66V232a8,8,0,0,0,16,0v-8.34A96.14,96.14,0,0,0,223.66,136H232a8,8,0,0,0,0-16Zm-96,87.6V200a8,8,0,0,0-16,0v7.6A80.15,80.15,0,0,1,48.4,136H56a8,8,0,0,0,0-16H48.4A80.15,80.15,0,0,1,120,48.4V56a8,8,0,0,0,16,0V48.4A80.15,80.15,0,0,1,207.6,120H200a8,8,0,0,0,0,16h7.6A80.15,80.15,0,0,1,136,207.6ZM128,88a40,40,0,1,0,40,40A40,40,0,0,0,128,88Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,152Z"},null,-1),iy=[oy,sy],ly={key:2},ay=h("path",{d:"M232,120h-8.34A96.14,96.14,0,0,0,136,32.34V24a8,8,0,0,0-16,0v8.34A96.14,96.14,0,0,0,32.34,120H24a8,8,0,0,0,0,16h8.34A96.14,96.14,0,0,0,120,223.66V232a8,8,0,0,0,16,0v-8.34A96.14,96.14,0,0,0,223.66,136H232a8,8,0,0,0,0-16Zm-32,16h7.6A80.15,80.15,0,0,1,136,207.6V200a8,8,0,0,0-16,0v7.6A80.15,80.15,0,0,1,48.4,136H56a8,8,0,0,0,0-16H48.4A80.15,80.15,0,0,1,120,48.4V56a8,8,0,0,0,16,0V48.4A80.15,80.15,0,0,1,207.6,120H200a8,8,0,0,0,0,16Zm-32-8a40,40,0,1,1-40-40A40,40,0,0,1,168,128Z"},null,-1),uy=[ay],cy={key:3},dy=h("path",{d:"M232,122H221.8A94.13,94.13,0,0,0,134,34.2V24a6,6,0,0,0-12,0V34.2A94.13,94.13,0,0,0,34.2,122H24a6,6,0,0,0,0,12H34.2A94.13,94.13,0,0,0,122,221.8V232a6,6,0,0,0,12,0V221.8A94.13,94.13,0,0,0,221.8,134H232a6,6,0,0,0,0-12Zm-98,87.76V200a6,6,0,0,0-12,0v9.76A82.09,82.09,0,0,1,46.24,134H56a6,6,0,0,0,0-12H46.24A82.09,82.09,0,0,1,122,46.24V56a6,6,0,0,0,12,0V46.24A82.09,82.09,0,0,1,209.76,122H200a6,6,0,0,0,0,12h9.76A82.09,82.09,0,0,1,134,209.76ZM128,90a38,38,0,1,0,38,38A38,38,0,0,0,128,90Zm0,64a26,26,0,1,1,26-26A26,26,0,0,1,128,154Z"},null,-1),fy=[dy],py={key:4},hy=h("path",{d:"M232,120h-8.34A96.14,96.14,0,0,0,136,32.34V24a8,8,0,0,0-16,0v8.34A96.14,96.14,0,0,0,32.34,120H24a8,8,0,0,0,0,16h8.34A96.14,96.14,0,0,0,120,223.66V232a8,8,0,0,0,16,0v-8.34A96.14,96.14,0,0,0,223.66,136H232a8,8,0,0,0,0-16Zm-96,87.6V200a8,8,0,0,0-16,0v7.6A80.15,80.15,0,0,1,48.4,136H56a8,8,0,0,0,0-16H48.4A80.15,80.15,0,0,1,120,48.4V56a8,8,0,0,0,16,0V48.4A80.15,80.15,0,0,1,207.6,120H200a8,8,0,0,0,0,16h7.6A80.15,80.15,0,0,1,136,207.6ZM128,88a40,40,0,1,0,40,40A40,40,0,0,0,128,88Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,152Z"},null,-1),vy=[hy],my={key:5},gy=h("path",{d:"M232,124H219.91A92.13,92.13,0,0,0,132,36.09V24a4,4,0,0,0-8,0V36.09A92.13,92.13,0,0,0,36.09,124H24a4,4,0,0,0,0,8H36.09A92.13,92.13,0,0,0,124,219.91V232a4,4,0,0,0,8,0V219.91A92.13,92.13,0,0,0,219.91,132H232a4,4,0,0,0,0-8ZM132,211.9V200a4,4,0,0,0-8,0v11.9A84.11,84.11,0,0,1,44.1,132H56a4,4,0,0,0,0-8H44.1A84.11,84.11,0,0,1,124,44.1V56a4,4,0,0,0,8,0V44.1A84.11,84.11,0,0,1,211.9,124H200a4,4,0,0,0,0,8h11.9A84.11,84.11,0,0,1,132,211.9ZM128,92a36,36,0,1,0,36,36A36,36,0,0,0,128,92Zm0,64a28,28,0,1,1,28-28A28,28,0,0,1,128,156Z"},null,-1),yy=[gy],by={name:"PhCrosshair"},wy=_e({...by,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",ey,ny)):i.value==="duotone"?(x(),T("g",ry,iy)):i.value==="fill"?(x(),T("g",ly,uy)):i.value==="light"?(x(),T("g",cy,fy)):i.value==="regular"?(x(),T("g",py,vy)):i.value==="thin"?(x(),T("g",my,yy)):ce("",!0)],16,Qg))}}),_y=["width","height","fill","transform"],Sy={key:0},$y=h("path",{d:"M251,123.13c-.37-.81-9.13-20.26-28.48-39.61C196.63,57.67,164,44,128,44S59.37,57.67,33.51,83.52C14.16,102.87,5.4,122.32,5,123.13a12.08,12.08,0,0,0,0,9.75c.37.82,9.13,20.26,28.49,39.61C59.37,198.34,92,212,128,212s68.63-13.66,94.48-39.51c19.36-19.35,28.12-38.79,28.49-39.61A12.08,12.08,0,0,0,251,123.13Zm-46.06,33C183.47,177.27,157.59,188,128,188s-55.47-10.73-76.91-31.88A130.36,130.36,0,0,1,29.52,128,130.45,130.45,0,0,1,51.09,99.89C72.54,78.73,98.41,68,128,68s55.46,10.73,76.91,31.89A130.36,130.36,0,0,1,226.48,128,130.45,130.45,0,0,1,204.91,156.12ZM128,84a44,44,0,1,0,44,44A44.05,44.05,0,0,0,128,84Zm0,64a20,20,0,1,1,20-20A20,20,0,0,1,128,148Z"},null,-1),Ay=[$y],xy={key:1},Ey=h("path",{d:"M128,56C48,56,16,128,16,128s32,72,112,72,112-72,112-72S208,56,128,56Zm0,112a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z",opacity:"0.2"},null,-1),Cy=h("path",{d:"M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"},null,-1),Ty=[Ey,Cy],Oy={key:2},Py=h("path",{d:"M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,168a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z"},null,-1),Ry=[Py],My={key:3},ky=h("path",{d:"M245.48,125.57c-.34-.78-8.66-19.23-27.24-37.81C201,70.54,171.38,50,128,50S55,70.54,37.76,87.76c-18.58,18.58-26.9,37-27.24,37.81a6,6,0,0,0,0,4.88c.34.77,8.66,19.22,27.24,37.8C55,185.47,84.62,206,128,206s73-20.53,90.24-37.75c18.58-18.58,26.9-37,27.24-37.8A6,6,0,0,0,245.48,125.57ZM128,194c-31.38,0-58.78-11.42-81.45-33.93A134.77,134.77,0,0,1,22.69,128,134.56,134.56,0,0,1,46.55,95.94C69.22,73.42,96.62,62,128,62s58.78,11.42,81.45,33.94A134.56,134.56,0,0,1,233.31,128C226.94,140.21,195,194,128,194Zm0-112a46,46,0,1,0,46,46A46.06,46.06,0,0,0,128,82Zm0,80a34,34,0,1,1,34-34A34,34,0,0,1,128,162Z"},null,-1),Ly=[ky],Iy={key:4},Ny=h("path",{d:"M247.31,124.76c-.35-.79-8.82-19.58-27.65-38.41C194.57,61.26,162.88,48,128,48S61.43,61.26,36.34,86.35C17.51,105.18,9,124,8.69,124.76a8,8,0,0,0,0,6.5c.35.79,8.82,19.57,27.65,38.4C61.43,194.74,93.12,208,128,208s66.57-13.26,91.66-38.34c18.83-18.83,27.3-37.61,27.65-38.4A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-57.67-11.19-79.93-33.25A133.47,133.47,0,0,1,25,128,133.33,133.33,0,0,1,48.07,97.25C70.33,75.19,97.22,64,128,64s57.67,11.19,79.93,33.25A133.46,133.46,0,0,1,231.05,128C223.84,141.46,192.43,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Z"},null,-1),Hy=[Ny],Fy={key:5},Vy=h("path",{d:"M243.66,126.38c-.34-.76-8.52-18.89-26.83-37.2C199.87,72.22,170.7,52,128,52S56.13,72.22,39.17,89.18c-18.31,18.31-26.49,36.44-26.83,37.2a4.08,4.08,0,0,0,0,3.25c.34.77,8.52,18.89,26.83,37.2,17,17,46.14,37.17,88.83,37.17s71.87-20.21,88.83-37.17c18.31-18.31,26.49-36.43,26.83-37.2A4.08,4.08,0,0,0,243.66,126.38Zm-32.7,35c-23.07,23-51,34.62-83,34.62s-59.89-11.65-83-34.62A135.71,135.71,0,0,1,20.44,128,135.69,135.69,0,0,1,45,94.62C68.11,71.65,96,60,128,60s59.89,11.65,83,34.62A135.79,135.79,0,0,1,235.56,128,135.71,135.71,0,0,1,211,161.38ZM128,84a44,44,0,1,0,44,44A44.05,44.05,0,0,0,128,84Zm0,80a36,36,0,1,1,36-36A36,36,0,0,1,128,164Z"},null,-1),Dy=[Vy],By={name:"PhEye"},jy=_e({...By,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",Sy,Ay)):i.value==="duotone"?(x(),T("g",xy,Ty)):i.value==="fill"?(x(),T("g",Oy,Ry)):i.value==="light"?(x(),T("g",My,Ly)):i.value==="regular"?(x(),T("g",Iy,Hy)):i.value==="thin"?(x(),T("g",Fy,Dy)):ce("",!0)],16,_y))}}),zy=["width","height","fill","transform"],Uy={key:0},Zy=h("path",{d:"M200,164v8h12a12,12,0,0,1,0,24H200v12a12,12,0,0,1-24,0V152a12,12,0,0,1,12-12h32a12,12,0,0,1,0,24ZM92,172a32,32,0,0,1-32,32H56v4a12,12,0,0,1-24,0V152a12,12,0,0,1,12-12H60A32,32,0,0,1,92,172Zm-24,0a8,8,0,0,0-8-8H56v16h4A8,8,0,0,0,68,172Zm100,8a40,40,0,0,1-40,40H112a12,12,0,0,1-12-12V152a12,12,0,0,1,12-12h16A40,40,0,0,1,168,180Zm-24,0a16,16,0,0,0-16-16h-4v32h4A16,16,0,0,0,144,180ZM36,108V40A20,20,0,0,1,56,20h96a12,12,0,0,1,8.49,3.52l56,56A12,12,0,0,1,220,88v20a12,12,0,0,1-24,0v-4H148a12,12,0,0,1-12-12V44H60v64a12,12,0,0,1-24,0ZM160,57V80h23Z"},null,-1),qy=[Zy],Wy={key:1},Gy=h("path",{d:"M208,88H152V32Z",opacity:"0.2"},null,-1),Ky=h("path",{d:"M224,152a8,8,0,0,1-8,8H192v16h16a8,8,0,0,1,0,16H192v16a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h32A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm88,8a36,36,0,0,1-36,36H112a8,8,0,0,1-8-8V152a8,8,0,0,1,8-8h16A36,36,0,0,1,164,180Zm-16,0a20,20,0,0,0-20-20h-8v40h8A20,20,0,0,0,148,180ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"},null,-1),Yy=[Gy,Ky],Jy={key:2},Xy=h("path",{d:"M44,120H212a4,4,0,0,0,4-4V88a8,8,0,0,0-2.34-5.66l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40v76A4,4,0,0,0,44,120ZM152,44l44,44H152Zm72,108.53a8.18,8.18,0,0,1-8.25,7.47H192v16h15.73a8.17,8.17,0,0,1,8.25,7.47,8,8,0,0,1-8,8.53H192v15.73a8.17,8.17,0,0,1-7.47,8.25,8,8,0,0,1-8.53-8V152a8,8,0,0,1,8-8h32A8,8,0,0,1,224,152.53ZM64,144H48a8,8,0,0,0-8,8v55.73A8.17,8.17,0,0,0,47.47,216,8,8,0,0,0,56,208v-8h7.4c15.24,0,28.14-11.92,28.59-27.15A28,28,0,0,0,64,144Zm-.35,40H56V160h8a12,12,0,0,1,12,13.16A12.25,12.25,0,0,1,63.65,184ZM128,144H112a8,8,0,0,0-8,8v56a8,8,0,0,0,8,8h15.32c19.66,0,36.21-15.48,36.67-35.13A36,36,0,0,0,128,144Zm-.49,56H120V160h8a20,20,0,0,1,20,20.77C147.58,191.59,138.34,200,127.51,200Z"},null,-1),Qy=[Xy],eb={key:3},tb=h("path",{d:"M222,152a6,6,0,0,1-6,6H190v20h18a6,6,0,0,1,0,12H190v18a6,6,0,0,1-12,0V152a6,6,0,0,1,6-6h32A6,6,0,0,1,222,152ZM90,172a26,26,0,0,1-26,26H54v10a6,6,0,0,1-12,0V152a6,6,0,0,1,6-6H64A26,26,0,0,1,90,172Zm-12,0a14,14,0,0,0-14-14H54v28H64A14,14,0,0,0,78,172Zm84,8a34,34,0,0,1-34,34H112a6,6,0,0,1-6-6V152a6,6,0,0,1,6-6h16A34,34,0,0,1,162,180Zm-12,0a22,22,0,0,0-22-22H118v44h10A22,22,0,0,0,150,180ZM42,112V40A14,14,0,0,1,56,26h96a6,6,0,0,1,4.25,1.76l56,56A6,6,0,0,1,214,88v24a6,6,0,0,1-12,0V94H152a6,6,0,0,1-6-6V38H56a2,2,0,0,0-2,2v72a6,6,0,0,1-12,0ZM158,82h35.52L158,46.48Z"},null,-1),nb=[tb],rb={key:4},ob=h("path",{d:"M224,152a8,8,0,0,1-8,8H192v16h16a8,8,0,0,1,0,16H192v16a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h32A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm88,8a36,36,0,0,1-36,36H112a8,8,0,0,1-8-8V152a8,8,0,0,1,8-8h16A36,36,0,0,1,164,180Zm-16,0a20,20,0,0,0-20-20h-8v40h8A20,20,0,0,0,148,180ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"},null,-1),sb=[ob],ib={key:5},lb=h("path",{d:"M220,152a4,4,0,0,1-4,4H188v24h20a4,4,0,0,1,0,8H188v20a4,4,0,0,1-8,0V152a4,4,0,0,1,4-4h32A4,4,0,0,1,220,152ZM88,172a24,24,0,0,1-24,24H52v12a4,4,0,0,1-8,0V152a4,4,0,0,1,4-4H64A24,24,0,0,1,88,172Zm-8,0a16,16,0,0,0-16-16H52v32H64A16,16,0,0,0,80,172Zm80,8a32,32,0,0,1-32,32H112a4,4,0,0,1-4-4V152a4,4,0,0,1,4-4h16A32,32,0,0,1,160,180Zm-8,0a24,24,0,0,0-24-24H116v48h12A24,24,0,0,0,152,180ZM44,112V40A12,12,0,0,1,56,28h96a4,4,0,0,1,2.83,1.17l56,56A4,4,0,0,1,212,88v24a4,4,0,0,1-8,0V92H152a4,4,0,0,1-4-4V36H56a4,4,0,0,0-4,4v72a4,4,0,0,1-8,0ZM156,84h42.34L156,41.65Z"},null,-1),ab=[lb],ub={name:"PhFilePdf"},Il=_e({...ub,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",Uy,qy)):i.value==="duotone"?(x(),T("g",Wy,Yy)):i.value==="fill"?(x(),T("g",Jy,Qy)):i.value==="light"?(x(),T("g",eb,nb)):i.value==="regular"?(x(),T("g",rb,sb)):i.value==="thin"?(x(),T("g",ib,ab)):ce("",!0)],16,zy))}}),cb=["width","height","fill","transform"],db={key:0},fb=h("path",{d:"M232,152a12,12,0,0,1-12,12h-8v44a12,12,0,0,1-24,0V164h-8a12,12,0,0,1,0-24h40A12,12,0,0,1,232,152ZM92,172a32,32,0,0,1-32,32H56v4a12,12,0,0,1-24,0V152a12,12,0,0,1,12-12H60A32,32,0,0,1,92,172Zm-24,0a8,8,0,0,0-8-8H56v16h4A8,8,0,0,0,68,172Zm96,0a32,32,0,0,1-32,32h-4v4a12,12,0,0,1-24,0V152a12,12,0,0,1,12-12h16A32,32,0,0,1,164,172Zm-24,0a8,8,0,0,0-8-8h-4v16h4A8,8,0,0,0,140,172ZM36,108V40A20,20,0,0,1,56,20h96a12,12,0,0,1,8.49,3.52l56,56A12,12,0,0,1,220,88v20a12,12,0,0,1-24,0v-4H148a12,12,0,0,1-12-12V44H60v64a12,12,0,0,1-24,0ZM160,80h23L160,57Z"},null,-1),pb=[fb],hb={key:1},vb=h("path",{d:"M208,88H152V32Z",opacity:"0.2"},null,-1),mb=h("path",{d:"M224,152a8,8,0,0,1-8,8H204v48a8,8,0,0,1-16,0V160H176a8,8,0,0,1,0-16h40A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm84,0a28,28,0,0,1-28,28h-8v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h16A28,28,0,0,1,160,172Zm-16,0a12,12,0,0,0-12-12h-8v24h8A12,12,0,0,0,144,172ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"},null,-1),gb=[vb,mb],yb={key:2},bb=h("path",{d:"M224,152.53a8.17,8.17,0,0,1-8.25,7.47H204v47.73a8.17,8.17,0,0,1-7.47,8.25,8,8,0,0,1-8.53-8V160H176.27a8.17,8.17,0,0,1-8.25-7.47,8,8,0,0,1,8-8.53h40A8,8,0,0,1,224,152.53ZM92,172.85C91.54,188.08,78.64,200,63.4,200H56v7.73A8.17,8.17,0,0,1,48.53,216,8,8,0,0,1,40,208V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172.85Zm-16-2A12.25,12.25,0,0,0,63.65,160H56v24h8A12,12,0,0,0,76,170.84Zm84,2C159.54,188.08,146.64,200,131.4,200H124v7.73a8.17,8.17,0,0,1-7.47,8.25,8,8,0,0,1-8.53-8V152a8,8,0,0,1,8-8h16A28,28,0,0,1,160,172.85Zm-16-2A12.25,12.25,0,0,0,131.65,160H124v24h8A12,12,0,0,0,144,170.84ZM40,116V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v28a4,4,0,0,1-4,4H44A4,4,0,0,1,40,116ZM152,88h44L152,44Z"},null,-1),wb=[bb],_b={key:3},Sb=h("path",{d:"M222,152a6,6,0,0,1-6,6H202v50a6,6,0,0,1-12,0V158H176a6,6,0,0,1,0-12h40A6,6,0,0,1,222,152ZM90,172a26,26,0,0,1-26,26H54v10a6,6,0,0,1-12,0V152a6,6,0,0,1,6-6H64A26,26,0,0,1,90,172Zm-12,0a14,14,0,0,0-14-14H54v28H64A14,14,0,0,0,78,172Zm80,0a26,26,0,0,1-26,26H122v10a6,6,0,0,1-12,0V152a6,6,0,0,1,6-6h16A26,26,0,0,1,158,172Zm-12,0a14,14,0,0,0-14-14H122v28h10A14,14,0,0,0,146,172ZM42,112V40A14,14,0,0,1,56,26h96a6,6,0,0,1,4.25,1.76l56,56A6,6,0,0,1,214,88v24a6,6,0,0,1-12,0V94H152a6,6,0,0,1-6-6V38H56a2,2,0,0,0-2,2v72a6,6,0,0,1-12,0ZM158,82h35.52L158,46.48Z"},null,-1),$b=[Sb],Ab={key:4},xb=h("path",{d:"M224,152a8,8,0,0,1-8,8H204v48a8,8,0,0,1-16,0V160H176a8,8,0,0,1,0-16h40A8,8,0,0,1,224,152ZM92,172a28,28,0,0,1-28,28H56v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8H64A28,28,0,0,1,92,172Zm-16,0a12,12,0,0,0-12-12H56v24h8A12,12,0,0,0,76,172Zm84,0a28,28,0,0,1-28,28h-8v8a8,8,0,0,1-16,0V152a8,8,0,0,1,8-8h16A28,28,0,0,1,160,172Zm-16,0a12,12,0,0,0-12-12h-8v24h8A12,12,0,0,0,144,172ZM40,112V40A16,16,0,0,1,56,24h96a8,8,0,0,1,5.66,2.34l56,56A8,8,0,0,1,216,88v24a8,8,0,0,1-16,0V96H152a8,8,0,0,1-8-8V40H56v72a8,8,0,0,1-16,0ZM160,80h28.69L160,51.31Z"},null,-1),Eb=[xb],Cb={key:5},Tb=h("path",{d:"M220,152a4,4,0,0,1-4,4H200v52a4,4,0,0,1-8,0V156H176a4,4,0,0,1,0-8h40A4,4,0,0,1,220,152ZM88,172a24,24,0,0,1-24,24H52v12a4,4,0,0,1-8,0V152a4,4,0,0,1,4-4H64A24,24,0,0,1,88,172Zm-8,0a16,16,0,0,0-16-16H52v32H64A16,16,0,0,0,80,172Zm76,0a24,24,0,0,1-24,24H120v12a4,4,0,0,1-8,0V152a4,4,0,0,1,4-4h16A24,24,0,0,1,156,172Zm-8,0a16,16,0,0,0-16-16H120v32h12A16,16,0,0,0,148,172ZM44,112V40A12,12,0,0,1,56,28h96a4,4,0,0,1,2.83,1.17l56,56A4,4,0,0,1,212,88v24a4,4,0,0,1-8,0V92H152a4,4,0,0,1-4-4V36H56a4,4,0,0,0-4,4v72a4,4,0,0,1-8,0ZM156,84h42.34L156,41.65Z"},null,-1),Ob=[Tb],Pb={name:"PhFilePpt"},F0=_e({...Pb,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",db,pb)):i.value==="duotone"?(x(),T("g",hb,gb)):i.value==="fill"?(x(),T("g",yb,wb)):i.value==="light"?(x(),T("g",_b,$b)):i.value==="regular"?(x(),T("g",Ab,Eb)):i.value==="thin"?(x(),T("g",Cb,Ob)):ce("",!0)],16,cb))}}),Rb=["width","height","fill","transform"],Mb={key:0},kb=h("path",{d:"M253.88,108.11l-25.53-51a20,20,0,0,0-26.83-9L178.34,59.7,131.7,44.58a12.14,12.14,0,0,0-7.4,0L77.66,59.7,54.48,48.11a20,20,0,0,0-26.83,9L2.12,108.11a20,20,0,0,0,9,26.83l26.67,13.34,51.18,37.41A12.15,12.15,0,0,0,93,187.62l62,16a12.27,12.27,0,0,0,3,.38,12,12,0,0,0,8.48-3.52l52.62-52.62,25.83-12.92a20,20,0,0,0,8.95-26.83Zm-58.12,29.15-27.52-26a12,12,0,0,0-16.76.26c-9.66,9.74-25.06,16.81-40.81,9.55l38.19-37h22.72l25.81,51.63ZM47.32,71.37,60.59,78l-22,43.9-13.27-6.63Zm107,107.3L101.23,165l-42-30.66L85.17,82.5,128,68.61l1.69.55L90,107.68l-.13.12a20,20,0,0,0,3.4,31c20.95,13.39,46,12.07,66.33-2.73l19.2,18.15Zm63-56.77-22-43.9,13.27-6.63,21.95,43.9ZM118.55,219a12,12,0,0,1-14.62,8.62l-26.6-6.87a12,12,0,0,1-4.08-1.93L48.92,201a12,12,0,0,1,14.16-19.37l22.47,16.42,24.38,6.29A12,12,0,0,1,118.55,219Z"},null,-1),Lb=[kb],Ib={key:1},Nb=h("path",{d:"M200,152l-40,40L96,176,40,136,72.68,70.63,128,56l55.32,14.63L183.6,72H144L98.34,116.29a8,8,0,0,0,1.38,12.42C117.23,139.9,141,139.13,160,120Z",opacity:"0.2"},null,-1),Hb=h("path",{d:"M254.3,107.91,228.78,56.85a16,16,0,0,0-21.47-7.15L182.44,62.13,130.05,48.27a8.14,8.14,0,0,0-4.1,0L73.56,62.13,48.69,49.7a16,16,0,0,0-21.47,7.15L1.7,107.9a16,16,0,0,0,7.15,21.47l27,13.51,55.49,39.63a8.06,8.06,0,0,0,2.71,1.25l64,16a8,8,0,0,0,7.6-2.1l55.07-55.08,26.42-13.21a16,16,0,0,0,7.15-21.46Zm-54.89,33.37L165,113.72a8,8,0,0,0-10.68.61C136.51,132.27,116.66,130,104,122L147.24,80h31.81l27.21,54.41ZM41.53,64,62,74.22,36.43,125.27,16,115.06Zm116,119.13L99.42,168.61l-49.2-35.14,28-56L128,64.28l9.8,2.59-45,43.68-.08.09a16,16,0,0,0,2.72,24.81c20.56,13.13,45.37,11,64.91-5L188,152.66Zm62-57.87-25.52-51L214.47,64,240,115.06Zm-87.75,92.67a8,8,0,0,1-7.75,6.06,8.13,8.13,0,0,1-1.95-.24L80.41,213.33a7.89,7.89,0,0,1-2.71-1.25L51.35,193.26a8,8,0,0,1,9.3-13l25.11,17.94L126,208.24A8,8,0,0,1,131.82,217.94Z"},null,-1),Fb=[Nb,Hb],Vb={key:2},Db=h("path",{d:"M254.3,107.91,228.78,56.85a16,16,0,0,0-21.47-7.15L182.44,62.13,130.05,48.27a8.14,8.14,0,0,0-4.1,0L73.56,62.13,48.69,49.7a16,16,0,0,0-21.47,7.15L1.7,107.9a16,16,0,0,0,7.15,21.47l27,13.51,55.49,39.63a8.06,8.06,0,0,0,2.71,1.25l64,16a8,8,0,0,0,7.6-2.1l40-40,15.08-15.08,26.42-13.21a16,16,0,0,0,7.15-21.46Zm-54.89,33.37L165,113.72a8,8,0,0,0-10.68.61C136.51,132.27,116.66,130,104,122L147.24,80h31.81l27.21,54.41Zm-41.87,41.86L99.42,168.61l-49.2-35.14,28-56L128,64.28l9.8,2.59-45,43.68-.08.09a16,16,0,0,0,2.72,24.81c20.56,13.13,45.37,11,64.91-5L188,152.66Zm-25.72,34.8a8,8,0,0,1-7.75,6.06,8.13,8.13,0,0,1-1.95-.24L80.41,213.33a7.89,7.89,0,0,1-2.71-1.25L51.35,193.26a8,8,0,0,1,9.3-13l25.11,17.94L126,208.24A8,8,0,0,1,131.82,217.94Z"},null,-1),Bb=[Db],jb={key:3},zb=h("path",{d:"M252.51,108.8,227,57.75a14,14,0,0,0-18.78-6.27L182.66,64.26,129.53,50.2a6.1,6.1,0,0,0-3.06,0L73.34,64.26,47.79,51.48A14,14,0,0,0,29,57.75L3.49,108.8a14,14,0,0,0,6.26,18.78L36.9,141.16l55.61,39.72a6,6,0,0,0,2,.94l64,16A6.08,6.08,0,0,0,160,198a6,6,0,0,0,4.24-1.76l55.31-55.31,26.7-13.35a14,14,0,0,0,6.26-18.78Zm-53,35.16-35.8-28.68a6,6,0,0,0-8,.45c-18.65,18.79-39.5,16.42-52.79,7.92a2,2,0,0,1-.94-1.5,1.9,1.9,0,0,1,.51-1.55L146.43,78h33.86l28.41,56.82ZM14.11,115.69a2,2,0,0,1,.11-1.52L39.74,63.11a2,2,0,0,1,1.8-1.1,2,2,0,0,1,.89.21l22.21,11.1L37.32,128l-22.21-11.1A2,2,0,0,1,14.11,115.69Zm144.05,69.67-59.6-14.9L47.66,134.1,76.84,75.75,128,62.21l14.8,3.92a5.92,5.92,0,0,0-3,1.57L94.1,112.05a14,14,0,0,0,2.39,21.72c20.22,12.92,44.75,10.49,63.8-5.89L191,152.5Zm83.73-69.67a2,2,0,0,1-1,1.16L218.68,128,191.36,73.32l22.21-11.1a2,2,0,0,1,1.53-.11,2,2,0,0,1,1.16,1l25.52,51.06A2,2,0,0,1,241.89,115.69Zm-112,101.76a6,6,0,0,1-7.27,4.37L80.89,211.39a5.88,5.88,0,0,1-2-.94L52.52,191.64a6,6,0,1,1,7-9.77L84.91,200l40.61,10.15A6,6,0,0,1,129.88,217.45Z"},null,-1),Ub=[zb],Zb={key:4},qb=h("path",{d:"M254.3,107.91,228.78,56.85a16,16,0,0,0-21.47-7.15L182.44,62.13,130.05,48.27a8.14,8.14,0,0,0-4.1,0L73.56,62.13,48.69,49.7a16,16,0,0,0-21.47,7.15L1.7,107.9a16,16,0,0,0,7.15,21.47l27,13.51,55.49,39.63a8.06,8.06,0,0,0,2.71,1.25l64,16a8,8,0,0,0,7.6-2.1l55.07-55.08,26.42-13.21a16,16,0,0,0,7.15-21.46Zm-54.89,33.37L165,113.72a8,8,0,0,0-10.68.61C136.51,132.27,116.66,130,104,122L147.24,80h31.81l27.21,54.41ZM41.53,64,62,74.22,36.43,125.27,16,115.06Zm116,119.13L99.42,168.61l-49.2-35.14,28-56L128,64.28l9.8,2.59-45,43.68-.08.09a16,16,0,0,0,2.72,24.81c20.56,13.13,45.37,11,64.91-5L188,152.66Zm62-57.87-25.52-51L214.47,64,240,115.06Zm-87.75,92.67a8,8,0,0,1-7.75,6.06,8.13,8.13,0,0,1-1.95-.24L80.41,213.33a7.89,7.89,0,0,1-2.71-1.25L51.35,193.26a8,8,0,0,1,9.3-13l25.11,17.94L126,208.24A8,8,0,0,1,131.82,217.94Z"},null,-1),Wb=[qb],Gb={key:5},Kb=h("path",{d:"M250.73,109.69l-25.53-51a12,12,0,0,0-16.1-5.37L182.88,66.38,129,52.14a3.92,3.92,0,0,0-2,0L73.12,66.38,46.9,53.27a12,12,0,0,0-16.1,5.37L5.27,109.69a12,12,0,0,0,5.37,16.1l27.29,13.65,55.75,39.82a3.87,3.87,0,0,0,1.35.62l64,16a4,4,0,0,0,3.8-1l55.54-55.54,27-13.5a12,12,0,0,0,5.37-16.1Zm-51,36.95-37.2-29.8a4,4,0,0,0-5.34.3c-19.49,19.64-41.34,17.11-55.29,8.2a4.07,4.07,0,0,1-1.85-3,3.91,3.91,0,0,1,1.11-3.21L145.62,76h35.91l29.6,59.21ZM12.21,116.32a4,4,0,0,1,.22-3L38,62.22h0A4,4,0,0,1,41.54,60a4,4,0,0,1,1.78.43l24,12L38.21,130.64l-24-12A4,4,0,0,1,12.21,116.32Zm146.56,71.25L97.71,172.3l-52.6-37.57L75.45,74,128,60.14,157.72,68H144a4,4,0,0,0-2.79,1.13l-45.7,44.33a12,12,0,0,0,2.06,18.62c19.88,12.71,44.13,10,62.66-6.81L194,152.33Zm85-71.25a4,4,0,0,1-2,2.32l-24,12L188.68,72.43l24-12A4,4,0,0,1,218,62.22l25.53,51.05A4,4,0,0,1,243.79,116.32ZM127.94,217a4,4,0,0,1-3.88,3,4.09,4.09,0,0,1-1-.12L81.38,209.45a4,4,0,0,1-1.36-.62L53.68,190a4,4,0,0,1,4.65-6.51l25.72,18.37,41,10.25A4,4,0,0,1,127.94,217Z"},null,-1),Yb=[Kb],Jb={name:"PhHandshake"},za=_e({...Jb,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",Mb,Lb)):i.value==="duotone"?(x(),T("g",Ib,Fb)):i.value==="fill"?(x(),T("g",Vb,Bb)):i.value==="light"?(x(),T("g",jb,Ub)):i.value==="regular"?(x(),T("g",Zb,Wb)):i.value==="thin"?(x(),T("g",Gb,Yb)):ce("",!0)],16,Rb))}}),Xb=["width","height","fill","transform"],Qb={key:0},e4=h("path",{d:"M176,128a12,12,0,0,1-5.17,9.87l-52,36A12,12,0,0,1,100,164V92a12,12,0,0,1,18.83-9.87l52,36A12,12,0,0,1,176,128Zm60,0A108,108,0,1,1,128,20,108.12,108.12,0,0,1,236,128Zm-24,0a84,84,0,1,0-84,84A84.09,84.09,0,0,0,212,128Z"},null,-1),t4=[e4],n4={key:1},r4=h("path",{d:"M128,32a96,96,0,1,0,96,96A96,96,0,0,0,128,32ZM108,168V88l64,40Z",opacity:"0.2"},null,-1),o4=h("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm48.24-94.78-64-40A8,8,0,0,0,100,88v80a8,8,0,0,0,12.24,6.78l64-40a8,8,0,0,0,0-13.56ZM116,153.57V102.43L156.91,128Z"},null,-1),s4=[r4,o4],i4={key:2},l4=h("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm40.55,110.58-52,36A8,8,0,0,1,104,164V92a8,8,0,0,1,12.55-6.58l52,36a8,8,0,0,1,0,13.16Z"},null,-1),a4=[l4],u4={key:3},c4=h("path",{d:"M128,26A102,102,0,1,0,230,128,102.12,102.12,0,0,0,128,26Zm0,192a90,90,0,1,1,90-90A90.1,90.1,0,0,1,128,218Zm47.18-95.09-64-40A6,6,0,0,0,102,88v80a6,6,0,0,0,9.18,5.09l64-40a6,6,0,0,0,0-10.18ZM114,157.17V98.83L160.68,128Z"},null,-1),d4=[c4],f4={key:4},p4=h("path",{d:"M128,24A104,104,0,1,0,232,128,104.11,104.11,0,0,0,128,24Zm0,192a88,88,0,1,1,88-88A88.1,88.1,0,0,1,128,216Zm48.24-94.78-64-40A8,8,0,0,0,100,88v80a8,8,0,0,0,12.24,6.78l64-40a8,8,0,0,0,0-13.56ZM116,153.57V102.43L156.91,128Z"},null,-1),h4=[p4],v4={key:5},m4=h("path",{d:"M128,28A100,100,0,1,0,228,128,100.11,100.11,0,0,0,128,28Zm0,192a92,92,0,1,1,92-92A92.1,92.1,0,0,1,128,220Zm46.12-95.39-64-40A4,4,0,0,0,104,88v80a4,4,0,0,0,2.06,3.5,4.06,4.06,0,0,0,1.94.5,4,4,0,0,0,2.12-.61l64-40a4,4,0,0,0,0-6.78ZM112,160.78V95.22L164.45,128Z"},null,-1),g4=[m4],y4={name:"PhPlayCircle"},Nr=_e({...y4,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",Qb,t4)):i.value==="duotone"?(x(),T("g",n4,s4)):i.value==="fill"?(x(),T("g",i4,a4)):i.value==="light"?(x(),T("g",u4,d4)):i.value==="regular"?(x(),T("g",f4,h4)):i.value==="thin"?(x(),T("g",v4,g4)):ce("",!0)],16,Xb))}}),b4=["width","height","fill","transform"],w4={key:0},_4=h("path",{d:"M160,116h48a20,20,0,0,0,20-20V48a20,20,0,0,0-20-20H160a20,20,0,0,0-20,20V60H128a28,28,0,0,0-28,28v28H76v-4A20,20,0,0,0,56,92H24A20,20,0,0,0,4,112v32a20,20,0,0,0,20,20H56a20,20,0,0,0,20-20v-4h24v28a28,28,0,0,0,28,28h12v12a20,20,0,0,0,20,20h48a20,20,0,0,0,20-20V160a20,20,0,0,0-20-20H160a20,20,0,0,0-20,20v12H128a4,4,0,0,1-4-4V88a4,4,0,0,1,4-4h12V96A20,20,0,0,0,160,116ZM52,140H28V116H52Zm112,24h40v40H164Zm0-112h40V92H164Z"},null,-1),S4=[_4],$4={key:1},A4=h("path",{d:"M64,112v32a8,8,0,0,1-8,8H24a8,8,0,0,1-8-8V112a8,8,0,0,1,8-8H56A8,8,0,0,1,64,112ZM208,40H160a8,8,0,0,0-8,8V96a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V48A8,8,0,0,0,208,40Zm0,112H160a8,8,0,0,0-8,8v48a8,8,0,0,0,8,8h48a8,8,0,0,0,8-8V160A8,8,0,0,0,208,152Z",opacity:"0.2"},null,-1),x4=h("path",{d:"M160,112h48a16,16,0,0,0,16-16V48a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16V64H128a24,24,0,0,0-24,24v32H72v-8A16,16,0,0,0,56,96H24A16,16,0,0,0,8,112v32a16,16,0,0,0,16,16H56a16,16,0,0,0,16-16v-8h32v32a24,24,0,0,0,24,24h16v16a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V160a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16v16H128a8,8,0,0,1-8-8V88a8,8,0,0,1,8-8h16V96A16,16,0,0,0,160,112ZM56,144H24V112H56v32Zm104,16h48v48H160Zm0-112h48V96H160Z"},null,-1),E4=[A4,x4],C4={key:2},T4=h("path",{d:"M144,96V80H128a8,8,0,0,0-8,8v80a8,8,0,0,0,8,8h16V160a16,16,0,0,1,16-16h48a16,16,0,0,1,16,16v48a16,16,0,0,1-16,16H160a16,16,0,0,1-16-16V192H128a24,24,0,0,1-24-24V136H72v8a16,16,0,0,1-16,16H24A16,16,0,0,1,8,144V112A16,16,0,0,1,24,96H56a16,16,0,0,1,16,16v8h32V88a24,24,0,0,1,24-24h16V48a16,16,0,0,1,16-16h48a16,16,0,0,1,16,16V96a16,16,0,0,1-16,16H160A16,16,0,0,1,144,96Z"},null,-1),O4=[T4],P4={key:3},R4=h("path",{d:"M160,110h48a14,14,0,0,0,14-14V48a14,14,0,0,0-14-14H160a14,14,0,0,0-14,14V66H128a22,22,0,0,0-22,22v34H70V112A14,14,0,0,0,56,98H24a14,14,0,0,0-14,14v32a14,14,0,0,0,14,14H56a14,14,0,0,0,14-14V134h36v34a22,22,0,0,0,22,22h18v18a14,14,0,0,0,14,14h48a14,14,0,0,0,14-14V160a14,14,0,0,0-14-14H160a14,14,0,0,0-14,14v18H128a10,10,0,0,1-10-10V88a10,10,0,0,1,10-10h18V96A14,14,0,0,0,160,110ZM58,144a2,2,0,0,1-2,2H24a2,2,0,0,1-2-2V112a2,2,0,0,1,2-2H56a2,2,0,0,1,2,2Zm100,16a2,2,0,0,1,2-2h48a2,2,0,0,1,2,2v48a2,2,0,0,1-2,2H160a2,2,0,0,1-2-2Zm0-112a2,2,0,0,1,2-2h48a2,2,0,0,1,2,2V96a2,2,0,0,1-2,2H160a2,2,0,0,1-2-2Z"},null,-1),M4=[R4],k4={key:4},L4=h("path",{d:"M160,112h48a16,16,0,0,0,16-16V48a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16V64H128a24,24,0,0,0-24,24v32H72v-8A16,16,0,0,0,56,96H24A16,16,0,0,0,8,112v32a16,16,0,0,0,16,16H56a16,16,0,0,0,16-16v-8h32v32a24,24,0,0,0,24,24h16v16a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V160a16,16,0,0,0-16-16H160a16,16,0,0,0-16,16v16H128a8,8,0,0,1-8-8V88a8,8,0,0,1,8-8h16V96A16,16,0,0,0,160,112ZM56,144H24V112H56v32Zm104,16h48v48H160Zm0-112h48V96H160Z"},null,-1),I4=[L4],N4={key:5},H4=h("path",{d:"M160,108h48a12,12,0,0,0,12-12V48a12,12,0,0,0-12-12H160a12,12,0,0,0-12,12V68H128a20,20,0,0,0-20,20v36H68V112a12,12,0,0,0-12-12H24a12,12,0,0,0-12,12v32a12,12,0,0,0,12,12H56a12,12,0,0,0,12-12V132h40v36a20,20,0,0,0,20,20h20v20a12,12,0,0,0,12,12h48a12,12,0,0,0,12-12V160a12,12,0,0,0-12-12H160a12,12,0,0,0-12,12v20H128a12,12,0,0,1-12-12V88a12,12,0,0,1,12-12h20V96A12,12,0,0,0,160,108ZM60,144a4,4,0,0,1-4,4H24a4,4,0,0,1-4-4V112a4,4,0,0,1,4-4H56a4,4,0,0,1,4,4Zm96,16a4,4,0,0,1,4-4h48a4,4,0,0,1,4,4v48a4,4,0,0,1-4,4H160a4,4,0,0,1-4-4Zm0-112a4,4,0,0,1,4-4h48a4,4,0,0,1,4,4V96a4,4,0,0,1-4,4H160a4,4,0,0,1-4-4Z"},null,-1),F4=[H4],V4={name:"PhTreeStructure"},Ua=_e({...V4,props:{weight:{type:String},size:{type:[String,Number]},color:{type:String},mirrored:{type:Boolean}},setup(e){const t=e,n=ae("weight","regular"),r=ae("size","1em"),o=ae("color","currentColor"),s=ae("mirrored",!1),i=O(()=>t.weight??n),l=O(()=>t.size??r),a=O(()=>t.color??o),u=O(()=>t.mirrored!==void 0?t.mirrored?"scale(-1, 1)":void 0:s?"scale(-1, 1)":void 0);return(c,f)=>(x(),T("svg",dt({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 256 256",width:l.value,height:l.value,fill:a.value,transform:u.value},c.$attrs),[Ut(c.$slots,"default"),i.value==="bold"?(x(),T("g",w4,S4)):i.value==="duotone"?(x(),T("g",$4,E4)):i.value==="fill"?(x(),T("g",C4,O4)):i.value==="light"?(x(),T("g",P4,M4)):i.value==="regular"?(x(),T("g",k4,I4)):i.value==="thin"?(x(),T("g",N4,F4)):ce("",!0)],16,b4))}});function Za(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"fill-rule":"evenodd",d:"M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z","clip-rule":"evenodd"})])}const D4={class:"fixed inset-0 overflow-y-auto"},B4={class:"flex min-h-full items-center justify-center p-4"},j4={class:"flex flex-col gap-6 items-center h-full"},z4={class:"flex items-center justify-between w-full",style:{position:"fixed",top:"35px",padding:"0 34px"}},U4=["src"],Z4={__name:"MenuMobile",emits:["scrollRequest"],setup(e,{expose:t,emit:n}){const r=F(!1);ka();const o=r0();function s(){r.value=!0}function i(){r.value=!1}function l(a){o.push(a),i()}return t({openModal:s}),(a,u)=>{const c=rn("RouterLink");return x(),tt(R(Zr),{appear:"",show:r.value,as:"template"},{default:X(()=>[M(R(qo),{as:"div",onClose:u[4]||(u[4]=()=>i()),class:"relative z-10"},{default:X(()=>[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:X(()=>u[5]||(u[5]=[h("div",{class:"fixed inset-0",style:{"background-color":"rgba(255,255,255, 0.9)"}},null,-1)])),_:1}),h("div",D4,[h("div",B4,[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:X(()=>[h("div",j4,[h("div",null,[h("a",{class:"cursor-pointer btn btn-outline-inv",onClick:u[0]||(u[0]=f=>l("/corsi-attivi"))},"Corsi attivi")]),h("div",null,[h("a",{class:"cursor-pointer btn btn-outline-inv",onClick:u[1]||(u[1]=f=>l("/prenotazioni"))},"Le tue prenotazioni")]),M(R(S0),{as:"div",class:"relative"},{default:X(()=>[h("div",null,[M(R($0),{class:"flex items-center gap-x-3 btn btn-outline-inv"},{default:X(()=>[u[6]||(u[6]=oe(" Approfondimenti ")),M(R(Za),{class:"h-5 w-5 text-[#00624A]","aria-hidden":"true"})]),_:1})]),M(ri,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:X(()=>[M(R(A0),{class:"mt-5 p-2 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none"},{default:X(()=>[M(R(Ir),null,{default:X(({active:f})=>[M(c,{to:{name:"commercial"},class:"flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[M(R(za),{size:"28",class:"mr-5"}),u[7]||(u[7]=h("span",{class:"text-left leading-tight"},[oe("Ambito"),h("br"),oe("commerciale")],-1))]),_:1})]),_:1}),M(R(Ir),null,{default:X(({active:f})=>[M(c,{to:{name:"products"},class:"flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[M(R(ja),{size:"28",class:"mr-5"}),u[8]||(u[8]=h("span",{class:"text-left leading-tight"},[oe("Conoscere"),h("br"),oe("i prodotti")],-1))]),_:1})]),_:1}),M(R(Ir),null,{default:X(({active:f})=>[M(c,{to:{name:"operative"},class:"flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[M(R(Ua),{size:"28",class:"mr-5"}),u[9]||(u[9]=h("span",{class:"text-left leading-tight"},[oe("Novità operative"),h("br"),oe("e di processo")],-1))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),h("div",null,[h("a",{class:"cursor-pointer btn btn-outline-inv",onClick:u[2]||(u[2]=f=>l("/cos'è-uniqum"))},"Cos'è Uniqum")]),h("div",null,[h("a",{class:"cursor-pointer btn btn-outline-inv",onClick:u[3]||(u[3]=f=>l("/team"))},"Il tuo team")])])]),_:1})])]),h("div",z4,[h("img",{src:"https://"+a.window.staticHost+"/themes/uniqum/uniqum-logo-inv.svg",alt:"Uniqum"},null,8,U4),M(R(s0),{class:"size-10",style:{color:"#14513A"},onClick:i})])]),_:1})]),_:1},8,["show"])}}};function V0(e,t){return function(){return e.apply(t,arguments)}}const{toString:q4}=Object.prototype,{getPrototypeOf:qa}=Object,{iterator:pi,toStringTag:D0}=Symbol,hi=(e=>t=>{const n=q4.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),ln=e=>(e=e.toLowerCase(),t=>hi(t)===e),vi=e=>t=>typeof t===e,{isArray:qr}=Array,Po=vi("undefined");function W4(e){return e!==null&&!Po(e)&&e.constructor!==null&&!Po(e.constructor)&&Ht(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const B0=ln("ArrayBuffer");function G4(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&B0(e.buffer),t}const K4=vi("string"),Ht=vi("function"),j0=vi("number"),mi=e=>e!==null&&typeof e=="object",Y4=e=>e===!0||e===!1,ys=e=>{if(hi(e)!=="object")return!1;const t=qa(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(D0 in e)&&!(pi in e)},J4=ln("Date"),X4=ln("File"),Q4=ln("Blob"),e8=ln("FileList"),t8=e=>mi(e)&&Ht(e.pipe),n8=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ht(e.append)&&((t=hi(e))==="formdata"||t==="object"&&Ht(e.toString)&&e.toString()==="[object FormData]"))},r8=ln("URLSearchParams"),[o8,s8,i8,l8]=["ReadableStream","Request","Response","Headers"].map(ln),a8=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Go(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),qr(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let l;for(r=0;r<i;r++)l=s[r],t.call(null,e[l],l,e)}}function z0(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const fr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,U0=e=>!Po(e)&&e!==fr;function Nl(){const{caseless:e}=U0(this)&&this||{},t={},n=(r,o)=>{const s=e&&z0(t,o)||o;ys(t[s])&&ys(r)?t[s]=Nl(t[s],r):ys(r)?t[s]=Nl({},r):qr(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&Go(arguments[r],n);return t}const u8=(e,t,n,{allOwnKeys:r}={})=>(Go(t,(o,s)=>{n&&Ht(o)?e[s]=V0(o,n):e[s]=o},{allOwnKeys:r}),e),c8=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),d8=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},f8=(e,t,n,r)=>{let o,s,i;const l={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&qa(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},p8=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},h8=e=>{if(!e)return null;if(qr(e))return e;let t=e.length;if(!j0(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},v8=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&qa(Uint8Array)),m8=(e,t)=>{const r=(e&&e[pi]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},g8=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},y8=ln("HTMLFormElement"),b8=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),$c=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),w8=ln("RegExp"),Z0=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Go(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},_8=e=>{Z0(e,(t,n)=>{if(Ht(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Ht(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},S8=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return qr(e)?r(e):r(String(e).split(t)),n},$8=()=>{},A8=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function x8(e){return!!(e&&Ht(e.append)&&e[D0]==="FormData"&&e[pi])}const E8=e=>{const t=new Array(10),n=(r,o)=>{if(mi(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=qr(r)?[]:{};return Go(r,(i,l)=>{const a=n(i,o+1);!Po(a)&&(s[l]=a)}),t[o]=void 0,s}}return r};return n(e,0)},C8=ln("AsyncFunction"),T8=e=>e&&(mi(e)||Ht(e))&&Ht(e.then)&&Ht(e.catch),q0=((e,t)=>e?setImmediate:t?((n,r)=>(fr.addEventListener("message",({source:o,data:s})=>{o===fr&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),fr.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ht(fr.postMessage)),O8=typeof queueMicrotask<"u"?queueMicrotask.bind(fr):typeof process<"u"&&process.nextTick||q0,P8=e=>e!=null&&Ht(e[pi]),L={isArray:qr,isArrayBuffer:B0,isBuffer:W4,isFormData:n8,isArrayBufferView:G4,isString:K4,isNumber:j0,isBoolean:Y4,isObject:mi,isPlainObject:ys,isReadableStream:o8,isRequest:s8,isResponse:i8,isHeaders:l8,isUndefined:Po,isDate:J4,isFile:X4,isBlob:Q4,isRegExp:w8,isFunction:Ht,isStream:t8,isURLSearchParams:r8,isTypedArray:v8,isFileList:e8,forEach:Go,merge:Nl,extend:u8,trim:a8,stripBOM:c8,inherits:d8,toFlatObject:f8,kindOf:hi,kindOfTest:ln,endsWith:p8,toArray:h8,forEachEntry:m8,matchAll:g8,isHTMLForm:y8,hasOwnProperty:$c,hasOwnProp:$c,reduceDescriptors:Z0,freezeMethods:_8,toObjectSet:S8,toCamelCase:b8,noop:$8,toFiniteNumber:A8,findKey:z0,global:fr,isContextDefined:U0,isSpecCompliantForm:x8,toJSONObject:E8,isAsyncFn:C8,isThenable:T8,setImmediate:q0,asap:O8,isIterable:P8};function Te(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}L.inherits(Te,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:L.toJSONObject(this.config),code:this.code,status:this.status}}});const W0=Te.prototype,G0={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{G0[e]={value:e}});Object.defineProperties(Te,G0);Object.defineProperty(W0,"isAxiosError",{value:!0});Te.from=(e,t,n,r,o,s)=>{const i=Object.create(W0);return L.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),Te.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const R8=null;function Hl(e){return L.isPlainObject(e)||L.isArray(e)}function K0(e){return L.endsWith(e,"[]")?e.slice(0,-2):e}function Ac(e,t,n){return e?e.concat(t).map(function(o,s){return o=K0(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function M8(e){return L.isArray(e)&&!e.some(Hl)}const k8=L.toFlatObject(L,{},null,function(t){return/^is[A-Z]/.test(t)});function gi(e,t,n){if(!L.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=L.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(m,g){return!L.isUndefined(g[m])});const r=n.metaTokens,o=n.visitor||c,s=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&L.isSpecCompliantForm(t);if(!L.isFunction(o))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(L.isDate(v))return v.toISOString();if(L.isBoolean(v))return v.toString();if(!a&&L.isBlob(v))throw new Te("Blob is not supported. Use a Buffer instead.");return L.isArrayBuffer(v)||L.isTypedArray(v)?a&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,m,g){let b=v;if(v&&!g&&typeof v=="object"){if(L.endsWith(m,"{}"))m=r?m:m.slice(0,-2),v=JSON.stringify(v);else if(L.isArray(v)&&M8(v)||(L.isFileList(v)||L.endsWith(m,"[]"))&&(b=L.toArray(v)))return m=K0(m),b.forEach(function(S,A){!(L.isUndefined(S)||S===null)&&t.append(i===!0?Ac([m],A,s):i===null?m:m+"[]",u(S))}),!1}return Hl(v)?!0:(t.append(Ac(g,m,s),u(v)),!1)}const f=[],d=Object.assign(k8,{defaultVisitor:c,convertValue:u,isVisitable:Hl});function p(v,m){if(!L.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+m.join("."));f.push(v),L.forEach(v,function(b,y){(!(L.isUndefined(b)||b===null)&&o.call(t,b,L.isString(y)?y.trim():y,m,d))===!0&&p(b,m?m.concat(y):[y])}),f.pop()}}if(!L.isObject(e))throw new TypeError("data must be an object");return p(e),t}function xc(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Wa(e,t){this._pairs=[],e&&gi(e,this,t)}const Y0=Wa.prototype;Y0.append=function(t,n){this._pairs.push([t,n])};Y0.toString=function(t){const n=t?function(r){return t.call(this,r,xc)}:xc;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function L8(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function J0(e,t,n){if(!t)return e;const r=n&&n.encode||L8;L.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=L.isURLSearchParams(t)?t.toString():new Wa(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Ec{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){L.forEach(this.handlers,function(r){r!==null&&t(r)})}}const X0={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},I8=typeof URLSearchParams<"u"?URLSearchParams:Wa,N8=typeof FormData<"u"?FormData:null,H8=typeof Blob<"u"?Blob:null,F8={isBrowser:!0,classes:{URLSearchParams:I8,FormData:N8,Blob:H8},protocols:["http","https","file","blob","url","data"]},Ga=typeof window<"u"&&typeof document<"u",Fl=typeof navigator=="object"&&navigator||void 0,V8=Ga&&(!Fl||["ReactNative","NativeScript","NS"].indexOf(Fl.product)<0),D8=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",B8=Ga&&window.location.href||"http://localhost",j8=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Ga,hasStandardBrowserEnv:V8,hasStandardBrowserWebWorkerEnv:D8,navigator:Fl,origin:B8},Symbol.toStringTag,{value:"Module"})),xt={...j8,...F8};function z8(e,t){return gi(e,new xt.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return xt.isNode&&L.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function U8(e){return L.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Z8(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function Q0(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=s>=n.length;return i=!i&&L.isArray(o)?o.length:i,a?(L.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!l):((!o[i]||!L.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&L.isArray(o[i])&&(o[i]=Z8(o[i])),!l)}if(L.isFormData(e)&&L.isFunction(e.entries)){const n={};return L.forEachEntry(e,(r,o)=>{t(U8(r),o,n,0)}),n}return null}function q8(e,t,n){if(L.isString(e))try{return(t||JSON.parse)(e),L.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Ko={transitional:X0,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=L.isObject(t);if(s&&L.isHTMLForm(t)&&(t=new FormData(t)),L.isFormData(t))return o?JSON.stringify(Q0(t)):t;if(L.isArrayBuffer(t)||L.isBuffer(t)||L.isStream(t)||L.isFile(t)||L.isBlob(t)||L.isReadableStream(t))return t;if(L.isArrayBufferView(t))return t.buffer;if(L.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return z8(t,this.formSerializer).toString();if((l=L.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return gi(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),q8(t)):t}],transformResponse:[function(t){const n=this.transitional||Ko.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(L.isResponse(t)||L.isReadableStream(t))return t;if(t&&L.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?Te.from(l,Te.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:xt.classes.FormData,Blob:xt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};L.forEach(["delete","get","head","post","put","patch"],e=>{Ko.headers[e]={}});const W8=L.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),G8=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&W8[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Cc=Symbol("internals");function to(e){return e&&String(e).trim().toLowerCase()}function bs(e){return e===!1||e==null?e:L.isArray(e)?e.map(bs):String(e)}function K8(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Y8=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Qi(e,t,n,r,o){if(L.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!L.isString(t)){if(L.isString(r))return t.indexOf(r)!==-1;if(L.isRegExp(r))return r.test(t)}}function J8(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function X8(e,t){const n=L.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}let Ft=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(l,a,u){const c=to(a);if(!c)throw new Error("header name must be a non-empty string");const f=L.findKey(o,c);(!f||o[f]===void 0||u===!0||u===void 0&&o[f]!==!1)&&(o[f||a]=bs(l))}const i=(l,a)=>L.forEach(l,(u,c)=>s(u,c,a));if(L.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(L.isString(t)&&(t=t.trim())&&!Y8(t))i(G8(t),n);else if(L.isObject(t)&&L.isIterable(t)){let l={},a,u;for(const c of t){if(!L.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?L.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=to(t),t){const r=L.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return K8(o);if(L.isFunction(n))return n.call(this,o,r);if(L.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=to(t),t){const r=L.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Qi(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=to(i),i){const l=L.findKey(r,i);l&&(!n||Qi(r,r[l],l,n))&&(delete r[l],o=!0)}}return L.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||Qi(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return L.forEach(this,(o,s)=>{const i=L.findKey(r,s);if(i){n[i]=bs(o),delete n[s];return}const l=t?J8(s):String(s).trim();l!==s&&delete n[s],n[l]=bs(o),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return L.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&L.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[Cc]=this[Cc]={accessors:{}}).accessors,o=this.prototype;function s(i){const l=to(i);r[l]||(X8(o,i),r[l]=!0)}return L.isArray(t)?t.forEach(s):s(t),this}};Ft.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);L.reduceDescriptors(Ft.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});L.freezeMethods(Ft);function el(e,t){const n=this||Ko,r=t||n,o=Ft.from(r.headers);let s=r.data;return L.forEach(e,function(l){s=l.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function ep(e){return!!(e&&e.__CANCEL__)}function Wr(e,t,n){Te.call(this,e??"canceled",Te.ERR_CANCELED,t,n),this.name="CanceledError"}L.inherits(Wr,Te,{__CANCEL__:!0});function tp(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Te("Request failed with status code "+n.status,[Te.ERR_BAD_REQUEST,Te.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Q8(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function e6(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=r[s];i||(i=u),n[o]=a,r[o]=u;let f=s,d=0;for(;f!==o;)d+=n[f++],f=f%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),u-i<t)return;const p=c&&u-c;return p?Math.round(d*1e3/p):void 0}}function t6(e,t){let n=0,r=1e3/t,o,s;const i=(u,c=Date.now())=>{n=c,o=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=r?i(u,c):(o=u,s||(s=setTimeout(()=>{s=null,i(o)},r-f)))},()=>o&&i(o)]}const Ms=(e,t,n=3)=>{let r=0;const o=e6(50,250);return t6(s=>{const i=s.loaded,l=s.lengthComputable?s.total:void 0,a=i-r,u=o(a),c=i<=l;r=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:s,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},Tc=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Oc=e=>(...t)=>L.asap(()=>e(...t)),n6=xt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,xt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(xt.origin),xt.navigator&&/(msie|trident)/i.test(xt.navigator.userAgent)):()=>!0,r6=xt.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];L.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),L.isString(r)&&i.push("path="+r),L.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function o6(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function s6(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function np(e,t,n){let r=!o6(t);return e&&(r||n==!1)?s6(e,t):t}const Pc=e=>e instanceof Ft?{...e}:e;function wr(e,t){t=t||{};const n={};function r(u,c,f,d){return L.isPlainObject(u)&&L.isPlainObject(c)?L.merge.call({caseless:d},u,c):L.isPlainObject(c)?L.merge({},c):L.isArray(c)?c.slice():c}function o(u,c,f,d){if(L.isUndefined(c)){if(!L.isUndefined(u))return r(void 0,u,f,d)}else return r(u,c,f,d)}function s(u,c){if(!L.isUndefined(c))return r(void 0,c)}function i(u,c){if(L.isUndefined(c)){if(!L.isUndefined(u))return r(void 0,u)}else return r(void 0,c)}function l(u,c,f){if(f in t)return r(u,c);if(f in e)return r(void 0,u)}const a={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>o(Pc(u),Pc(c),f,!0)};return L.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||o,d=f(e[c],t[c],c);L.isUndefined(d)&&f!==l||(n[c]=d)}),n}const rp=e=>{const t=wr({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:l}=t;t.headers=i=Ft.from(i),t.url=J0(np(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(L.isFormData(n)){if(xt.hasStandardBrowserEnv||xt.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(xt.hasStandardBrowserEnv&&(r&&L.isFunction(r)&&(r=r(t)),r||r!==!1&&n6(t.url))){const u=o&&s&&r6.read(s);u&&i.set(o,u)}return t},i6=typeof XMLHttpRequest<"u",l6=i6&&function(e){return new Promise(function(n,r){const o=rp(e);let s=o.data;const i=Ft.from(o.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=o,c,f,d,p,v;function m(){p&&p(),v&&v(),o.cancelToken&&o.cancelToken.unsubscribe(c),o.signal&&o.signal.removeEventListener("abort",c)}let g=new XMLHttpRequest;g.open(o.method.toUpperCase(),o.url,!0),g.timeout=o.timeout;function b(){if(!g)return;const S=Ft.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders()),$={data:!l||l==="text"||l==="json"?g.responseText:g.response,status:g.status,statusText:g.statusText,headers:S,config:e,request:g};tp(function(H){n(H),m()},function(H){r(H),m()},$),g=null}"onloadend"in g?g.onloadend=b:g.onreadystatechange=function(){!g||g.readyState!==4||g.status===0&&!(g.responseURL&&g.responseURL.indexOf("file:")===0)||setTimeout(b)},g.onabort=function(){g&&(r(new Te("Request aborted",Te.ECONNABORTED,e,g)),g=null)},g.onerror=function(){r(new Te("Network Error",Te.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let A=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const $=o.transitional||X0;o.timeoutErrorMessage&&(A=o.timeoutErrorMessage),r(new Te(A,$.clarifyTimeoutError?Te.ETIMEDOUT:Te.ECONNABORTED,e,g)),g=null},s===void 0&&i.setContentType(null),"setRequestHeader"in g&&L.forEach(i.toJSON(),function(A,$){g.setRequestHeader($,A)}),L.isUndefined(o.withCredentials)||(g.withCredentials=!!o.withCredentials),l&&l!=="json"&&(g.responseType=o.responseType),u&&([d,v]=Ms(u,!0),g.addEventListener("progress",d)),a&&g.upload&&([f,p]=Ms(a),g.upload.addEventListener("progress",f),g.upload.addEventListener("loadend",p)),(o.cancelToken||o.signal)&&(c=S=>{g&&(r(!S||S.type?new Wr(null,e,g):S),g.abort(),g=null)},o.cancelToken&&o.cancelToken.subscribe(c),o.signal&&(o.signal.aborted?c():o.signal.addEventListener("abort",c)));const y=Q8(o.url);if(y&&xt.protocols.indexOf(y)===-1){r(new Te("Unsupported protocol "+y+":",Te.ERR_BAD_REQUEST,e));return}g.send(s||null)})},a6=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(u){if(!o){o=!0,l();const c=u instanceof Error?u:this.reason;r.abort(c instanceof Te?c:new Wr(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,s(new Te(`timeout ${t} of ms exceeded`,Te.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:a}=r;return a.unsubscribe=()=>L.asap(l),a}},u6=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},c6=async function*(e,t){for await(const n of d6(e))yield*u6(n,t)},d6=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Rc=(e,t,n,r)=>{const o=c6(e,t);let s=0,i,l=a=>{i||(i=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await o.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let d=s+=f;n(d)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),o.return()}},{highWaterMark:2})},yi=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",op=yi&&typeof ReadableStream=="function",f6=yi&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),sp=(e,...t)=>{try{return!!e(...t)}catch{return!1}},p6=op&&sp(()=>{let e=!1;const t=new Request(xt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Mc=64*1024,Vl=op&&sp(()=>L.isReadableStream(new Response("").body)),ks={stream:Vl&&(e=>e.body)};yi&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ks[t]&&(ks[t]=L.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new Te(`Response type '${t}' is not supported`,Te.ERR_NOT_SUPPORT,r)})})})(new Response);const h6=async e=>{if(e==null)return 0;if(L.isBlob(e))return e.size;if(L.isSpecCompliantForm(e))return(await new Request(xt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(L.isArrayBufferView(e)||L.isArrayBuffer(e))return e.byteLength;if(L.isURLSearchParams(e)&&(e=e+""),L.isString(e))return(await f6(e)).byteLength},v6=async(e,t)=>{const n=L.toFiniteNumber(e.getContentLength());return n??h6(t)},m6=yi&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=rp(e);u=u?(u+"").toLowerCase():"text";let p=a6([o,s&&s.toAbortSignal()],i),v;const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let g;try{if(a&&p6&&n!=="get"&&n!=="head"&&(g=await v6(c,r))!==0){let $=new Request(t,{method:"POST",body:r,duplex:"half"}),I;if(L.isFormData(r)&&(I=$.headers.get("content-type"))&&c.setContentType(I),$.body){const[H,U]=Tc(g,Ms(Oc(a)));r=Rc($.body,Mc,H,U)}}L.isString(f)||(f=f?"include":"omit");const b="credentials"in Request.prototype;v=new Request(t,{...d,signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:b?f:void 0});let y=await fetch(v,d);const S=Vl&&(u==="stream"||u==="response");if(Vl&&(l||S&&m)){const $={};["status","statusText","headers"].forEach(N=>{$[N]=y[N]});const I=L.toFiniteNumber(y.headers.get("content-length")),[H,U]=l&&Tc(I,Ms(Oc(l),!0))||[];y=new Response(Rc(y.body,Mc,H,()=>{U&&U(),m&&m()}),$)}u=u||"text";let A=await ks[L.findKey(ks,u)||"text"](y,e);return!S&&m&&m(),await new Promise(($,I)=>{tp($,I,{data:A,headers:Ft.from(y.headers),status:y.status,statusText:y.statusText,config:e,request:v})})}catch(b){throw m&&m(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new Te("Network Error",Te.ERR_NETWORK,e,v),{cause:b.cause||b}):Te.from(b,b&&b.code,e,v)}}),Dl={http:R8,xhr:l6,fetch:m6};L.forEach(Dl,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const kc=e=>`- ${e}`,g6=e=>L.isFunction(e)||e===null||e===!1,ip={getAdapter:e=>{e=L.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!g6(n)&&(r=Dl[(i=String(n)).toLowerCase()],r===void 0))throw new Te(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(kc).join(`
`):" "+kc(s[0]):"as no adapter specified";throw new Te("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:Dl};function tl(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Wr(null,e)}function Lc(e){return tl(e),e.headers=Ft.from(e.headers),e.data=el.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ip.getAdapter(e.adapter||Ko.adapter)(e).then(function(r){return tl(e),r.data=el.call(e,e.transformResponse,r),r.headers=Ft.from(r.headers),r},function(r){return ep(r)||(tl(e),r&&r.response&&(r.response.data=el.call(e,e.transformResponse,r.response),r.response.headers=Ft.from(r.response.headers))),Promise.reject(r)})}const lp="1.10.0",bi={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{bi[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ic={};bi.transitional=function(t,n,r){function o(s,i){return"[Axios v"+lp+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,l)=>{if(t===!1)throw new Te(o(i," has been removed"+(n?" in "+n:"")),Te.ERR_DEPRECATED);return n&&!Ic[i]&&(Ic[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,l):!0}};bi.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function y6(e,t,n){if(typeof e!="object")throw new Te("options must be an object",Te.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const l=e[s],a=l===void 0||i(l,s,e);if(a!==!0)throw new Te("option "+s+" must be "+a,Te.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Te("Unknown option "+s,Te.ERR_BAD_OPTION)}}const ws={assertOptions:y6,validators:bi},un=ws.validators;let gr=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Ec,response:new Ec}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=wr(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&ws.assertOptions(r,{silentJSONParsing:un.transitional(un.boolean),forcedJSONParsing:un.transitional(un.boolean),clarifyTimeoutError:un.transitional(un.boolean)},!1),o!=null&&(L.isFunction(o)?n.paramsSerializer={serialize:o}:ws.assertOptions(o,{encode:un.function,serialize:un.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ws.assertOptions(n,{baseUrl:un.spelling("baseURL"),withXsrfToken:un.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&L.merge(s.common,s[n.method]);s&&L.forEach(["delete","get","head","post","put","patch","common"],v=>{delete s[v]}),n.headers=Ft.concat(i,s);const l=[];let a=!0;this.interceptors.request.forEach(function(m){typeof m.runWhen=="function"&&m.runWhen(n)===!1||(a=a&&m.synchronous,l.unshift(m.fulfilled,m.rejected))});const u=[];this.interceptors.response.forEach(function(m){u.push(m.fulfilled,m.rejected)});let c,f=0,d;if(!a){const v=[Lc.bind(this),void 0];for(v.unshift.apply(v,l),v.push.apply(v,u),d=v.length,c=Promise.resolve(n);f<d;)c=c.then(v[f++],v[f++]);return c}d=l.length;let p=n;for(f=0;f<d;){const v=l[f++],m=l[f++];try{p=v(p)}catch(g){m.call(this,g);break}}try{c=Lc.call(this,p)}catch(v){return Promise.reject(v)}for(f=0,d=u.length;f<d;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=wr(this.defaults,t);const n=np(t.baseURL,t.url,t.allowAbsoluteUrls);return J0(n,t.params,t.paramsSerializer)}};L.forEach(["delete","get","head","options"],function(t){gr.prototype[t]=function(n,r){return this.request(wr(r||{},{method:t,url:n,data:(r||{}).data}))}});L.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,l){return this.request(wr(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}gr.prototype[t]=n(),gr.prototype[t+"Form"]=n(!0)});let b6=class ap{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(l=>{r.subscribe(l),s=l}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,l){r.reason||(r.reason=new Wr(s,i,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new ap(function(o){t=o}),cancel:t}}};function w6(e){return function(n){return e.apply(null,n)}}function _6(e){return L.isObject(e)&&e.isAxiosError===!0}const Bl={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Bl).forEach(([e,t])=>{Bl[t]=e});function up(e){const t=new gr(e),n=V0(gr.prototype.request,t);return L.extend(n,gr.prototype,t,{allOwnKeys:!0}),L.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return up(wr(e,o))},n}const Ce=up(Ko);Ce.Axios=gr;Ce.CanceledError=Wr;Ce.CancelToken=b6;Ce.isCancel=ep;Ce.VERSION=lp;Ce.toFormData=gi;Ce.AxiosError=Te;Ce.Cancel=Ce.CanceledError;Ce.all=function(t){return Promise.all(t)};Ce.spread=w6;Ce.isAxiosError=_6;Ce.mergeConfig=wr;Ce.AxiosHeaders=Ft;Ce.formToJSON=e=>Q0(L.isHTMLForm(e)?new FormData(e):e);Ce.getAdapter=ip.getAdapter;Ce.HttpStatusCode=Bl;Ce.default=Ce;const{Axios:Zx,AxiosError:qx,CanceledError:Wx,isCancel:Gx,CancelToken:Kx,VERSION:Yx,all:Jx,Cancel:Xx,isAxiosError:Qx,spread:eE,toFormData:tE,AxiosHeaders:nE,HttpStatusCode:rE,formToJSON:oE,getAdapter:sE,mergeConfig:iE}=Ce;async function S6(e){var n;const t=(n=await Ce.get(`/apps/uniqum/materials/list/${e}`))==null?void 0:n.data;return t!=null&&t.success?t:[]}async function $6(e){var n;const t=(n=await Ce.get(`/apps/uniqum/materials/main/${e}`))==null?void 0:n.data;return t!=null&&t.success?t:[]}async function A6(){var t;const e=(t=await Ce.get("/apps/uniqum/materials/check"))==null?void 0:t.data;return e!=null&&e.success?e:[]}async function x6(){var t;const e=(t=await Ce.get("/apps/uniqum/materials/highlight"))==null?void 0:t.data;return e!=null&&e.success?e:[]}const Yo={getAll:S6,getMainByCategory:$6,getMaterialsStatus:A6,getHomeHighlightedMaterials:x6},Jo={TOP_LEFT:"top-left",TOP_RIGHT:"top-right",TOP_CENTER:"top-center",BOTTOM_LEFT:"bottom-left",BOTTOM_RIGHT:"bottom-right",BOTTOM_CENTER:"bottom-center"},zr={LIGHT:"light",DARK:"dark",COLORED:"colored",AUTO:"auto"},Mt={INFO:"info",SUCCESS:"success",WARNING:"warning",ERROR:"error",DEFAULT:"default"},E6={BOUNCE:"bounce",SLIDE:"slide",FLIP:"flip",ZOOM:"zoom",NONE:"none"},C6={dangerouslyHTMLString:!1,multiple:!0,position:Jo.TOP_RIGHT,autoClose:5e3,transition:"bounce",hideProgressBar:!1,pauseOnHover:!0,pauseOnFocusLoss:!0,closeOnClick:!0,className:"",bodyClassName:"",style:{},progressClassName:"",progressStyle:{},role:"alert",theme:"light"},T6={rtl:!1,newestOnTop:!1,toastClassName:""},cp={...C6,...T6};Mt.DEFAULT;var ze=(e=>(e[e.COLLAPSE_DURATION=300]="COLLAPSE_DURATION",e[e.DEBOUNCE_DURATION=50]="DEBOUNCE_DURATION",e.CSS_NAMESPACE="Toastify",e))(ze||{}),jl=(e=>(e.ENTRANCE_ANIMATION_END="d",e))(jl||{});const O6={enter:"Toastify--animate Toastify__bounce-enter",exit:"Toastify--animate Toastify__bounce-exit",appendPosition:!0},P6={enter:"Toastify--animate Toastify__slide-enter",exit:"Toastify--animate Toastify__slide-exit",appendPosition:!0},R6={enter:"Toastify--animate Toastify__zoom-enter",exit:"Toastify--animate Toastify__zoom-exit"},M6={enter:"Toastify--animate Toastify__flip-enter",exit:"Toastify--animate Toastify__flip-exit"},Nc="Toastify--animate Toastify__none-enter";function dp(e,t=!1){var n;let r=O6;if(!e||typeof e=="string")switch(e){case"flip":r=M6;break;case"zoom":r=R6;break;case"slide":r=P6;break}else r=e;if(t)r.enter=Nc;else if(r.enter===Nc){const o=(n=r.exit.split("__")[1])==null?void 0:n.split("-")[0];r.enter=`Toastify--animate Toastify__${o}-enter`}return r}function k6(e){return e.containerId||String(e.position)}const wi="will-unmount";function L6(e=Jo.TOP_RIGHT){return!!document.querySelector(`.${ze.CSS_NAMESPACE}__toast-container--${e}`)}function I6(e=Jo.TOP_RIGHT){return`${ze.CSS_NAMESPACE}__toast-container--${e}`}function N6(e,t,n=!1){const r=[`${ze.CSS_NAMESPACE}__toast-container`,`${ze.CSS_NAMESPACE}__toast-container--${e}`,n?`${ze.CSS_NAMESPACE}__toast-container--rtl`:null].filter(Boolean).join(" ");return Hr(t)?t({position:e,rtl:n,defaultClassName:r}):`${r} ${t||""}`}function H6(e){var t;const{position:n,containerClassName:r,rtl:o=!1,style:s={}}=e,i=ze.CSS_NAMESPACE,l=I6(n),a=document.querySelector(`.${i}`),u=document.querySelector(`.${l}`),c=!!u&&!((t=u.className)!=null&&t.includes(wi)),f=a||document.createElement("div"),d=document.createElement("div");d.className=N6(n,r,o),d.dataset.testid=`${ze.CSS_NAMESPACE}__toast-container--${n}`,d.id=k6(e);for(const p in s)if(Object.prototype.hasOwnProperty.call(s,p)){const v=s[p];d.style[p]=v}return a||(f.className=ze.CSS_NAMESPACE,document.body.appendChild(f)),c||f.appendChild(d),d}function zl(e){var t,n,r;const o=typeof e=="string"?e:((t=e.currentTarget)==null?void 0:t.id)||((n=e.target)==null?void 0:n.id),s=document.getElementById(o);s&&s.removeEventListener("animationend",zl,!1);try{Ro[o].unmount(),(r=document.getElementById(o))==null||r.remove(),delete Ro[o],delete bt[o]}catch{}}const Ro=_t({});function F6(e,t){const n=document.getElementById(String(t));n&&(Ro[n.id]=e)}function Ul(e,t=!0){const n=String(e);if(!Ro[n])return;const r=document.getElementById(n);r&&r.classList.add(wi),t?(D6(e),r&&r.addEventListener("animationend",zl,!1)):zl(n),gn.items=gn.items.filter(o=>o.containerId!==e)}function V6(e){for(const t in Ro)Ul(t,e);gn.items=[]}function fp(e,t){const n=document.getElementById(e.toastId);if(n){let r=e;r={...r,...dp(r.transition)};const o=r.appendPosition?`${r.exit}--${r.position}`:r.exit;n.className+=` ${o}`,t&&t(n)}}function D6(e){for(const t in bt)if(t===e)for(const n of bt[t]||[])fp(n)}function B6(e){const t=Ur().find(n=>n.toastId===e);return t==null?void 0:t.containerId}function Ka(e){return document.getElementById(e)}function j6(e){const t=Ka(e.containerId);return t&&t.classList.contains(wi)}function Hc(e){var t;const n=Dr(e.content)?xe(e.content.props):null;return n??xe((t=e.data)!=null?t:{})}function z6(e){return e?gn.items.filter(t=>t.containerId===e).length>0:gn.items.length>0}function U6(){if(gn.items.length>0){const e=gn.items.shift();_s(e==null?void 0:e.toastContent,e==null?void 0:e.toastProps)}}const bt=_t({}),gn=_t({items:[]});function Ur(){const e=xe(bt);return Object.values(e).reduce((t,n)=>[...t,...n],[])}function Z6(e){return Ur().find(t=>t.toastId===e)}function _s(e,t={}){if(j6(t)){const n=Ka(t.containerId);n&&n.addEventListener("animationend",Zl.bind(null,e,t),!1)}else Zl(e,t)}function Zl(e,t={}){const n=Ka(t.containerId);n&&n.removeEventListener("animationend",Zl.bind(null,e,t),!1);const r=bt[t.containerId]||[],o=r.length>0;if(!o&&!L6(t.position)){const s=H6(t),i=Pa(cw,t);i.mount(s),F6(i,s.id)}o&&!t.updateId&&(t.position=r[0].position),et(()=>{t.updateId?Dt.update(t):Dt.add(e,t)})}const Dt={add(e,t){const{containerId:n=""}=t;n&&(bt[n]=bt[n]||[],bt[n].find(r=>r.toastId===t.toastId)||setTimeout(()=>{var r,o;t.newestOnTop?(r=bt[n])==null||r.unshift(t):(o=bt[n])==null||o.push(t),t.onOpen&&t.onOpen(Hc(t))},t.delay||0))},remove(e){if(e){const t=B6(e);if(t){const n=bt[t];let r=n.find(o=>o.toastId===e);bt[t]=n.filter(o=>o.toastId!==e),!bt[t].length&&!z6(t)&&Ul(t,!1),U6(),et(()=>{r!=null&&r.onClose&&(r.onClose(Hc(r)),r=void 0)})}}},update(e={}){const{containerId:t=""}=e;if(t&&e.updateId){bt[t]=bt[t]||[];const n=bt[t].find(s=>s.toastId===e.toastId),r=(n==null?void 0:n.position)!==e.position||(n==null?void 0:n.transition)!==e.transition,o={...e,disabledEnterTransition:!r,updateId:void 0};Dt.dismissForce(e==null?void 0:e.toastId),setTimeout(()=>{Pe(o.content,o)},e.delay||0)}},clear(e,t=!0){e?Ul(e,t):V6(t)},dismissCallback(e){var t;const n=(t=e.currentTarget)==null?void 0:t.id,r=document.getElementById(n);r&&(r.removeEventListener("animationend",Dt.dismissCallback,!1),setTimeout(()=>{Dt.remove(n)}))},dismiss(e){if(e){const t=Ur();for(const n of t)if(n.toastId===e){fp(n,r=>{r.addEventListener("animationend",Dt.dismissCallback,!1)});break}}},dismissForce(e){if(e){const t=Ur();for(const n of t)if(n.toastId===e){const r=document.getElementById(e);r&&(r.remove(),r.removeEventListener("animationend",Dt.dismissCallback,!1),Dt.remove(e));break}}}},pp=_t({}),Ls=_t({});function hp(){return Math.random().toString(36).substring(2,9)}function q6(e){return typeof e=="number"&&!isNaN(e)}function ql(e){return typeof e=="string"}function Hr(e){return typeof e=="function"}function _i(...e){return dt(...e)}function Ss(e){return typeof e=="object"&&(!!(e!=null&&e.render)||!!(e!=null&&e.setup)||typeof(e==null?void 0:e.type)=="object")}function W6(e={}){pp[`${ze.CSS_NAMESPACE}-default-options`]=e}function G6(){return pp[`${ze.CSS_NAMESPACE}-default-options`]||cp}function K6(){return document.documentElement.classList.contains("dark")?"dark":"light"}var $s=(e=>(e[e.Enter=0]="Enter",e[e.Exit=1]="Exit",e))($s||{});const vp={containerId:{type:[String,Number],required:!1,default:""},clearOnUrlChange:{type:Boolean,required:!1,default:!0},disabledEnterTransition:{type:Boolean,required:!1,default:!1},dangerouslyHTMLString:{type:Boolean,required:!1,default:!1},multiple:{type:Boolean,required:!1,default:!0},limit:{type:Number,required:!1,default:void 0},position:{type:String,required:!1,default:Jo.TOP_LEFT},bodyClassName:{type:String,required:!1,default:""},autoClose:{type:[Number,Boolean],required:!1,default:!1},closeButton:{type:[Boolean,Function,Object],required:!1,default:void 0},transition:{type:[String,Object],required:!1,default:"bounce"},hideProgressBar:{type:Boolean,required:!1,default:!1},pauseOnHover:{type:Boolean,required:!1,default:!0},pauseOnFocusLoss:{type:Boolean,required:!1,default:!0},closeOnClick:{type:Boolean,required:!1,default:!0},progress:{type:Number,required:!1,default:void 0},progressClassName:{type:String,required:!1,default:""},toastStyle:{type:Object,required:!1,default(){return{}}},progressStyle:{type:Object,required:!1,default(){return{}}},role:{type:String,required:!1,default:"alert"},theme:{type:String,required:!1,default:zr.AUTO},content:{type:[String,Object,Function],required:!1,default:""},toastId:{type:[String,Number],required:!1,default:""},data:{type:[Object,String],required:!1,default(){return{}}},type:{type:String,required:!1,default:Mt.DEFAULT},icon:{type:[Boolean,String,Number,Object,Function],required:!1,default:void 0},delay:{type:Number,required:!1,default:void 0},onOpen:{type:Function,required:!1,default:void 0},onClose:{type:Function,required:!1,default:void 0},onClick:{type:Function,required:!1,default:void 0},isLoading:{type:Boolean,required:!1,default:void 0},rtl:{type:Boolean,required:!1,default:!1},toastClassName:{type:String,required:!1,default:""},updateId:{type:[String,Number],required:!1,default:""}},Y6={autoClose:{type:[Number,Boolean],required:!0},isRunning:{type:Boolean,required:!1,default:void 0},type:{type:String,required:!1,default:Mt.DEFAULT},theme:{type:String,required:!1,default:zr.AUTO},hide:{type:Boolean,required:!1,default:void 0},className:{type:[String,Function],required:!1,default:""},controlledProgress:{type:Boolean,required:!1,default:void 0},rtl:{type:Boolean,required:!1,default:void 0},isIn:{type:Boolean,required:!1,default:void 0},progress:{type:Number,required:!1,default:void 0},closeToast:{type:Function,required:!1,default:void 0}},J6=_e({name:"ProgressBar",props:Y6,setup(e,{attrs:t}){const n=F(),r=O(()=>e.hide?"true":"false"),o=O(()=>({...t.style||{},animationDuration:`${e.autoClose===!0?5e3:e.autoClose}ms`,animationPlayState:e.isRunning?"running":"paused",opacity:e.hide||e.autoClose===!1?0:1,transform:e.controlledProgress?`scaleX(${e.progress})`:"none"})),s=O(()=>[`${ze.CSS_NAMESPACE}__progress-bar`,e.controlledProgress?`${ze.CSS_NAMESPACE}__progress-bar--controlled`:`${ze.CSS_NAMESPACE}__progress-bar--animated`,`${ze.CSS_NAMESPACE}__progress-bar-theme--${e.theme}`,`${ze.CSS_NAMESPACE}__progress-bar--${e.type}`,e.rtl?`${ze.CSS_NAMESPACE}__progress-bar--rtl`:null].filter(Boolean).join(" ")),i=O(()=>`${s.value} ${(t==null?void 0:t.class)||""}`),l=()=>{n.value&&(n.value.onanimationend=null,n.value.ontransitionend=null)},a=()=>{e.isIn&&e.closeToast&&e.autoClose!==!1&&(e.closeToast(),l())},u=O(()=>e.controlledProgress?null:a),c=O(()=>e.controlledProgress?a:null);return it(()=>{n.value&&(l(),n.value.onanimationend=u.value,n.value.ontransitionend=c.value)}),()=>M("div",{ref:n,role:"progressbar","aria-hidden":r.value,"aria-label":"notification timer",class:i.value,style:o.value},null)}}),X6=_e({name:"CloseButton",inheritAttrs:!1,props:{theme:{type:String,required:!1,default:zr.AUTO},type:{type:String,required:!1,default:zr.LIGHT},ariaLabel:{type:String,required:!1,default:"close"},closeToast:{type:Function,required:!1,default:void 0}},setup(e){return()=>M("button",{class:`${ze.CSS_NAMESPACE}__close-button ${ze.CSS_NAMESPACE}__close-button--${e.theme}`,type:"button",onClick:t=>{t.stopPropagation(),e.closeToast&&e.closeToast(t)},"aria-label":e.ariaLabel},[M("svg",{"aria-hidden":"true",viewBox:"0 0 14 16"},[M("path",{"fill-rule":"evenodd",d:"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z"},null)])])}}),Si=({theme:e,type:t,path:n,...r})=>M("svg",dt({viewBox:"0 0 24 24",width:"100%",height:"100%",fill:e==="colored"?"currentColor":`var(--toastify-icon-color-${t})`},r),[M("path",{d:n},null)]);function Q6(e){return M(Si,dt(e,{path:"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z"}),null)}function ew(e){return M(Si,dt(e,{path:"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z"}),null)}function tw(e){return M(Si,dt(e,{path:"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z"}),null)}function nw(e){return M(Si,dt(e,{path:"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z"}),null)}function rw(){return M("div",{class:`${ze.CSS_NAMESPACE}__spinner`},null)}const Wl={info:ew,warning:Q6,success:tw,error:nw,spinner:rw},ow=e=>e in Wl;function sw({theme:e,type:t,isLoading:n,icon:r}){let o;const s={theme:e,type:t};return n?o=Wl.spinner():r===!1?o=void 0:Ss(r)?o=xe(r):Hr(r)?o=r(s):Dr(r)?o=vn(r,s):ql(r)||q6(r)?o=r:ow(t)&&(o=Wl[t](s)),o}const iw=()=>{};function lw(e,t,n=ze.COLLAPSE_DURATION){const{scrollHeight:r,style:o}=e,s=n;requestAnimationFrame(()=>{o.minHeight="initial",o.height=r+"px",o.transition=`all ${s}ms`,requestAnimationFrame(()=>{o.height="0",o.padding="0",o.margin="0",setTimeout(t,s)})})}function aw(e){const t=F(!1),n=F(!1),r=F(!1),o=F($s.Enter),s=_t({...e,appendPosition:e.appendPosition||!1,collapse:typeof e.collapse>"u"?!0:e.collapse,collapseDuration:e.collapseDuration||ze.COLLAPSE_DURATION}),i=s.done||iw,l=O(()=>s.appendPosition?`${s.enter}--${s.position}`:s.enter),a=O(()=>s.appendPosition?`${s.exit}--${s.position}`:s.exit),u=O(()=>e.pauseOnHover?{onMouseenter:g,onMouseleave:m}:{});function c(){const y=l.value.split(" ");d().addEventListener(jl.ENTRANCE_ANIMATION_END,m,{once:!0});const S=$=>{const I=d();$.target===I&&(I.dispatchEvent(new Event(jl.ENTRANCE_ANIMATION_END)),I.removeEventListener("animationend",S),I.removeEventListener("animationcancel",S),o.value===$s.Enter&&$.type!=="animationcancel"&&I.classList.remove(...y))},A=()=>{const $=d();$.classList.add(...y),$.addEventListener("animationend",S),$.addEventListener("animationcancel",S)};e.pauseOnFocusLoss&&p(),A()}function f(){if(!d())return;const y=()=>{const A=d();A.removeEventListener("animationend",y),s.collapse?lw(A,i,s.collapseDuration):i()},S=()=>{const A=d();o.value=$s.Exit,A&&(A.className+=` ${a.value}`,A.addEventListener("animationend",y))};n.value||(r.value?y():setTimeout(S))}function d(){return e.toastRef.value}function p(){document.hasFocus()||g(),window.addEventListener("focus",m),window.addEventListener("blur",g)}function v(){window.removeEventListener("focus",m),window.removeEventListener("blur",g)}function m(){(!e.loading.value||e.isLoading===void 0)&&(t.value=!0)}function g(){t.value=!1}function b(y){y&&(y.stopPropagation(),y.preventDefault()),n.value=!1}return it(f),it(()=>{const y=Ur();n.value=y.findIndex(S=>S.toastId===s.toastId)>-1}),it(()=>{e.isLoading!==void 0&&(e.loading.value?g():m())}),We(c),pt(()=>{e.pauseOnFocusLoss&&v()}),{isIn:n,isRunning:t,hideToast:b,eventHandlers:u}}const uw=_e({name:"ToastItem",inheritAttrs:!1,props:vp,setup(e){const t=F(),n=O(()=>!!e.isLoading),r=O(()=>e.progress!==void 0&&e.progress!==null),o=O(()=>sw(e)),s=O(()=>[`${ze.CSS_NAMESPACE}__toast`,`${ze.CSS_NAMESPACE}__toast-theme--${e.theme}`,`${ze.CSS_NAMESPACE}__toast--${e.type}`,e.rtl?`${ze.CSS_NAMESPACE}__toast--rtl`:void 0,e.toastClassName||""].filter(Boolean).join(" ")),{isRunning:i,isIn:l,hideToast:a,eventHandlers:u}=aw({toastRef:t,loading:n,done:()=>{Dt.remove(e.toastId)},...dp(e.transition,e.disabledEnterTransition),...e});return()=>M("div",dt({id:e.toastId,class:s.value,style:e.toastStyle||{},ref:t,"data-testid":`toast-item-${e.toastId}`,onClick:c=>{e.closeOnClick&&a(),e.onClick&&e.onClick(c)}},u.value),[M("div",{role:e.role,"data-testid":"toast-body",class:`${ze.CSS_NAMESPACE}__toast-body ${e.bodyClassName||""}`},[o.value!=null&&M("div",{"data-testid":`toast-icon-${e.type}`,class:[`${ze.CSS_NAMESPACE}__toast-icon`,e.isLoading?"":`${ze.CSS_NAMESPACE}--animate-icon ${ze.CSS_NAMESPACE}__zoom-enter`].join(" ")},[Ss(o.value)?Oe(xe(o.value),{theme:e.theme,type:e.type}):Hr(o.value)?o.value({theme:e.theme,type:e.type}):o.value]),M("div",{"data-testid":"toast-content"},[Ss(e.content)?Oe(xe(e.content),{toastProps:xe(e),closeToast:a,data:e.data}):Hr(e.content)?e.content({toastProps:xe(e),closeToast:a,data:e.data}):e.dangerouslyHTMLString?Oe("div",{innerHTML:e.content}):e.content])]),(e.closeButton===void 0||e.closeButton===!0)&&M(X6,{theme:e.theme,closeToast:c=>{c.stopPropagation(),c.preventDefault(),a()}},null),Ss(e.closeButton)?Oe(xe(e.closeButton),{closeToast:a,type:e.type,theme:e.theme}):Hr(e.closeButton)?e.closeButton({closeToast:a,type:e.type,theme:e.theme}):null,M(J6,{className:e.progressClassName,style:e.progressStyle,rtl:e.rtl,theme:e.theme,isIn:l.value,type:e.type,hide:e.hideProgressBar,isRunning:i.value,autoClose:e.autoClose,controlledProgress:r.value,progress:e.progress,closeToast:e.isLoading?void 0:a},null)])}});let ho=0;function mp(){typeof window>"u"||(ho&&window.cancelAnimationFrame(ho),ho=window.requestAnimationFrame(mp),Ls.lastUrl!==window.location.href&&(Ls.lastUrl=window.location.href,Dt.clear()))}const cw=_e({name:"ToastifyContainer",inheritAttrs:!1,props:vp,setup(e){const t=O(()=>e.containerId),n=O(()=>bt[t.value]||[]),r=O(()=>n.value.filter(o=>o.position===e.position));return We(()=>{typeof window<"u"&&e.clearOnUrlChange&&window.requestAnimationFrame(mp)}),pt(()=>{typeof window<"u"&&ho&&(window.cancelAnimationFrame(ho),Ls.lastUrl="")}),()=>M(he,null,[r.value.map(o=>{const{toastId:s=""}=o;return M(uw,dt({key:s},o),null)})])}});let nl=!1;function gp(){const e=[];return Ur().forEach(t=>{const n=document.getElementById(t.containerId);n&&!n.classList.contains(wi)&&e.push(t)}),e}function dw(e){const t=gp().length,n=e??0;return n>0&&t+gn.items.length>=n}function fw(e){dw(e.limit)&&!e.updateId&&gn.items.push({toastId:e.toastId,containerId:e.containerId,toastContent:e.content,toastProps:e})}function Xn(e,t,n={}){if(nl)return;n=_i(G6(),{type:t},xe(n)),(!n.toastId||typeof n.toastId!="string"&&typeof n.toastId!="number")&&(n.toastId=hp()),n={...n,content:e,containerId:n.containerId||String(n.position)};const r=Number(n==null?void 0:n.progress);return r<0&&(n.progress=0),r>1&&(n.progress=1),n.theme==="auto"&&(n.theme=K6()),fw(n),Ls.lastUrl=window.location.href,n.multiple?gn.items.length?n.updateId&&_s(e,n):_s(e,n):(nl=!0,Pe.clearAll(void 0,!1),setTimeout(()=>{_s(e,n)},0),setTimeout(()=>{nl=!1},390)),n.toastId}const Pe=(e,t)=>Xn(e,Mt.DEFAULT,t);Pe.info=(e,t)=>Xn(e,Mt.DEFAULT,{...t,type:Mt.INFO});Pe.error=(e,t)=>Xn(e,Mt.DEFAULT,{...t,type:Mt.ERROR});Pe.warning=(e,t)=>Xn(e,Mt.DEFAULT,{...t,type:Mt.WARNING});Pe.warn=Pe.warning;Pe.success=(e,t)=>Xn(e,Mt.DEFAULT,{...t,type:Mt.SUCCESS});Pe.loading=(e,t)=>Xn(e,Mt.DEFAULT,_i(t,{isLoading:!0,autoClose:!1,closeOnClick:!1,closeButton:!1,draggable:!1}));Pe.dark=(e,t)=>Xn(e,Mt.DEFAULT,_i(t,{theme:zr.DARK}));Pe.remove=e=>{e?Dt.dismiss(e):Dt.clear()};Pe.clearAll=(e,t)=>{et(()=>{Dt.clear(e,t)})};Pe.isActive=e=>{let t=!1;return t=gp().findIndex(n=>n.toastId===e)>-1,t};Pe.update=(e,t={})=>{setTimeout(()=>{const n=Z6(e);if(n){const r=xe(n),{content:o}=r,s={...r,...t,toastId:t.toastId||e,updateId:hp()},i=s.render||o;delete s.render,Xn(i,s.type,s)}},0)};Pe.done=e=>{Pe.update(e,{isLoading:!1,progress:1})};Pe.promise=pw;function pw(e,{pending:t,error:n,success:r},o){var s,i,l;let a;const u={...o||{},autoClose:!1};t&&(a=ql(t)?Pe.loading(t,u):Pe.loading(t.render,{...u,...t}));const c={autoClose:(s=o==null?void 0:o.autoClose)!=null?s:!0,closeOnClick:(i=o==null?void 0:o.closeOnClick)!=null?i:!0,closeButton:(l=o==null?void 0:o.autoClose)!=null?l:null,isLoading:void 0,draggable:null,delay:100},f=(p,v,m)=>{if(v==null){Pe.remove(a);return}const g={type:p,...c,...o,data:m},b=ql(v)?{render:v}:v;return a?Pe.update(a,{...g,...b,isLoading:!1}):Pe(b.render,{...g,...b,isLoading:!1}),m},d=Hr(e)?e():e;return d.then(p=>{f("success",r,p)}).catch(p=>{f("error",n,p)}),d}Pe.POSITION=Jo;Pe.THEME=zr;Pe.TYPE=Mt;Pe.TRANSITIONS=E6;const hw={install(e,t={}){yp(t)}};typeof window<"u"&&(window.Vue3Toastify=hw);function yp(e={}){const t=_i(cp,e);W6(t)}/*!
 * pinia v2.2.2
 * (c) 2024 Eduardo San Martin Morote
 * @license MIT
 */let bp;const $i=e=>bp=e,wp=Symbol();function Gl(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var vo;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(vo||(vo={}));function vw(){const e=Id(!0),t=e.run(()=>F({}));let n=[],r=[];const o=Gs({install(s){$i(o),o._a=s,s.provide(wp,o),s.config.globalProperties.$pinia=o,r.forEach(i=>n.push(i)),r=[]},use(s){return this._a?n.push(s):r.push(s),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return o}const _p=()=>{};function Fc(e,t,n,r=_p){e.push(t);const o=()=>{const s=e.indexOf(t);s>-1&&(e.splice(s,1),r())};return!n&&Nd()&&x1(o),o}function Er(e,...t){e.slice().forEach(n=>{n(...t)})}const mw=e=>e(),Vc=Symbol(),rl=Symbol();function Kl(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,r)=>e.set(r,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],o=e[n];Gl(o)&&Gl(r)&&e.hasOwnProperty(n)&&!Ye(r)&&!jn(r)?e[n]=Kl(o,r):e[n]=r}return e}const gw=Symbol();function yw(e){return!Gl(e)||!e.hasOwnProperty(gw)}const{assign:Ln}=Object;function bw(e){return!!(Ye(e)&&e.effect)}function ww(e,t,n,r){const{state:o,actions:s,getters:i}=t,l=n.state.value[e];let a;function u(){l||(n.state.value[e]=o?o():{});const c=Y1(n.state.value[e]);return Ln(c,s,Object.keys(i||{}).reduce((f,d)=>(f[d]=Gs(O(()=>{$i(n);const p=n._s.get(e);return i[d].call(p,p)})),f),{}))}return a=Sp(e,u,t,n,r,!0),a}function Sp(e,t,n={},r,o,s){let i;const l=Ln({actions:{}},n),a={deep:!0};let u,c,f=[],d=[],p;const v=r.state.value[e];!s&&!v&&(r.state.value[e]={}),F({});let m;function g(U){let N;u=c=!1,typeof U=="function"?(U(r.state.value[e]),N={type:vo.patchFunction,storeId:e,events:p}):(Kl(r.state.value[e],U),N={type:vo.patchObject,payload:U,storeId:e,events:p});const D=m=Symbol();et().then(()=>{m===D&&(u=!0)}),c=!0,Er(f,N,r.state.value[e])}const b=s?function(){const{state:N}=n,D=N?N():{};this.$patch(se=>{Ln(se,D)})}:_p;function y(){i.stop(),f=[],d=[],r._s.delete(e)}const S=(U,N="")=>{if(Vc in U)return U[rl]=N,U;const D=function(){$i(r);const se=Array.from(arguments),G=[],le=[];function fe(K){G.push(K)}function Se(K){le.push(K)}Er(d,{args:se,name:D[rl],store:$,after:fe,onError:Se});let re;try{re=U.apply(this&&this.$id===e?this:$,se)}catch(K){throw Er(le,K),K}return re instanceof Promise?re.then(K=>(Er(G,K),K)).catch(K=>(Er(le,K),Promise.reject(K))):(Er(G,re),re)};return D[Vc]=!0,D[rl]=N,D},A={_p:r,$id:e,$onAction:Fc.bind(null,d),$patch:g,$reset:b,$subscribe(U,N={}){const D=Fc(f,U,N.detached,()=>se()),se=i.run(()=>ut(()=>r.state.value[e],G=>{(N.flush==="sync"?c:u)&&U({storeId:e,type:vo.direct,events:p},G)},Ln({},a,N)));return D},$dispose:y},$=_t(A);r._s.set(e,$);const H=(r._a&&r._a.runWithContext||mw)(()=>r._e.run(()=>(i=Id()).run(()=>t({action:S}))));for(const U in H){const N=H[U];if(Ye(N)&&!bw(N)||jn(N))s||(v&&yw(N)&&(Ye(N)?N.value=v[U]:Kl(N,v[U])),r.state.value[e][U]=N);else if(typeof N=="function"){const D=S(N,U);H[U]=D,l.actions[U]=N}}return Ln($,H),Ln(xe($),H),Object.defineProperty($,"$state",{get:()=>r.state.value[e],set:U=>{g(N=>{Ln(N,U)})}}),r._p.forEach(U=>{Ln($,i.run(()=>U({store:$,app:r._a,pinia:r,options:l})))}),v&&s&&n.hydrate&&n.hydrate($.$state,v),u=!0,c=!0,$}function _w(e,t,n){let r,o;const s=typeof t=="function";r=e,o=s?n:t;function i(l,a){const u=Nh();return l=l||(u?ae(wp,null):null),l&&$i(l),l=bp,l._s.has(r)||(s?Sp(r,t,o,l):ww(r,o,l)),l._s.get(r)}return i.$id=r,i}const Ya=_w("userStore",{state:()=>({isAuthenticated:!1,UNAME:"",UID:null,UTYPE:null,AREA:null,DISTRICT:null,AGENZIA:null})}),Sw={style:{padding:"35px 0"}},$w={class:"container"},Aw={class:"flex items-center justify-between md:justify-normal",style:{gap:"40px"}},xw=["src"],Ew={class:"flex justify-between grow items-center"},Cw={class:"menu gap-x-5 hidden lg:flex"},Tw={key:0,class:"absolute right-0 top-0"},Ow={key:0,class:"absolute right-0 top-1/2 -translate-y-1/2",style:{top:"50%",transform:"translateY(-50%)"}},Pw={key:0,class:"absolute right-0 top-1/2 -translate-y-1/2",style:{top:"50%",transform:"translateY(-50%)"}},Rw={key:0,class:"absolute right-0 top-1/2 -translate-y-1/2"},Mw={class:"menu-2 gap-x-3 hidden lg:flex items-center"},kw={class:"block lg:hidden"},Lw={class:"hidden 2xl:block",style:{position:"absolute",right:"0",top:"25px"}},Iw={style:{"background-color":"#ffffff",padding:"18px"}},Nw=["src"],Hw={__name:"Header",emits:["clickOnFaq"],setup(e,{emit:t}){const n=Ya(),r=t,o=F([]),s=F(!1);function i(){r("clickOnFaq")}const l=F(null);function a(){l.value.openModal()}u();async function u(){const f=await Yo.getMaterialsStatus();if(!(f!=null&&f.success)){Pe.error("Errore imprevisto");return}o.value=f.data,s.value=o.value.some(d=>d.hasNewContent===1)}function c(f){return o.value.some(d=>d.category===f&&d.hasNewContent===1)}return(f,d)=>{const p=rn("RouterLink");return x(),T(he,null,[M(Z4,{ref_key:"menuMobileModal",ref:l,onScrollRequest:i},null,512),h("header",Sw,[h("div",$w,[h("div",Aw,[M(p,{to:"/"},{default:X(()=>[h("img",{class:"mb-1",src:"https://"+f.window.staticHost+"/themes/uniqum/uniqum-logo.svg",alt:"Uniqum"},null,8,xw),d[0]||(d[0]=h("div",{style:{color:"#ffffff","font-size":"10px"}},"COLLABORARE, CRESCERE, ECCELLERE.",-1))]),_:1}),h("div",Ew,[h("nav",Cw,[R(n).UTYPE!=="AREAMGR"&&R(n).UTYPE!=="INTERMEDIARIO"?(x(),tt(p,{key:0,to:"/corsi-attivi"},{default:X(()=>d[1]||(d[1]=[oe("Corsi attivi")])),_:1})):ce("",!0),R(n).UTYPE!=="AREAMGR"&&R(n).UTYPE!=="INTERMEDIARIO"?(x(),tt(p,{key:1,to:"/prenotazioni"},{default:X(()=>d[2]||(d[2]=[oe("Le tue prenotazioni")])),_:1})):ce("",!0),M(R(S0),{as:"div",class:"relative"},{default:X(()=>[s.value?(x(),T("div",Tw,d[3]||(d[3]=[h("div",{class:"h-4 w-4 rounded-full bg-[#E94E1D]"},null,-1)]))):ce("",!0),h("div",null,[M(R($0),{class:"flex items-center gap-x-3"},{default:X(()=>[d[4]||(d[4]=oe(" Approfondimenti ")),M(R(Za),{class:"h-5 w-5 text-[#00624A]","aria-hidden":"true"})]),_:1})]),M(ri,{"enter-active-class":"transition duration-100 ease-out","enter-from-class":"transform scale-95 opacity-0","enter-to-class":"transform scale-100 opacity-100","leave-active-class":"transition duration-75 ease-in","leave-from-class":"transform scale-100 opacity-100","leave-to-class":"transform scale-95 opacity-0"},{default:X(()=>[M(R(A0),{class:"absolute left-0 mt-5 p-2 w-56 origin-top-right divide-y-2 divide-gray-500 rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none z-10"},{default:X(()=>[M(R(Ir),null,{default:X(({active:v})=>[M(p,{to:{name:"commercial"},class:"relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[c("commercial")?(x(),T("div",Ow,d[5]||(d[5]=[h("div",{class:"h-4 w-4 rounded-full bg-[#E94E1D]"},null,-1)]))):ce("",!0),M(R(za),{size:"28",class:"mr-5"}),d[6]||(d[6]=h("span",{class:"text-left leading-tight"},[oe("Ambito"),h("br"),oe("commerciale")],-1))]),_:1})]),_:1}),M(R(Ir),null,{default:X(({active:v})=>[M(p,{to:{name:"products"},class:"relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[c("products")?(x(),T("div",Pw,d[7]||(d[7]=[h("div",{class:"h-4 w-4 rounded-full bg-[#E94E1D]"},null,-1)]))):ce("",!0),M(R(ja),{size:"28",class:"mr-5"}),d[8]||(d[8]=h("span",{class:"text-left leading-tight"},[oe("Conoscere"),h("br"),oe("i prodotti")],-1))]),_:1})]),_:1}),M(R(Ir),null,{default:X(({active:v})=>[M(p,{to:{name:"operative"},class:"relative flex w-full items-center px-2 py-1 text-sm hover:bg-green-800 hover:text-white text-[#00624A]"},{default:X(()=>[c("operative")?(x(),T("div",Rw,d[9]||(d[9]=[h("div",{class:"h-4 w-4 rounded-full bg-[#E94E1D]"},null,-1)]))):ce("",!0),M(R(Ua),{size:"28",class:"mr-5"}),d[10]||(d[10]=h("span",{class:"text-left leading-tight"},[oe("Novità operative"),h("br"),oe("e di processo")],-1))]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})]),h("nav",Mw,[M(p,{"active-class":"active",to:{name:"description"},title:"Cos'è Uniqum"},{default:X(()=>d[11]||(d[11]=[oe("Cos'è Uniqum")])),_:1}),d[13]||(d[13]=h("span",{style:{"background-color":"#14513A",width:"1px",height:"30px"}},null,-1)),R(n).UTYPE!=="AREAMGR"&&R(n).UTYPE!=="INTERMEDIARIO"?(x(),tt(p,{key:0,"active-class":"active",to:"/team",title:"Il tuo team",class:"relative"},{default:X(()=>d[12]||(d[12]=[oe(" Il tuo team ")])),_:1})):ce("",!0)])]),h("div",kw,[M(R(Km),{class:"text-white size-10",onClick:a})])])]),h("div",Lw,[h("div",Iw,[h("img",{src:"https://"+f.window.staticHost+"/themes/uniqum/groupama-logo.svg",alt:"Groupama"},null,8,Nw)])])])],64)}}};async function Fw(){var t;const e=(t=await Ce.post("/stats",{l1:"uniqum",l2:"",l3:""}))==null?void 0:t.data;return e!=null&&e.success?e:[]}const Vw={registerSession:Fw},no=_t([]),$p=function(){return{DialogsStore:no,addDialog:function(r){no.push(Gs(r))},removeDialog:function(r){const o=no.findIndex(s=>s.id==r);no.splice(o,1)},removeAll:function(){no.splice(0)}}};function ol(){const e=[],t=o=>{const s=e.indexOf(o);s!==-1&&e.splice(s,1)};return{on:o=>(e.push(o),{off:()=>t(o)}),off:t,trigger:o=>{e.forEach(s=>s(o))}}}const Dw=typeof window<"u",Bw=()=>{},Dc=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Bc="__vueuse_ssr_handlers__";Dc[Bc]=Dc[Bc]||{};function jw(e=F(!1)){const t=ol(),n=ol(),r=ol();let o=Bw;const s=a=>(r.trigger(a),e.value=!0,new Promise(u=>{o=u})),i=a=>{e.value=!1,t.trigger(a),o({data:a,isCanceled:!1})},l=a=>{e.value=!1,n.trigger(a),o({data:a,isCanceled:!0})};return{isRevealed:O(()=>e.value),reveal:s,confirm:i,cancel:l,onReveal:r.on,onConfirm:t.on,onCancel:n.on}}var jc,zc;Dw&&(window!=null&&window.navigator)&&((jc=window==null?void 0:window.navigator)!=null&&jc.platform)&&/iP(ad|hone|od)/.test((zc=window==null?void 0:window.navigator)==null?void 0:zc.platform);var zw=Object.defineProperty,Uc=Object.getOwnPropertySymbols,Uw=Object.prototype.hasOwnProperty,Zw=Object.prototype.propertyIsEnumerable,Zc=(e,t,n)=>t in e?zw(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,qw=(e,t)=>{for(var n in t||(t={}))Uw.call(t,n)&&Zc(e,n,t[n]);if(Uc)for(var n of Uc(t))Zw.call(t,n)&&Zc(e,n,t[n]);return e};const Ww={top:0,left:0,bottom:0,right:0,height:0,width:0};qw({text:""},Ww);let Gw=0;function Kw(){return++Gw}function Ap(e,t={},n={chore:!1,keepInitial:!1}){const r=A=>{if(!A){o.value={};return}for(const $ in A)o.value[$]=A[$]},o=F({});r(t);const s=F(!1),i=()=>{s.value=!1,a(y)},{addDialog:l,removeDialog:a,removeAll:u,DialogsStore:c}=$p(),{reveal:f,isRevealed:d,onConfirm:p,onReveal:v,onCancel:m,confirm:g,cancel:b}=jw(),y=Kw();return v(A=>{s.value=!0,A&&r(A),l({id:y,dialog:e,isRevealed:d,confirm:g,cancel:b,props:o.value,close:i,revealed:s})}),ut(d,A=>{A||(n.chore&&r(n.keepInitial?t:null),a(y))}),{close:i,closeAll:()=>{c.forEach(A=>{A.revealed.value=!1}),u()},reveal:f,isRevealed:O(()=>d.value&&s.value),onConfirm:p,onCancel:m}}var Ja=_e({name:"DialogsWrapper",setup(){const{DialogsStore:e}=$p();return()=>e.map(t=>Oe(t.dialog,{is:t.dialog,onConfirm:t.confirm,onCancel:t.cancel,key:t.id,...t.props}))}});function Yw(e){e.component("DialogsWrapper",Ja)}const Jw=Object.freeze(Object.defineProperty({__proto__:null,DialogsWrapper:Ja,createConfirmDialog:Ap,install:Yw},Symbol.toStringTag,{value:"Module"})),Xw={__name:"App",setup(e){const t=ka(),n=r0();function r(){var s;(s=t.meta)!=null&&s.isHome||n.push("/"),document.getElementById("faq").scrollIntoView({block:"start",behavior:"smooth"})}return Vw.registerSession(),(o,s)=>(x(),T(he,null,[M(R(Ja)),M(Hw,{onClickOnFaq:r}),M(R(n0))],64))}};function Xa(e,t=null){return t}const Qw=Xa("BASE_URL","/api"),e_=Xa("SESSION_COOKIE_NAME","SESSION"),t_=Xa("XSRF_COOKIE_NAME","XSRF-TOKEN"),mo={app:{baseUrl:Qw,sessionCookieName:e_,xsrfCookieName:t_}};/**
  * vee-validate v4.13.2
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */function gt(e){return typeof e=="function"}function xp(e){return e==null}const _r=e=>e!==null&&!!e&&typeof e=="object"&&!Array.isArray(e);function Qa(e){return Number(e)>=0}function n_(e){const t=parseFloat(e);return isNaN(t)?e:t}function r_(e){return typeof e=="object"&&e!==null}function o_(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}function qc(e){if(!r_(e)||o_(e)!=="[object Object]")return!1;if(Object.getPrototypeOf(e)===null)return!0;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Mo(e,t){return Object.keys(t).forEach(n=>{if(qc(t[n])&&qc(e[n])){e[n]||(e[n]={}),Mo(e[n],t[n]);return}e[n]=t[n]}),e}function so(e){const t=e.split(".");if(!t.length)return"";let n=String(t[0]);for(let r=1;r<t.length;r++){if(Qa(t[r])){n+=`[${t[r]}]`;continue}n+=`.${t[r]}`}return n}const Ep={};function sl(e,t){i_(e,t),Ep[e]=t}function s_(e){return Ep[e]}function i_(e,t){if(!gt(t))throw new Error(`Extension Error: The validator '${e}' must be a function.`)}function Wc(e,t,n){typeof n.value=="object"&&(n.value=je(n.value)),!n.enumerable||n.get||n.set||!n.configurable||!n.writable||t==="__proto__"?Object.defineProperty(e,t,n):e[t]=n.value}function je(e){if(typeof e!="object")return e;var t=0,n,r,o,s=Object.prototype.toString.call(e);if(s==="[object Object]"?o=Object.create(e.__proto__||null):s==="[object Array]"?o=Array(e.length):s==="[object Set]"?(o=new Set,e.forEach(function(i){o.add(je(i))})):s==="[object Map]"?(o=new Map,e.forEach(function(i,l){o.set(je(l),je(i))})):s==="[object Date]"?o=new Date(+e):s==="[object RegExp]"?o=new RegExp(e.source,e.flags):s==="[object DataView]"?o=new e.constructor(je(e.buffer)):s==="[object ArrayBuffer]"?o=e.slice(0):s.slice(-6)==="Array]"&&(o=new e.constructor(e)),o){for(r=Object.getOwnPropertySymbols(e);t<r.length;t++)Wc(o,r[t],Object.getOwnPropertyDescriptor(e,r[t]));for(t=0,r=Object.getOwnPropertyNames(e);t<r.length;t++)Object.hasOwnProperty.call(o,n=r[t])&&o[n]===e[n]||Wc(o,n,Object.getOwnPropertyDescriptor(e,n))}return o||e}const Ai=Symbol("vee-validate-form"),l_=Symbol("vee-validate-field-instance"),Is=Symbol("Default empty value"),a_=typeof window<"u";function Yl(e){return gt(e)&&!!e.__locatorRef}function Xt(e){return!!e&&gt(e.parse)&&e.__type==="VVTypedSchema"}function Ns(e){return!!e&&gt(e.validate)}function Xo(e){return e==="checkbox"||e==="radio"}function u_(e){return _r(e)||Array.isArray(e)}function c_(e){return Array.isArray(e)?e.length===0:_r(e)&&Object.keys(e).length===0}function xi(e){return/^\[.+\]$/i.test(e)}function d_(e){return Cp(e)&&e.multiple}function Cp(e){return e.tagName==="SELECT"}function f_(e,t){const n=![!1,null,void 0,0].includes(t.multiple)&&!Number.isNaN(t.multiple);return e==="select"&&"multiple"in t&&n}function p_(e,t){return!f_(e,t)&&t.type!=="file"&&!Xo(t.type)}function Tp(e){return eu(e)&&e.target&&"submit"in e.target}function eu(e){return e?!!(typeof Event<"u"&&gt(Event)&&e instanceof Event||e&&e.srcElement):!1}function Gc(e,t){return t in e&&e[t]!==Is}function Rt(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var n,r,o;if(Array.isArray(e)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(!Rt(e[r],t[r]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(r of e.entries())if(!t.has(r[0]))return!1;for(r of e.entries())if(!Rt(r[1],t.get(r[0])))return!1;return!0}if(Kc(e)&&Kc(t))return!(e.size!==t.size||e.name!==t.name||e.lastModified!==t.lastModified||e.type!==t.type);if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(r of e.entries())if(!t.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(n=e.length,n!=t.length)return!1;for(r=n;r--!==0;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();for(o=Object.keys(e),n=o.length,r=n;r--!==0;){var s=o[r];if(!Rt(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Kc(e){return a_?e instanceof File:!1}function tu(e){return xi(e)?e.replace(/\[|\]/gi,""):e}function It(e,t,n){return e?xi(t)?e[tu(t)]:(t||"").split(/\.|\[(\d+)\]/).filter(Boolean).reduce((o,s)=>u_(o)&&s in o?o[s]:n,e):n}function _n(e,t,n){if(xi(t)){e[tu(t)]=n;return}const r=t.split(/\.|\[(\d+)\]/).filter(Boolean);let o=e;for(let s=0;s<r.length;s++){if(s===r.length-1){o[r[s]]=n;return}(!(r[s]in o)||xp(o[r[s]]))&&(o[r[s]]=Qa(r[s+1])?[]:{}),o=o[r[s]]}}function il(e,t){if(Array.isArray(e)&&Qa(t)){e.splice(Number(t),1);return}_r(e)&&delete e[t]}function Yc(e,t){if(xi(t)){delete e[tu(t)];return}const n=t.split(/\.|\[(\d+)\]/).filter(Boolean);let r=e;for(let s=0;s<n.length;s++){if(s===n.length-1){il(r,n[s]);break}if(!(n[s]in r)||xp(r[n[s]]))break;r=r[n[s]]}const o=n.map((s,i)=>It(e,n.slice(0,i).join(".")));for(let s=o.length-1;s>=0;s--)if(c_(o[s])){if(s===0){il(e,n[0]);continue}il(o[s-1],n[s-1])}}function Vt(e){return Object.keys(e)}function Op(e,t=void 0){const n=$r();return(n==null?void 0:n.provides[e])||ae(e,t)}function Jc(e,t,n){if(Array.isArray(e)){const r=[...e],o=r.findIndex(s=>Rt(s,t));return o>=0?r.splice(o,1):r.push(t),r}return Rt(e,t)?n:t}function Xc(e,t=0){let n=null,r=[];return function(...o){return n&&clearTimeout(n),n=setTimeout(()=>{const s=e(...o);r.forEach(i=>i(s)),r=[]},t),new Promise(s=>r.push(s))}}function h_(e,t){return _r(t)&&t.number?n_(e):e}function Jl(e,t){let n;return async function(...o){const s=e(...o);n=s;const i=await s;return s!==n?i:(n=void 0,t(i,o))}}function Xl(e){return Array.isArray(e)?e:e?[e]:[]}function cs(e,t){const n={};for(const r in e)t.includes(r)||(n[r]=e[r]);return n}function v_(e){let t=null,n=[];return function(...r){const o=et(()=>{if(t!==o)return;const s=e(...r);n.forEach(i=>i(s)),n=[],t=null});return t=o,new Promise(s=>n.push(s))}}function nu(e,t,n){return t.slots.default?typeof e=="string"||!e?t.slots.default(n()):{default:()=>{var r,o;return(o=(r=t.slots).default)===null||o===void 0?void 0:o.call(r,n())}}:t.slots.default}function ll(e){if(Pp(e))return e._value}function Pp(e){return"_value"in e}function m_(e){return e.type==="number"||e.type==="range"?Number.isNaN(e.valueAsNumber)?e.value:e.valueAsNumber:e.value}function Hs(e){if(!eu(e))return e;const t=e.target;if(Xo(t.type)&&Pp(t))return ll(t);if(t.type==="file"&&t.files){const n=Array.from(t.files);return t.multiple?n:n[0]}if(d_(t))return Array.from(t.options).filter(n=>n.selected&&!n.disabled).map(ll);if(Cp(t)){const n=Array.from(t.options).find(r=>r.selected);return n?ll(n):t.value}return m_(t)}function Rp(e){const t={};return Object.defineProperty(t,"_$$isNormalized",{value:!0,writable:!1,enumerable:!1,configurable:!1}),e?_r(e)&&e._$$isNormalized?e:_r(e)?Object.keys(e).reduce((n,r)=>{const o=g_(e[r]);return e[r]!==!1&&(n[r]=Qc(o)),n},t):typeof e!="string"?t:e.split("|").reduce((n,r)=>{const o=y_(r);return o.name&&(n[o.name]=Qc(o.params)),n},t):t}function g_(e){return e===!0?[]:Array.isArray(e)||_r(e)?e:[e]}function Qc(e){const t=n=>typeof n=="string"&&n[0]==="@"?b_(n.slice(1)):n;return Array.isArray(e)?e.map(t):e instanceof RegExp?[e]:Object.keys(e).reduce((n,r)=>(n[r]=t(e[r]),n),{})}const y_=e=>{let t=[];const n=e.split(":")[0];return e.includes(":")&&(t=e.split(":").slice(1).join(":").split(",")),{name:n,params:t}};function b_(e){const t=n=>It(n,e)||n[e];return t.__locatorRef=e,t}function w_(e){return Array.isArray(e)?e.filter(Yl):Vt(e).filter(t=>Yl(e[t])).map(t=>e[t])}const __={generateMessage:({field:e})=>`${e} is not valid.`,bails:!0,validateOnBlur:!0,validateOnChange:!0,validateOnInput:!1,validateOnModelUpdate:!0};let Ql=Object.assign({},__);const pr=()=>Ql,S_=e=>{Ql=Object.assign(Object.assign({},Ql),e)},$_=S_;async function Mp(e,t,n={}){const r=n==null?void 0:n.bails,o={name:(n==null?void 0:n.name)||"{field}",rules:t,label:n==null?void 0:n.label,bails:r??!0,formData:(n==null?void 0:n.values)||{}},s=await A_(o,e);return Object.assign(Object.assign({},s),{valid:!s.errors.length})}async function A_(e,t){const n=e.rules;if(Xt(n)||Ns(n))return E_(t,Object.assign(Object.assign({},e),{rules:n}));if(gt(n)||Array.isArray(n)){const l={field:e.label||e.name,name:e.name,label:e.label,form:e.formData,value:t},a=Array.isArray(n)?n:[n],u=a.length,c=[];for(let f=0;f<u;f++){const d=a[f],p=await d(t,l);if(!(typeof p!="string"&&!Array.isArray(p)&&p)){if(Array.isArray(p))c.push(...p);else{const m=typeof p=="string"?p:Lp(l);c.push(m)}if(e.bails)return{errors:c}}}return{errors:c}}const r=Object.assign(Object.assign({},e),{rules:Rp(n)}),o=[],s=Object.keys(r.rules),i=s.length;for(let l=0;l<i;l++){const a=s[l],u=await C_(r,t,{name:a,params:r.rules[a]});if(u.error&&(o.push(u.error),e.bails))return{errors:o}}return{errors:o}}function x_(e){return!!e&&e.name==="ValidationError"}function kp(e){return{__type:"VVTypedSchema",async parse(n,r){var o;try{return{output:await e.validate(n,{abortEarly:!1,context:(r==null?void 0:r.formData)||{}}),errors:[]}}catch(s){if(!x_(s))throw s;if(!(!((o=s.inner)===null||o===void 0)&&o.length)&&s.errors.length)return{errors:[{path:s.path,errors:s.errors}]};const i=s.inner.reduce((l,a)=>{const u=a.path||"";return l[u]||(l[u]={errors:[],path:u}),l[u].errors.push(...a.errors),l},{});return{errors:Object.values(i)}}}}}async function E_(e,t){const r=await(Xt(t.rules)?t.rules:kp(t.rules)).parse(e,{formData:t.formData}),o=[];for(const s of r.errors)s.errors.length&&o.push(...s.errors);return{value:r.value,errors:o}}async function C_(e,t,n){const r=s_(n.name);if(!r)throw new Error(`No such validator '${n.name}' exists.`);const o=T_(n.params,e.formData),s={field:e.label||e.name,name:e.name,label:e.label,value:t,form:e.formData,rule:Object.assign(Object.assign({},n),{params:o})},i=await r(t,o,s);return typeof i=="string"?{error:i}:{error:i?void 0:Lp(s)}}function Lp(e){const t=pr().generateMessage;return t?t(e):"Field is invalid"}function T_(e,t){const n=r=>Yl(r)?r(t):r;return Array.isArray(e)?e.map(n):Object.keys(e).reduce((r,o)=>(r[o]=n(e[o]),r),{})}async function O_(e,t){const r=await(Xt(e)?e:kp(e)).parse(je(t)),o={},s={};for(const i of r.errors){const l=i.errors,a=(i.path||"").replace(/\["(\d+)"\]/g,(u,c)=>`[${c}]`);o[a]={valid:!l.length,errors:l},l.length&&(s[a]=l[0])}return{valid:!r.errors.length,results:o,errors:s,values:r.value,source:"schema"}}async function P_(e,t,n){const o=Vt(e).map(async u=>{var c,f,d;const p=(c=n==null?void 0:n.names)===null||c===void 0?void 0:c[u],v=await Mp(It(t,u),e[u],{name:(p==null?void 0:p.name)||u,label:p==null?void 0:p.label,values:t,bails:(d=(f=n==null?void 0:n.bailsMap)===null||f===void 0?void 0:f[u])!==null&&d!==void 0?d:!0});return Object.assign(Object.assign({},v),{path:u})});let s=!0;const i=await Promise.all(o),l={},a={};for(const u of i)l[u.path]={valid:u.valid,errors:u.errors},u.valid||(s=!1,a[u.path]=u.errors[0]);return{valid:s,results:l,errors:a,source:"schema"}}let ed=0;function R_(e,t){const{value:n,initialValue:r,setInitialValue:o}=M_(e,t.modelValue,t.form);if(!t.form){let a=function(p){var v;"value"in p&&(n.value=p.value),"errors"in p&&c(p.errors),"touched"in p&&(d.touched=(v=p.touched)!==null&&v!==void 0?v:d.touched),"initialValue"in p&&o(p.initialValue)};const{errors:u,setErrors:c}=I_(),f=ed>=Number.MAX_SAFE_INTEGER?0:++ed,d=L_(n,r,u,t.schema);return{id:f,path:e,value:n,initialValue:r,meta:d,flags:{pendingUnmount:{[f]:!1},pendingReset:!1},errors:u,setState:a}}const s=t.form.createPathState(e,{bails:t.bails,label:t.label,type:t.type,validate:t.validate,schema:t.schema}),i=O(()=>s.errors);function l(a){var u,c,f;"value"in a&&(n.value=a.value),"errors"in a&&((u=t.form)===null||u===void 0||u.setFieldError(R(e),a.errors)),"touched"in a&&((c=t.form)===null||c===void 0||c.setFieldTouched(R(e),(f=a.touched)!==null&&f!==void 0?f:!1)),"initialValue"in a&&o(a.initialValue)}return{id:Array.isArray(s.id)?s.id[s.id.length-1]:s.id,path:e,value:n,errors:i,meta:s,initialValue:r,flags:s.__flags,setState:l}}function M_(e,t,n){const r=F(R(t));function o(){return n?It(n.initialValues.value,R(e),R(r)):R(r)}function s(u){if(!n){r.value=u;return}n.setFieldInitialValue(R(e),u,!0)}const i=O(o);if(!n)return{value:F(o()),initialValue:i,setInitialValue:s};const l=k_(t,n,i,e);return n.stageInitialValue(R(e),l,!0),{value:O({get(){return It(n.values,R(e))},set(u){n.setFieldValue(R(e),u,!1)}}),initialValue:i,setInitialValue:s}}function k_(e,t,n,r){return Ye(e)?R(e):e!==void 0?e:It(t.values,R(r),R(n))}function L_(e,t,n,r){const o=O(()=>{var i,l,a;return(a=(l=(i=Ne(r))===null||i===void 0?void 0:i.describe)===null||l===void 0?void 0:l.call(i).required)!==null&&a!==void 0?a:!1}),s=_t({touched:!1,pending:!1,valid:!0,required:o,validated:!!R(n).length,initialValue:O(()=>R(t)),dirty:O(()=>!Rt(R(e),R(t)))});return ut(n,i=>{s.valid=!i.length},{immediate:!0,flush:"sync"}),s}function I_(){const e=F([]);return{errors:e,setErrors:t=>{e.value=Xl(t)}}}function N_(e,t,n){return Xo(n==null?void 0:n.type)?F_(e,t,n):Ip(e,t,n)}function Ip(e,t,n){const{initialValue:r,validateOnMount:o,bails:s,type:i,checkedValue:l,label:a,validateOnValueUpdate:u,uncheckedValue:c,controlled:f,keepValueOnUnmount:d,syncVModel:p,form:v}=H_(n),m=f?Op(Ai):void 0,g=v||m,b=O(()=>so(Ne(e))),y=O(()=>{if(Ne(g==null?void 0:g.schema))return;const k=R(t);return Ns(k)||Xt(k)||gt(k)||Array.isArray(k)?k:Rp(k)}),S=!gt(y.value)&&Xt(Ne(t)),{id:A,value:$,initialValue:I,meta:H,setState:U,errors:N,flags:D}=R_(b,{modelValue:r,form:g,bails:s,label:a,type:i,validate:y.value?re:void 0,schema:S?t:void 0}),se=O(()=>N.value[0]);p&&V_({value:$,prop:p,handleChange:K,shouldValidate:()=>u&&!D.pendingReset});const G=(Q,k=!1)=>{H.touched=!0,k&&fe()};async function le(Q){var k,Y;if(g!=null&&g.validateSchema){const{results:Z}=await g.validateSchema(Q);return(k=Z[Ne(b)])!==null&&k!==void 0?k:{valid:!0,errors:[]}}return y.value?Mp($.value,y.value,{name:Ne(b),label:Ne(a),values:(Y=g==null?void 0:g.values)!==null&&Y!==void 0?Y:{},bails:s}):{valid:!0,errors:[]}}const fe=Jl(async()=>(H.pending=!0,H.validated=!0,le("validated-only")),Q=>(D.pendingUnmount[Fe.id]||(U({errors:Q.errors}),H.pending=!1,H.valid=Q.valid),Q)),Se=Jl(async()=>le("silent"),Q=>(H.valid=Q.valid,Q));function re(Q){return(Q==null?void 0:Q.mode)==="silent"?Se():fe()}function K(Q,k=!0){const Y=Hs(Q);Ae(Y,k)}We(()=>{if(o)return fe();(!g||!g.validateSchema)&&Se()});function me(Q){H.touched=Q}function De(Q){var k;const Y=Q&&"value"in Q?Q.value:I.value;U({value:je(Y),initialValue:je(Y),touched:(k=Q==null?void 0:Q.touched)!==null&&k!==void 0?k:!1,errors:(Q==null?void 0:Q.errors)||[]}),H.pending=!1,H.validated=!1,Se()}const ht=$r();function Ae(Q,k=!0){$.value=ht&&p?h_(Q,ht.props.modelModifiers):Q,(k?fe:Se)()}function Re(Q){U({errors:Array.isArray(Q)?Q:[Q]})}const Xe=O({get(){return $.value},set(Q){Ae(Q,u)}}),Fe={id:A,name:b,label:a,value:Xe,meta:H,errors:N,errorMessage:se,type:i,checkedValue:l,uncheckedValue:c,bails:s,keepValueOnUnmount:d,resetField:De,handleReset:()=>De(),validate:re,handleChange:K,handleBlur:G,setState:U,setTouched:me,setErrors:Re,setValue:Ae};if(st(l_,Fe),Ye(t)&&typeof R(t)!="function"&&ut(t,(Q,k)=>{Rt(Q,k)||(H.validated?fe():Se())},{deep:!0}),!g)return Fe;const Zt=O(()=>{const Q=y.value;return!Q||gt(Q)||Ns(Q)||Xt(Q)||Array.isArray(Q)?{}:Object.keys(Q).reduce((k,Y)=>{const Z=w_(Q[Y]).map(ue=>ue.__locatorRef).reduce((ue,Me)=>{const w=It(g.values,Me)||g.values[Me];return w!==void 0&&(ue[Me]=w),ue},{});return Object.assign(k,Z),k},{})});return ut(Zt,(Q,k)=>{if(!Object.keys(Q).length)return;!Rt(Q,k)&&(H.validated?fe():Se())}),Aa(()=>{var Q;const k=(Q=Ne(Fe.keepValueOnUnmount))!==null&&Q!==void 0?Q:Ne(g.keepValuesOnUnmount),Y=Ne(b);if(k||!g||D.pendingUnmount[Fe.id]){g==null||g.removePathState(Y,A);return}D.pendingUnmount[Fe.id]=!0;const Z=g.getPathState(Y);if(Array.isArray(Z==null?void 0:Z.id)&&(Z!=null&&Z.multiple)?Z!=null&&Z.id.includes(Fe.id):(Z==null?void 0:Z.id)===Fe.id){if(Z!=null&&Z.multiple&&Array.isArray(Z.value)){const Me=Z.value.findIndex(w=>Rt(w,Ne(Fe.checkedValue)));if(Me>-1){const w=[...Z.value];w.splice(Me,1),g.setFieldValue(Y,w)}Array.isArray(Z.id)&&Z.id.splice(Z.id.indexOf(Fe.id),1)}else g.unsetPathValue(Ne(b));g.removePathState(Y,A)}}),Fe}function H_(e){const t=()=>({initialValue:void 0,validateOnMount:!1,bails:!0,label:void 0,validateOnValueUpdate:!0,keepValueOnUnmount:void 0,syncVModel:!1,controlled:!0}),n=!!(e!=null&&e.syncVModel),r=typeof(e==null?void 0:e.syncVModel)=="string"?e.syncVModel:(e==null?void 0:e.modelPropName)||"modelValue",o=n&&!("initialValue"in(e||{}))?ea($r(),r):e==null?void 0:e.initialValue;if(!e)return Object.assign(Object.assign({},t()),{initialValue:o});const s="valueProp"in e?e.valueProp:e.checkedValue,i="standalone"in e?!e.standalone:e.controlled,l=(e==null?void 0:e.modelPropName)||(e==null?void 0:e.syncVModel)||!1;return Object.assign(Object.assign(Object.assign({},t()),e||{}),{initialValue:o,controlled:i??!0,checkedValue:s,syncVModel:l})}function F_(e,t,n){const r=n!=null&&n.standalone?void 0:Op(Ai),o=n==null?void 0:n.checkedValue,s=n==null?void 0:n.uncheckedValue;function i(l){const a=l.handleChange,u=O(()=>{const f=Ne(l.value),d=Ne(o);return Array.isArray(f)?f.findIndex(p=>Rt(p,d))>=0:Rt(d,f)});function c(f,d=!0){var p,v;if(u.value===((p=f==null?void 0:f.target)===null||p===void 0?void 0:p.checked)){d&&l.validate();return}const m=Ne(e),g=r==null?void 0:r.getPathState(m),b=Hs(f);let y=(v=Ne(o))!==null&&v!==void 0?v:b;r&&(g!=null&&g.multiple)&&g.type==="checkbox"?y=Jc(It(r.values,m)||[],y,void 0):(n==null?void 0:n.type)==="checkbox"&&(y=Jc(Ne(l.value),y,Ne(s))),a(y,d)}return Object.assign(Object.assign({},l),{checked:u,checkedValue:o,uncheckedValue:s,handleChange:c})}return i(Ip(e,t,n))}function V_({prop:e,value:t,handleChange:n,shouldValidate:r}){const o=$r();if(!o||!e)return;const s=typeof e=="string"?e:"modelValue",i=`update:${s}`;s in o.props&&(ut(t,l=>{Rt(l,ea(o,s))||o.emit(i,l)}),ut(()=>ea(o,s),l=>{if(l===Is&&t.value===void 0)return;const a=l===Is?void 0:l;Rt(a,t.value)||n(a,r())}))}function ea(e,t){if(e)return e.props[t]}const D_=_e({name:"Field",inheritAttrs:!1,props:{as:{type:[String,Object],default:void 0},name:{type:String,required:!0},rules:{type:[Object,String,Function],default:void 0},validateOnMount:{type:Boolean,default:!1},validateOnBlur:{type:Boolean,default:void 0},validateOnChange:{type:Boolean,default:void 0},validateOnInput:{type:Boolean,default:void 0},validateOnModelUpdate:{type:Boolean,default:void 0},bails:{type:Boolean,default:()=>pr().bails},label:{type:String,default:void 0},uncheckedValue:{type:null,default:void 0},modelValue:{type:null,default:Is},modelModifiers:{type:null,default:()=>({})},"onUpdate:modelValue":{type:null,default:void 0},standalone:{type:Boolean,default:!1},keepValue:{type:Boolean,default:void 0}},setup(e,t){const n=lr(e,"rules"),r=lr(e,"name"),o=lr(e,"label"),s=lr(e,"uncheckedValue"),i=lr(e,"keepValue"),{errors:l,value:a,errorMessage:u,validate:c,handleChange:f,handleBlur:d,setTouched:p,resetField:v,handleReset:m,meta:g,checked:b,setErrors:y,setValue:S}=N_(r,n,{validateOnMount:e.validateOnMount,bails:e.bails,standalone:e.standalone,type:t.attrs.type,initialValue:j_(e,t),checkedValue:t.attrs.value,uncheckedValue:s,label:o,validateOnValueUpdate:e.validateOnModelUpdate,keepValueOnUnmount:i,syncVModel:!0}),A=function(D,se=!0){f(D,se)},$=O(()=>{const{validateOnInput:N,validateOnChange:D,validateOnBlur:se,validateOnModelUpdate:G}=B_(e);function le(K){d(K,se),gt(t.attrs.onBlur)&&t.attrs.onBlur(K)}function fe(K){A(K,N),gt(t.attrs.onInput)&&t.attrs.onInput(K)}function Se(K){A(K,D),gt(t.attrs.onChange)&&t.attrs.onChange(K)}const re={name:e.name,onBlur:le,onInput:fe,onChange:Se};return re["onUpdate:modelValue"]=K=>A(K,G),re}),I=O(()=>{const N=Object.assign({},$.value);Xo(t.attrs.type)&&b&&(N.checked=b.value);const D=td(e,t);return p_(D,t.attrs)&&(N.value=a.value),N}),H=O(()=>Object.assign(Object.assign({},$.value),{modelValue:a.value}));function U(){return{field:I.value,componentField:H.value,value:a.value,meta:g,errors:l.value,errorMessage:u.value,validate:c,resetField:v,handleChange:A,handleInput:N=>A(N,!1),handleReset:m,handleBlur:$.value.onBlur,setTouched:p,setErrors:y,setValue:S}}return t.expose({value:a,meta:g,errors:l,errorMessage:u,setErrors:y,setTouched:p,setValue:S,reset:v,validate:c,handleChange:f}),()=>{const N=xa(td(e,t)),D=nu(N,t,U);return N?Oe(N,Object.assign(Object.assign({},t.attrs),I.value),D):D}}});function td(e,t){let n=e.as||"";return!e.as&&!t.slots.default&&(n="input"),n}function B_(e){var t,n,r,o;const{validateOnInput:s,validateOnChange:i,validateOnBlur:l,validateOnModelUpdate:a}=pr();return{validateOnInput:(t=e.validateOnInput)!==null&&t!==void 0?t:s,validateOnChange:(n=e.validateOnChange)!==null&&n!==void 0?n:i,validateOnBlur:(r=e.validateOnBlur)!==null&&r!==void 0?r:l,validateOnModelUpdate:(o=e.validateOnModelUpdate)!==null&&o!==void 0?o:a}}function j_(e,t){return Xo(t.attrs.type)?Gc(e,"modelValue")?e.modelValue:void 0:Gc(e,"modelValue")?e.modelValue:t.attrs.value}const al=D_;let z_=0;const ds=["bails","fieldsCount","id","multiple","type","validate"];function Np(e){const t=(e==null?void 0:e.initialValues)||{},n=Object.assign({},Ne(t)),r=R(e==null?void 0:e.validationSchema);return r&&Xt(r)&&gt(r.cast)?je(r.cast(n)||{}):je(n)}function U_(e){var t;const n=z_++;let r=0;const o=F(!1),s=F(!1),i=F(0),l=[],a=_t(Np(e)),u=F([]),c=F({}),f=F({}),d=v_(()=>{f.value=u.value.reduce((C,E)=>(C[so(Ne(E.path))]=E,C),{})});function p(C,E){const j=re(C);if(!j){typeof C=="string"&&(c.value[so(C)]=Xl(E));return}if(typeof C=="string"){const ne=so(C);c.value[ne]&&delete c.value[ne]}j.errors=Xl(E),j.valid=!j.errors.length}function v(C){Vt(C).forEach(E=>{p(E,C[E])})}e!=null&&e.initialErrors&&v(e.initialErrors);const m=O(()=>{const C=u.value.reduce((E,j)=>(j.errors.length&&(E[j.path]=j.errors),E),{});return Object.assign(Object.assign({},c.value),C)}),g=O(()=>Vt(m.value).reduce((C,E)=>{const j=m.value[E];return j!=null&&j.length&&(C[E]=j[0]),C},{})),b=O(()=>u.value.reduce((C,E)=>(C[E.path]={name:E.path||"",label:E.label||""},C),{})),y=O(()=>u.value.reduce((C,E)=>{var j;return C[E.path]=(j=E.bails)!==null&&j!==void 0?j:!0,C},{})),S=Object.assign({},(e==null?void 0:e.initialErrors)||{}),A=(t=e==null?void 0:e.keepValuesOnUnmount)!==null&&t!==void 0?t:!1,{initialValues:$,originalInitialValues:I,setInitialValues:H}=q_(u,a,e),U=Z_(u,a,I,g),N=O(()=>u.value.reduce((C,E)=>{const j=It(a,E.path);return _n(C,E.path,j),C},{})),D=e==null?void 0:e.validationSchema;function se(C,E){var j,ne;const ve=O(()=>It($.value,Ne(C))),Ee=f.value[Ne(C)],de=(E==null?void 0:E.type)==="checkbox"||(E==null?void 0:E.type)==="radio";if(Ee&&de){Ee.multiple=!0;const qt=r++;return Array.isArray(Ee.id)?Ee.id.push(qt):Ee.id=[Ee.id,qt],Ee.fieldsCount++,Ee.__flags.pendingUnmount[qt]=!1,Ee}const Ie=O(()=>It(a,Ne(C))),rt=Ne(C),St=me.findIndex(qt=>qt===rt);St!==-1&&me.splice(St,1);const Qe=O(()=>{var qt,Gr,Ri,Mi;const ki=Ne(D);if(Xt(ki))return(Gr=(qt=ki.describe)===null||qt===void 0?void 0:qt.call(ki,Ne(C)).required)!==null&&Gr!==void 0?Gr:!1;const Li=Ne(E==null?void 0:E.schema);return Xt(Li)&&(Mi=(Ri=Li.describe)===null||Ri===void 0?void 0:Ri.call(Li).required)!==null&&Mi!==void 0?Mi:!1}),$t=r++,kt=_t({id:$t,path:C,touched:!1,pending:!1,valid:!0,validated:!!(!((j=S[rt])===null||j===void 0)&&j.length),required:Qe,initialValue:ve,errors:_a([]),bails:(ne=E==null?void 0:E.bails)!==null&&ne!==void 0?ne:!1,label:E==null?void 0:E.label,type:(E==null?void 0:E.type)||"default",value:Ie,multiple:!1,__flags:{pendingUnmount:{[$t]:!1},pendingReset:!1},fieldsCount:1,validate:E==null?void 0:E.validate,dirty:O(()=>!Rt(R(Ie),R(ve)))});return u.value.push(kt),f.value[rt]=kt,d(),g.value[rt]&&!S[rt]&&et(()=>{J(rt,{mode:"silent"})}),Ye(C)&&ut(C,qt=>{d();const Gr=je(Ie.value);f.value[qt]=kt,et(()=>{_n(a,qt,Gr)})}),kt}const G=Xc(ie,5),le=Xc(ie,5),fe=Jl(async C=>await(C==="silent"?G():le()),(C,[E])=>{const j=Vt(Q.errorBag.value),ve=[...new Set([...Vt(C.results),...u.value.map(Ee=>Ee.path),...j])].sort().reduce((Ee,de)=>{var Ie;const rt=de,St=re(rt)||K(rt),Qe=((Ie=C.results[rt])===null||Ie===void 0?void 0:Ie.errors)||[],$t=Ne(St==null?void 0:St.path)||rt,kt=W_({errors:Qe,valid:!Qe.length},Ee.results[$t]);return Ee.results[$t]=kt,kt.valid||(Ee.errors[$t]=kt.errors[0]),St&&c.value[$t]&&delete c.value[$t],St?(St.valid=kt.valid,E==="silent"||E==="validated-only"&&!St.validated||p(St,kt.errors),Ee):(p($t,Qe),Ee)},{valid:C.valid,results:{},errors:{},source:C.source});return C.values&&(ve.values=C.values,ve.source=C.source),Vt(ve.results).forEach(Ee=>{var de;const Ie=re(Ee);Ie&&E!=="silent"&&(E==="validated-only"&&!Ie.validated||p(Ie,(de=ve.results[Ee])===null||de===void 0?void 0:de.errors))}),ve});function Se(C){u.value.forEach(C)}function re(C){const E=typeof C=="string"?so(C):C;return typeof E=="string"?f.value[E]:E}function K(C){return u.value.filter(j=>C.startsWith(j.path)).reduce((j,ne)=>j?ne.path.length>j.path.length?ne:j:ne,void 0)}let me=[],De;function ht(C){return me.push(C),De||(De=et(()=>{[...me].sort().reverse().forEach(j=>{Yc(a,j)}),me=[],De=null})),De}function Ae(C){return function(j,ne){return function(Ee){return Ee instanceof Event&&(Ee.preventDefault(),Ee.stopPropagation()),Se(de=>de.touched=!0),o.value=!0,i.value++,te().then(de=>{const Ie=je(a);if(de.valid&&typeof j=="function"){const rt=je(N.value);let St=C?rt:Ie;return de.values&&(St=de.source==="schema"?de.values:Object.assign({},St,de.values)),j(St,{evt:Ee,controlledValues:rt,setErrors:v,setFieldError:p,setTouched:V,setFieldTouched:Me,setValues:Z,setFieldValue:k,resetForm:B,resetField:z})}!de.valid&&typeof ne=="function"&&ne({values:Ie,evt:Ee,errors:de.errors,results:de.results})}).then(de=>(o.value=!1,de),de=>{throw o.value=!1,de})}}}const Xe=Ae(!1);Xe.withControlled=Ae(!0);function Fe(C,E){const j=u.value.findIndex(ve=>ve.path===C&&(Array.isArray(ve.id)?ve.id.includes(E):ve.id===E)),ne=u.value[j];if(!(j===-1||!ne)){if(et(()=>{J(C,{mode:"silent",warn:!1})}),ne.multiple&&ne.fieldsCount&&ne.fieldsCount--,Array.isArray(ne.id)){const ve=ne.id.indexOf(E);ve>=0&&ne.id.splice(ve,1),delete ne.__flags.pendingUnmount[E]}(!ne.multiple||ne.fieldsCount<=0)&&(u.value.splice(j,1),ee(C),d(),delete f.value[C])}}function Zt(C){Vt(f.value).forEach(E=>{E.startsWith(C)&&delete f.value[E]}),u.value=u.value.filter(E=>!E.path.startsWith(C)),et(()=>{d()})}const Q={formId:n,values:a,controlledValues:N,errorBag:m,errors:g,schema:D,submitCount:i,meta:U,isSubmitting:o,isValidating:s,fieldArrays:l,keepValuesOnUnmount:A,validateSchema:R(D)?fe:void 0,validate:te,setFieldError:p,validateField:J,setFieldValue:k,setValues:Z,setErrors:v,setFieldTouched:Me,setTouched:V,resetForm:B,resetField:z,handleSubmit:Xe,useFieldModel:Le,defineInputBinds:Ze,defineComponentBinds:Ve,defineField:be,stageInitialValue:W,unsetInitialValue:ee,setFieldInitialValue:ye,createPathState:se,getPathState:re,unsetPathValue:ht,removePathState:Fe,initialValues:$,getAllPathStates:()=>u.value,destroyPath:Zt,isFieldTouched:w,isFieldDirty:_,isFieldValid:P};function k(C,E,j=!0){const ne=je(E),ve=typeof C=="string"?C:C.path;re(ve)||se(ve),_n(a,ve,ne),j&&J(ve)}function Y(C,E=!0){Vt(a).forEach(j=>{delete a[j]}),Vt(C).forEach(j=>{k(j,C[j],!1)}),E&&te()}function Z(C,E=!0){Mo(a,C),l.forEach(j=>j&&j.reset()),E&&te()}function ue(C,E){const j=re(Ne(C))||se(C);return O({get(){return j.value},set(ne){var ve;const Ee=Ne(C);k(Ee,ne,(ve=Ne(E))!==null&&ve!==void 0?ve:!1)}})}function Me(C,E){const j=re(C);j&&(j.touched=E)}function w(C){const E=re(C);return E?E.touched:u.value.filter(j=>j.path.startsWith(C)).some(j=>j.touched)}function _(C){const E=re(C);return E?E.dirty:u.value.filter(j=>j.path.startsWith(C)).some(j=>j.dirty)}function P(C){const E=re(C);return E?E.valid:u.value.filter(j=>j.path.startsWith(C)).every(j=>j.valid)}function V(C){if(typeof C=="boolean"){Se(E=>{E.touched=C});return}Vt(C).forEach(E=>{Me(E,!!C[E])})}function z(C,E){var j;const ne=E&&"value"in E?E.value:It($.value,C),ve=re(C);ve&&(ve.__flags.pendingReset=!0),ye(C,je(ne),!0),k(C,ne,!1),Me(C,(j=E==null?void 0:E.touched)!==null&&j!==void 0?j:!1),p(C,(E==null?void 0:E.errors)||[]),et(()=>{ve&&(ve.__flags.pendingReset=!1)})}function B(C,E){let j=je(C!=null&&C.values?C.values:I.value);j=E!=null&&E.force?j:Mo(I.value,j),j=Xt(D)&&gt(D.cast)?D.cast(j):j,H(j,{force:E==null?void 0:E.force}),Se(ne=>{var ve;ne.__flags.pendingReset=!0,ne.validated=!1,ne.touched=((ve=C==null?void 0:C.touched)===null||ve===void 0?void 0:ve[ne.path])||!1,k(ne.path,It(j,ne.path),!1),p(ne.path,void 0)}),E!=null&&E.force?Y(j,!1):Z(j,!1),v((C==null?void 0:C.errors)||{}),i.value=(C==null?void 0:C.submitCount)||0,et(()=>{te({mode:"silent"}),Se(ne=>{ne.__flags.pendingReset=!1})})}async function te(C){const E=(C==null?void 0:C.mode)||"force";if(E==="force"&&Se(de=>de.validated=!0),Q.validateSchema)return Q.validateSchema(E);s.value=!0;const j=await Promise.all(u.value.map(de=>de.validate?de.validate(C).then(Ie=>({key:de.path,valid:Ie.valid,errors:Ie.errors,value:Ie.value})):Promise.resolve({key:de.path,valid:!0,errors:[],value:void 0})));s.value=!1;const ne={},ve={},Ee={};for(const de of j)ne[de.key]={valid:de.valid,errors:de.errors},de.value&&_n(Ee,de.key,de.value),de.errors.length&&(ve[de.key]=de.errors[0]);return{valid:j.every(de=>de.valid),results:ne,errors:ve,values:Ee,source:"fields"}}async function J(C,E){var j;const ne=re(C);if(ne&&(E==null?void 0:E.mode)!=="silent"&&(ne.validated=!0),D){const{results:ve}=await fe((E==null?void 0:E.mode)||"validated-only");return ve[C]||{errors:[],valid:!0}}return ne!=null&&ne.validate?ne.validate(E):(!ne&&(j=E==null?void 0:E.warn),Promise.resolve({errors:[],valid:!0}))}function ee(C){Yc($.value,C)}function W(C,E,j=!1){ye(C,E),_n(a,C,E),j&&!(e!=null&&e.initialValues)&&_n(I.value,C,je(E))}function ye(C,E,j=!1){_n($.value,C,je(E)),j&&_n(I.value,C,je(E))}async function ie(){const C=R(D);if(!C)return{valid:!0,results:{},errors:{},source:"none"};s.value=!0;const E=Ns(C)||Xt(C)?await O_(C,a):await P_(C,a,{names:b.value,bailsMap:y.value});return s.value=!1,E}const ge=Xe((C,{evt:E})=>{Tp(E)&&E.target.submit()});We(()=>{if(e!=null&&e.initialErrors&&v(e.initialErrors),e!=null&&e.initialTouched&&V(e.initialTouched),e!=null&&e.validateOnMount){te();return}Q.validateSchema&&Q.validateSchema("silent")}),Ye(D)&&ut(D,()=>{var C;(C=Q.validateSchema)===null||C===void 0||C.call(Q,"validated-only")}),st(Ai,Q);function be(C,E){const j=gt(E)||E==null?void 0:E.label,ne=re(Ne(C))||se(C,{label:j}),ve=()=>gt(E)?E(cs(ne,ds)):E||{};function Ee(){var Qe;ne.touched=!0,((Qe=ve().validateOnBlur)!==null&&Qe!==void 0?Qe:pr().validateOnBlur)&&J(ne.path)}function de(){var Qe;((Qe=ve().validateOnInput)!==null&&Qe!==void 0?Qe:pr().validateOnInput)&&et(()=>{J(ne.path)})}function Ie(){var Qe;((Qe=ve().validateOnChange)!==null&&Qe!==void 0?Qe:pr().validateOnChange)&&et(()=>{J(ne.path)})}const rt=O(()=>{const Qe={onChange:Ie,onInput:de,onBlur:Ee};return gt(E)?Object.assign(Object.assign({},Qe),E(cs(ne,ds)).props||{}):E!=null&&E.props?Object.assign(Object.assign({},Qe),E.props(cs(ne,ds))):Qe});return[ue(C,()=>{var Qe,$t,kt;return(kt=(Qe=ve().validateOnModelUpdate)!==null&&Qe!==void 0?Qe:($t=pr())===null||$t===void 0?void 0:$t.validateOnModelUpdate)!==null&&kt!==void 0?kt:!0}),rt]}function Le(C){return Array.isArray(C)?C.map(E=>ue(E,!0)):ue(C)}function Ze(C,E){const[j,ne]=be(C,E);function ve(){ne.value.onBlur()}function Ee(Ie){const rt=Hs(Ie);k(Ne(C),rt,!1),ne.value.onInput()}function de(Ie){const rt=Hs(Ie);k(Ne(C),rt,!1),ne.value.onChange()}return O(()=>Object.assign(Object.assign({},ne.value),{onBlur:ve,onInput:Ee,onChange:de,value:j.value}))}function Ve(C,E){const[j,ne]=be(C,E),ve=re(Ne(C));function Ee(de){j.value=de}return O(()=>{const de=gt(E)?E(cs(ve,ds)):E||{};return Object.assign({[de.model||"modelValue"]:j.value,[`onUpdate:${de.model||"modelValue"}`]:Ee},ne.value)})}return Object.assign(Object.assign({},Q),{values:ga(a),handleReset:()=>B(),submitForm:ge})}function Z_(e,t,n,r){const o={touched:"some",pending:"some",valid:"every"},s=O(()=>!Rt(t,R(n)));function i(){const a=e.value;return Vt(o).reduce((u,c)=>{const f=o[c];return u[c]=a[f](d=>d[c]),u},{})}const l=_t(i());return it(()=>{const a=i();l.touched=a.touched,l.valid=a.valid,l.pending=a.pending}),O(()=>Object.assign(Object.assign({initialValues:R(n)},l),{valid:l.valid&&!Vt(r.value).length,dirty:s.value}))}function q_(e,t,n){const r=Np(n),o=F(r),s=F(je(r));function i(l,a){a!=null&&a.force?(o.value=je(l),s.value=je(l)):(o.value=Mo(je(o.value)||{},je(l)),s.value=Mo(je(s.value)||{},je(l))),a!=null&&a.updateFields&&e.value.forEach(u=>{if(u.touched)return;const f=It(o.value,u.path);_n(t,u.path,je(f))})}return{initialValues:o,originalInitialValues:s,setInitialValues:i}}function W_(e,t){return t?{valid:e.valid&&t.valid,errors:[...e.errors,...t.errors]}:e}const G_=_e({name:"Form",inheritAttrs:!1,props:{as:{type:null,default:"form"},validationSchema:{type:Object,default:void 0},initialValues:{type:Object,default:void 0},initialErrors:{type:Object,default:void 0},initialTouched:{type:Object,default:void 0},validateOnMount:{type:Boolean,default:!1},onSubmit:{type:Function,default:void 0},onInvalidSubmit:{type:Function,default:void 0},keepValues:{type:Boolean,default:!1}},setup(e,t){const n=lr(e,"validationSchema"),r=lr(e,"keepValues"),{errors:o,errorBag:s,values:i,meta:l,isSubmitting:a,isValidating:u,submitCount:c,controlledValues:f,validate:d,validateField:p,handleReset:v,resetForm:m,handleSubmit:g,setErrors:b,setFieldError:y,setFieldValue:S,setValues:A,setFieldTouched:$,setTouched:I,resetField:H}=U_({validationSchema:n.value?n:void 0,initialValues:e.initialValues,initialErrors:e.initialErrors,initialTouched:e.initialTouched,validateOnMount:e.validateOnMount,keepValuesOnUnmount:r}),U=g((re,{evt:K})=>{Tp(K)&&K.target.submit()},e.onInvalidSubmit),N=e.onSubmit?g(e.onSubmit,e.onInvalidSubmit):U;function D(re){eu(re)&&re.preventDefault(),v(),typeof t.attrs.onReset=="function"&&t.attrs.onReset()}function se(re,K){return g(typeof re=="function"&&!K?re:K,e.onInvalidSubmit)(re)}function G(){return je(i)}function le(){return je(l.value)}function fe(){return je(o.value)}function Se(){return{meta:l.value,errors:o.value,errorBag:s.value,values:i,isSubmitting:a.value,isValidating:u.value,submitCount:c.value,controlledValues:f.value,validate:d,validateField:p,handleSubmit:se,handleReset:v,submitForm:U,setErrors:b,setFieldError:y,setFieldValue:S,setValues:A,setFieldTouched:$,setTouched:I,resetForm:m,resetField:H,getValues:G,getMeta:le,getErrors:fe}}return t.expose({setFieldError:y,setErrors:b,setFieldValue:S,setValues:A,setFieldTouched:$,setTouched:I,resetForm:m,validate:d,validateField:p,resetField:H,getValues:G,getMeta:le,getErrors:fe,values:i,meta:l,errors:o}),function(){const K=e.as==="form"?e.as:e.as?xa(e.as):null,me=nu(K,t,Se);return K?Oe(K,Object.assign(Object.assign(Object.assign({},K==="form"?{novalidate:!0}:{}),t.attrs),{onSubmit:N,onReset:D}),me):me}}}),K_=G_,Y_=_e({name:"ErrorMessage",props:{as:{type:String,default:void 0},name:{type:String,required:!0}},setup(e,t){const n=ae(Ai,void 0),r=O(()=>n==null?void 0:n.errors.value[e.name]);function o(){return{message:r.value}}return()=>{if(!r.value)return;const s=e.as?xa(e.as):e.as,i=nu(s,t,o),l=Object.assign({role:"alert"},t.attrs);return!s&&(Array.isArray(i)||!i)&&(i!=null&&i.length)?i:(Array.isArray(i)||!i)&&!(i!=null&&i.length)?Oe(s||"span",l,r.value):Oe(s,l,i)}}}),ul=Y_;async function J_(){return[{label:"Meno di 1 anno",value:"LT1"},{label:"Tra 3 e 5 anni",value:"1T5"},{label:"Tra 5 e 10 anni",value:"5T10"},{label:"Oltre 10 anni",value:"MT10"}]}async function X_(){var t;const e=(t=await Ce.get("/apps/uniqum/enrollable"))==null?void 0:t.data;return e!=null&&e.success?e.data:[]}async function Q_(){var t;const e=(t=await Ce.get("/apps/uniqum/enrolled"))==null?void 0:t.data;return e!=null&&e.success?e:[]}async function e5(){var t;const e=(t=await Ce.get("/apps/uniqum/enrollable"))==null?void 0:t.data;return e!=null&&e.success?e.data.length:!1}const Fr={getSenioritySteps:J_,getEnrollable:X_,getEnrolled:Q_,hasEnrollable:e5};function t5(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"})])}function n5(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function r5(e,t){return x(),T("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[h("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"})])}/**
  * vee-validate v4.13.2
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */function Hp(e,t){return Array.isArray(e)?e[0]:e[t]}function Fp(e){return!!(e==null||e===""||Array.isArray(e)&&e.length===0)}const Vp=(e,t)=>{if(Fp(e))return!0;const n=Hp(t,"max");return Array.isArray(e)?e.length>0&&e.every(r=>Vp(r,{max:n})):Number(e)<=Number(n)},Dp=(e,t)=>{if(Fp(e))return!0;const n=Hp(t,"min");return Array.isArray(e)?e.length>0&&e.every(r=>Dp(r,{min:n})):Number(e)>=Number(n)};function o5(e){return e==null}function s5(e){return Array.isArray(e)&&e.length===0}const i5=e=>o5(e)||s5(e)||e===!1?!1:!!String(e).trim().length;/**
  * vee-validate v4.13.2
  * (c) 2024 Abdelrahman Awad
  * @license MIT
  */function nd(e){return typeof e=="function"}function l5(e){return typeof e=="object"&&e!==null}function a5(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}function rd(e){if(!l5(e)||a5(e)!=="[object Object]")return!1;if(Object.getPrototypeOf(e)===null)return!0;let t=e;for(;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Bp(e,t){return Object.keys(t).forEach(n=>{if(rd(t[n])&&rd(e[n])){e[n]||(e[n]={}),Bp(e[n],t[n]);return}e[n]=t[n]}),e}function od(e,t,n){const{prefix:r,suffix:o}=n,s=new RegExp(`([0-9]:)?${r}([^${o}]+)${o}`,"g");return e.replace(s,function(i,l,a){if(!l||!t.params)return a in t?t[a]:t.params&&a in t.params?t.params[a]:`${r}${a}${o}`;if(!Array.isArray(t.params))return a in t.params?t.params[a]:`${r}${a}${o}`;const u=Number(l.replace(":",""));return u in t.params?t.params[u]:`${l}${r}${a}${o}`})}class u5{constructor(t,n,r={prefix:"{",suffix:"}"}){this.container={},this.locale=t,this.interpolateOptions=r,this.merge(n)}resolve(t,n){return this.format(this.locale,t,n)}getLocaleDefault(t,n){var r,o,s,i,l;return((s=(o=(r=this.container[t])===null||r===void 0?void 0:r.fields)===null||o===void 0?void 0:o[n])===null||s===void 0?void 0:s._default)||((l=(i=this.container[t])===null||i===void 0?void 0:i.messages)===null||l===void 0?void 0:l._default)}resolveLabel(t,n,r){var o,s,i,l;return r?((s=(o=this.container[t])===null||o===void 0?void 0:o.names)===null||s===void 0?void 0:s[r])||r:((l=(i=this.container[t])===null||i===void 0?void 0:i.names)===null||l===void 0?void 0:l[n])||n}format(t,n,r){var o,s,i,l,a;let u;const{rule:c,form:f,label:d,name:p}=n,v=this.resolveLabel(t,p,d);return c?(u=((i=(s=(o=this.container[t])===null||o===void 0?void 0:o.fields)===null||s===void 0?void 0:s[p])===null||i===void 0?void 0:i[c.name])||((a=(l=this.container[t])===null||l===void 0?void 0:l.messages)===null||a===void 0?void 0:a[c.name]),u||(u=this.getLocaleDefault(t,p)||`${v} is not valid`),nd(u)?u(n):od(u,Object.assign(Object.assign({},f),{field:v,params:c.params}),r??this.interpolateOptions)):(u=this.getLocaleDefault(t,p)||`${v} is not valid`,nd(u)?u(n):od(u,Object.assign(Object.assign({},f),{field:v}),r??this.interpolateOptions))}merge(t){Bp(this.container,t)}}const cl=new u5("en",{});function c5(e,t,n){const r=o=>cl.resolve(o,n);return cl.locale=e,t&&cl.merge({[e]:t}),r}const d5={class:"fixed inset-0 overflow-y-auto"},f5={class:"flex min-h-full items-center justify-center p-4"},p5={class:"card max-w-2xl"},h5={key:0,class:"flex items-center gap-3 p-3 border-2 border-gray-700 rounded-lg mb-4 relative"},v5={class:"grid grid-cols-6 gap-x-5"},m5={class:"col-span-6 md:col-span-3"},g5={class:"form-group"},y5={class:"flex justify-between items-center"},b5=["value"],w5={class:"form-error"},_5={class:"col-span-3 md:col-span-1"},S5={class:"form-group"},$5={class:"form-error"},A5={class:"col-span-3 md:col-span-2"},x5={class:"form-group"},E5=["value"],C5={class:"form-error"},T5={class:"col-span-6 md:col-span-3"},O5={class:"form-group"},P5={class:"flex items-center"},R5={class:"flex justify-center gap-5"},M5={type:"submit",class:"btn btn-outline flex items-center gap-3"},k5={__name:"TeamSelectionModal",emits:["close","teamMemberAdded"],setup(e,{expose:t,emit:n}){$_({generateMessage:c5("it",{messages:{required:"Questo campo è obbligatorio",min:"Il valore minimo consentito è 18",max:"Il valore massimo consentito è 99"}})}),sl("required",i5),sl("min",Dp),sl("max",Vp);const r=n;let o=F(!1);const s=F([]),i=F([]),l=F(0),a=F(!1);async function u(){o.value=!0,s.value=await Fr.getEnrollable(),i.value=await Fr.getSenioritySteps()}function c(){o.value=!1,r("close")}async function f(d){d.isFullTime=l.value;const p=await Fr.enrollUser(d);if(!(p!=null&&p.success)){Pe.error("Errore imprevisto.");return}Pe.success("Dipendente aggiunto al team."),r("teamMemberAdded"),c()}return t({openModal:u}),(d,p)=>(x(),tt(R(Zr),{appear:"",show:R(o),as:"template"},{default:X(()=>[M(R(qo),{as:"div",onClose:c,class:"relative z-10"},{default:X(()=>[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:X(()=>p[4]||(p[4]=[h("div",{class:"fixed inset-0",style:{"background-color":"rgba(24,27,49,0.9)"}},null,-1)])),_:1}),h("div",d5,[h("div",f5,[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:X(()=>[h("div",p5,[p[15]||(p[15]=h("h1",{class:"text-2xl font-bold mb-6"},"Seleziona Dipendente",-1)),a.value?(x(),T("div",h5,[h("div",null,[M(R(r5),{class:"size-10"})]),p[5]||(p[5]=h("div",{class:"font-medium"},[oe(" Per comparire nell’elenco il collaboratore deve essere correttamente censito su Portale Agendo con qualifica di «Dipendente di Agenzia» o «Dipendente di Subagenzia». "),h("br"),oe(" Clicca su "),h("a",{class:"underline text-blue-800",href:"/#/apps/anagrafica/",target:"_blank"},"questo link"),oe(" per registrare o aggiornare l’anagrafica del collaboratore. ")],-1)),h("div",{class:"absolute cursor-pointer border-gray-700",style:{top:"5px",right:"5px"},onClick:p[0]||(p[0]=v=>a.value=!a.value)},[M(R(s0),{class:"size-6"})])])):ce("",!0),M(R(K_),{onSubmit:f},{default:X(({errors:v})=>[h("div",v5,[h("div",m5,[h("div",g5,[h("div",y5,[p[6]||(p[6]=h("label",{class:"text-sm",style:{color:"#7D8299"},for:"employee"},"Dipendente",-1)),h("div",{class:"helper text-right text-sm cursor-pointer",onClick:p[1]||(p[1]=m=>a.value=!a.value)},"Non trovi un dipendente?")]),M(R(al),{name:"user_id",as:"select",ref:"user_id",rules:"required",class:ot({"has-error":v.user_id})},{default:X(()=>[p[7]||(p[7]=h("option",{value:"",selected:"",disabled:""},"Seleziona",-1)),(x(!0),T(he,null,yt(s.value,m=>(x(),T("option",{value:m.id},pe(m.nome)+" "+pe(m.cognome),9,b5))),256))]),_:2},1032,["class"]),h("div",w5,[M(R(ul),{name:"user_id"})])])]),h("div",_5,[h("div",S5,[p[8]||(p[8]=h("label",{for:"age"},"Età",-1)),M(R(al),{name:"age",type:"number",rules:"required|min:18|max:99",class:ot({"has-error":v.age})},null,8,["class"]),h("div",$5,[M(R(ul),{name:"age"})])])]),h("div",A5,[h("div",x5,[p[10]||(p[10]=h("label",{for:"seniority"},"Anzianità di servizio",-1)),M(R(al),{name:"seniority",as:"select",ref:"seniority",rules:"required",class:ot({"has-error":v.seniority})},{default:X(()=>[p[9]||(p[9]=h("option",{value:"",selected:"",disabled:""},"Seleziona",-1)),(x(!0),T(he,null,yt(i.value,m=>(x(),T("option",{value:m.value},pe(m.label),9,E5))),256))]),_:2},1032,["class"]),h("div",C5,[M(R(ul),{name:"seniority"})])])]),h("div",T5,[h("div",O5,[p[13]||(p[13]=h("label",{for:"isFullTime"},"Impegno lavorativo",-1)),h("div",null,[M(R(X2),null,{default:X(()=>[h("div",P5,[M(R(_c),{class:ot(["mr-2 italic",l.value?"font-normal text-gray-400":"font-bold text-emerald-900"])},{default:X(()=>p[11]||(p[11]=[oe("Part time")])),_:1},8,["class"]),M(R(Q2),{modelValue:l.value,"onUpdate:modelValue":p[2]||(p[2]=m=>l.value=m),name:"isFullTime",as:"template"},{default:X(({checked:m})=>[h("button",{class:ot(["relative inline-flex h-6 w-11 items-center rounded-full",m?"bg-blue-600":"bg-blue-300"])},[h("span",{class:ot([m?"translate-x-6":"translate-x-1","inline-block h-4 w-4 transform rounded-full bg-white transition"])},null,2)],2)]),_:1},8,["modelValue"]),M(R(_c),{class:ot(["ml-2 italic",l.value?"font-bold text-emerald-900":"font-normal text-gray-400"])},{default:X(()=>p[12]||(p[12]=[oe("Full time")])),_:1},8,["class"])])]),_:1})])])])]),h("div",R5,[h("button",{class:"btn btn-secondary",onClick:p[3]||(p[3]=()=>c())},"Chiudi"),h("button",M5,[M(R(o0),{class:"size-6"}),p[14]||(p[14]=oe(" Salva"))])])]),_:1})])]),_:1})])])]),_:1})]),_:1},8,["show"]))}},L5={class:"fixed inset-0 overflow-y-auto"},I5={class:"flex min-h-full items-center justify-center p-4"},N5={class:"card max-w-2xl"},H5={class:"mb-6"},F5={class:"flex justify-center gap-5"},V5={__name:"RemoveTeamMemberConfirm",props:["teamMemberName","teamMemberId"],emits:["close","teamMemberRemoved"],setup(e,{expose:t,emit:n}){const r=n,o=e;let s=F(!1);function i(){s.value=!0}function l(){s.value=!1}async function a(){const u=await Fr.removeEnrolledUser(o.teamMemberId);if(!(u!=null&&u.success)){Pe.error("Errore imprevisto.");return}Pe.success("Dipendente rimosso dal team."),l(),r("teamMemberRemoved")}return t({openModal:i}),(u,c)=>(x(),tt(R(Zr),{appear:"",show:R(s),as:"template"},{default:X(()=>[M(R(qo),{as:"div",onClose:l,class:"relative z-10"},{default:X(()=>[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:X(()=>c[1]||(c[1]=[h("div",{class:"fixed inset-0",style:{"background-color":"rgba(24,27,49,0.9)"}},null,-1)])),_:1}),h("div",L5,[h("div",I5,[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:X(()=>[h("div",N5,[c[3]||(c[3]=h("h1",{class:"text-2xl font-bold mb-6"},"Conferma cancellazione",-1)),h("p",H5,"Sei sicuro di voler rimuovere "+pe(o.teamMemberName)+" dal team?",1),h("div",F5,[h("button",{class:"btn btn-secondary",onClick:c[0]||(c[0]=()=>l())},"Annulla"),h("button",{type:"button",class:"btn btn-outline flex items-center gap-3",onClick:a},[M(R(o0),{class:"size-6"}),c[2]||(c[2]=oe(" Conferma"))])])])]),_:1})])])]),_:1})]),_:1},8,["show"]))}},On=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n},D5={class:"bg-white min-h-screen h-full py-16"},B5={class:"container-small"},j5={class:"py-5"},z5={key:0,class:"hidden md:grid grid-custom grid-head"},U5={class:"hidden md:grid grid-custom card card-fit items-center mb-3"},Z5={key:0},q5={key:1},W5={class:"card flex flex-col justify-normal md:hidden mb-5 text-lg"},G5={class:"mb-3"},K5={class:"font-bold"},Y5={class:"mb-3"},J5={class:"font-bold"},X5={class:"mb-3"},Q5={class:"font-bold"},e3={class:"mb-3"},t3={class:"font-bold"},n3={key:0},r3={key:1},o3={__name:"Team",setup(e){const t=F(null),n=F(null),r=F([]),o=F({}),s={LT1:"Meno di 1 anno","1T5":"Tra 3 e 5 anni","5T10":"Tra 5 e 10 anni",MT10:"Oltre 10 anni"},i=F(!0);l();async function l(){const u=await Fr.getEnrolled();if(!(u!=null&&u.success)){Pe.error("Errore imprevisto.");return}r.value=u.data;const c=await Fr.hasEnrollable();c!=null&&c.success||(i.value=c)}function a(u){return s[u]}return(u,c)=>(x(),T(he,null,[M(V5,{ref_key:"removeTeamMemberConfirm",ref:n,"team-member-name":o.value.nome+" "+o.value.cognome,"team-member-id":o.value.user_id,onTeamMemberRemoved:l},null,8,["team-member-name","team-member-id"]),M(k5,{ref_key:"teamSelectionModal",ref:t,onTeamMemberAdded:l},null,512),h("div",D5,[h("div",B5,[c[5]||(c[5]=h("h1",{class:"text-4xl font-bold mb-8"},"Traccia il percorso di crescita per il tuo team.",-1)),c[6]||(c[6]=h("p",null,"In questo momento non è possibile inserire altri i dipendenti nel progetto Uniqum. I primi corsi stanno per essere lanciati. Sarà possibile iscrivere ai corsi solo i dipendenti precedentemente selezionati. Nei primi mesi del 2025 questa sezione verrà riaperta per aggiornare l’elenco dei dipendenti che partecipano al progetto.",-1)),h("div",j5,[r.value.length?(x(),T("div",z5,c[0]||(c[0]=[h("div",null,"Dipendente",-1),h("div",null,"Età",-1),h("div",null,"Anzianità",-1),h("div",null,"Impegno lavorativo",-1)]))):ce("",!0),(x(!0),T(he,null,yt(r.value,f=>(x(),T("div",U5,[h("div",null,pe(f.nome)+" "+pe(f.cognome),1),h("div",null,pe(f.age),1),h("div",null,pe(a(f.seniority)),1),h("div",null,[f.isFullTime?(x(),T("span",Z5,"Full time")):ce("",!0),f.isFullTime?ce("",!0):(x(),T("span",q5,"Part time"))])]))),256)),(x(!0),T(he,null,yt(r.value,f=>(x(),T("div",W5,[h("div",G5,[c[1]||(c[1]=h("div",{class:"grid-head"},"Dipendente:",-1)),h("div",K5,pe(f.nome)+" "+pe(f.cognome),1)]),h("div",Y5,[c[2]||(c[2]=h("span",{class:"grid-head mr-2"},"Età:",-1)),h("span",J5,pe(f.age),1)]),h("div",X5,[c[3]||(c[3]=h("div",{class:"grid-head"},"Anzianità:",-1)),h("div",Q5,pe(a(f.seniority)),1)]),h("div",e3,[c[4]||(c[4]=h("div",{class:"grid-head"},"Impegno lavorativo:",-1)),h("div",t3,[f.isFullTime?(x(),T("span",n3,"Full time")):ce("",!0),f.isFullTime?ce("",!0):(x(),T("span",r3,"Part time"))])])]))),256))])])])],64))}},s3=On(o3,[["__scopeId","data-v-3395444b"]]),i3={class:"fixed inset-0 overflow-y-auto"},l3={class:"flex min-h-full items-center justify-center"},a3={class:"card card-fit max-w-2xl mb-6",style:{"border-radius":"30px"}},u3={key:0,class:"embed-container"},c3=["poster"],d3=["src"],f3={__name:"VideoModal",props:["cover","source"],setup(e,{expose:t}){const n=e;let r=F(!1);const o=O(()=>n.source?`https://${window.staticHost}${n.source}`:""),s=O(()=>n.cover?`https://${window.staticHost}${n.cover}`:"");function i(){r.value=!0}function l(){r.value=!1}return t({openModal:i}),(a,u)=>(x(),tt(R(Zr),{appear:"",show:R(r),as:"template"},{default:X(()=>[M(R(qo),{as:"div",onClose:l,class:"relative z-10"},{default:X(()=>[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:X(()=>u[0]||(u[0]=[h("div",{class:"fixed inset-0",style:{"background-color":"rgba(24,27,49,0.9)"}},null,-1)])),_:1}),h("div",i3,[h("div",l3,[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:X(()=>[h("div",null,[h("div",a3,[o.value?(x(),T("div",u3,[h("video",{controls:"",poster:s.value},[h("source",{src:o.value,type:"video/mp4"},null,8,d3),u[1]||(u[1]=oe(" Questo browser non supporta il tag video. "))],8,c3)])):ce("",!0)]),h("div",{class:"text-center text-white cursor-pointer text-lg"},[h("span",{class:"p-3",onClick:l},"Chiudi")])])]),_:1})])])]),_:1})]),_:1},8,["show"]))}},jp=On(f3,[["__scopeId","data-v-0424fc1a"]]),p3={class:"pt-12 pb-12 relative"},h3={class:"container-small"},v3={class:"grid grid-cols-1 md:grid-cols-2 gap-8"},m3=["href"],g3={class:"bg-white min-h-screen h-full py-16"},y3={class:"container-small"},b3={class:"grid grid-cols-3 gap-5 mb-12"},w3={class:"col-span-3 md:col-span-1"},_3=["src"],S3={class:"grid grid-cols-3 gap-5 mb-12"},$3={class:"col-span-3 md:col-span-2 md:flex md:flex-col md:justify-end"},A3={class:"flex gap-x-3 mb-1"},x3={class:"flex gap-x-3"},E3={class:"col-span-3 md:col-span-1 hidden md:flex md:flex-col md:justify-end"},C3=["src"],T3={class:"bg-[#e4f3ed] py-16"},O3={class:"container-small"},P3={class:"flex justify-center"},R3={class:"flex items-center gap-3"},M3={class:"flex items-center"},k3=["src"],L3={class:"bg-white rounded-full shadow-lg -ml-6 p-4"},I3={class:"text-lg font-bold leading-tight text-[#00624A]"},N3=["onClick"],H3={__name:"Home",setup(e){const t=F(null),n=F([]);r();async function r(){const i=await Yo.getHomeHighlightedMaterials();if(!(i!=null&&i.success))return Pe.error("Errore imprevisto");n.value=i.data}function o(){t.value.openModal()}function s(i){window.open("https://"+window.staticHost+"/uniqum/files/"+i,"_blank")}return(i,l)=>{const a=rn("RouterLink");return x(),T(he,null,[M(jp,{ref_key:"launchVideoModal",ref:t,cover:"/assets/dashboard/uniqum-cover-video.jpg",source:"/assets/dashboard/Groupama_Uniqum_1080.mp4"},null,512),h("div",p3,[h("div",h3,[l[2]||(l[2]=h("div",{class:"text-5xl md:text-7xl font-light text-white text-center mb-8"},"L'evoluzione continua.",-1)),h("div",v3,[h("a",{class:"big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto",href:"https://"+i.window.staticHost+"/assets/dashboard/Uniqum-report.pdf",target:"_blank"},[h("div",null,[M(R(Il),{size:"50",color:"#ffffff"})]),l[0]||(l[0]=h("div",{class:"text-xl font-light"},"Scarica il report della prima tornata di aule",-1))],8,m3),h("div",{class:"big-pill flex gap-8 items-center justify-center cursor-pointer text-white rounded-full mx-auto",onClick:o},[h("div",null,[M(R(Nr),{size:"50",color:"#ffffff"})]),l[1]||(l[1]=h("div",{class:"text-xl font-light"},"Guarda il video con tutte le novità",-1))])])])]),h("div",g3,[h("div",y3,[h("div",b3,[h("div",w3,[h("img",{class:"mx-auto",src:"https://"+i.window.staticHost+"/themes/uniqum/tizia-farfalla.png",alt:""},null,8,_3)]),l[3]||(l[3]=Al('<div class="col-span-3 md:col-span-2" data-v-a5442b18><div class="text-2xl text-[#00624A] font-bold mb-3 leading-tight" data-v-a5442b18>Uniqum si impegna a valorizzare al meglio ogni risorsa a disposizione, con una visione che si fonda su due pilastri essenziali.</div><div class="text-lg text-[#3E4154] mb-3 leading-tight" data-v-a5442b18><span class="font-bold" data-v-a5442b18>Innovazione continua:</span><br data-v-a5442b18>arricchire costantemente il sito con nuovi contenuti e attività.</div><div class="text-lg text-[#3E4154] mb-3 leading-tight" data-v-a5442b18><span class="font-bold" data-v-a5442b18>Collaborazione totale:</span><br data-v-a5442b18>coinvolgere attivamente tutti gli attori, dalla Compagnia agli Agenti, dai Dipendenti ai Collaboratori di Agenzia.</div></div>',1))]),h("div",S3,[h("div",$3,[l[6]||(l[6]=h("div",{class:"text-2xl text-[#00624A] font-bold mb-3 leading-tight"},"Uniqum si evolve con una nuova sezione dedicata agli Approfondimenti.",-1)),h("div",A3,[M(R(wy),{class:"flex-shrink-0",size:"38",color:"#7ec5ab"}),l[4]||(l[4]=h("div",{class:"text-lg text-[#3E4154] mb-3 leading-tight"},[h("span",{class:"font-bold"},"Contenuti mirati:"),h("br"),oe("tematiche commerciali, di prodotto e operative.")],-1))]),h("div",x3,[M(R(Nr),{class:"flex-shrink-0",size:"38",color:"#7ec5ab"}),l[5]||(l[5]=h("div",{class:"text-lg text-[#3E4154] mb-3 leading-tight"},[h("span",{class:"font-bold"},"Formato innovativo:"),h("br"),oe("pillole informative in video per catturare l’attenzione e stimolare la curiosità.")],-1))]),l[7]||(l[7]=h("div",{class:"text-lg"},[h("span",{class:"text-[#E94E1D] font-bold"},"Esplora questa sezione e condividi contenuti con i tuoi dipendenti."),oe(" Ogni video sarà un’opportunità per confrontarsi e crescere insieme.")],-1))]),h("div",E3,[h("img",{class:"hidden md:block",src:"https://"+i.window.staticHost+"/themes/uniqum/fumetto.png",alt:""},null,8,C3),l[8]||(l[8]=h("div",{class:"bg-[#C7E6DA] text-xl text-semibold text-center text-[#3E4154] py-2.5 px-14 rounded-xl",style:{"border-radius":"50px"}},"Approfondimenti",-1))])]),l[9]||(l[9]=h("div",{class:"text-3xl text-[#00624A] font-bold text-center"}," La strada è pronta, sta a noi percorrerla insieme per collaborare, crescere, eccellere! ",-1))])]),h("div",T3,[h("div",O3,[l[10]||(l[10]=h("div",{class:"text-3xl text-[#00624A] font-bold text-center mb-12"},"In evidenza",-1)),h("div",P3,[(x(!0),T(he,null,yt(n.value,u=>{var c;return x(),T("div",R3,[h("div",M3,[h("img",{src:"https://"+i.window.staticHost+"/uniqum/covers/"+u.cover,alt:"",class:"max-w-32 rounded-full"},null,8,k3),h("div",L3,[u.type==="PPT"?(x(),tt(R(F0),{key:0,size:"34"})):ce("",!0),u.type==="PDF"?(x(),tt(R(Il),{key:1,size:"34"})):ce("",!0),u.type==="VID"?(x(),tt(R(Nr),{key:2,size:"34"})):ce("",!0)])]),h("div",null,[h("div",I3,pe(u.title),1),u.fileName?(x(),T("div",{key:0,class:"text-[#3E4154] underline cursor-pointer",onClick:f=>s(u.fileName)},pe(u.data.linkString),9,N3)):ce("",!0),(c=u.data)!=null&&c.internalLink?(x(),tt(a,{key:1,to:{name:u.data.internalLink},class:"text-[#3E4154] underline cursor-pointer"},{default:X(()=>[oe(pe(u.data.linkString),1)]),_:2},1032,["to"])):ce("",!0)])])}),256))])])])],64)}}},F3=On(H3,[["__scopeId","data-v-a5442b18"]]);async function V3(){var t;const e=(t=await Ce.get("/apps/uniqum/dashboard/active-courses"))==null?void 0:t.data;return e!=null&&e.success?e:[]}async function D3(e){var n;const t=(n=await Ce.get(`/apps/formazione/course/view/${e}`))==null?void 0:n.data;return t!=null&&t.success?t:[]}async function B3(e){var n;const t=(n=await Ce.get(`/apps/formazione/booking/course/${e}/available`))==null?void 0:n.data;return t!=null&&t.success?t:[]}async function j3(e){var n;const t=(n=await Ce.get(`/apps/formazione/booking/classroom/${e}`))==null?void 0:n.data;return t!=null&&t.success?t:[]}async function z3(e){var n;const t=(n=await Ce.get(`/apps/formazione/booking/course/${e}/recipients`))==null?void 0:n.data;return t!=null&&t.success?t:[]}const As={getActiveCourses:V3,getCourse:D3,getAvailableClassrooms:B3,getClassroomData:j3,getClassroomBookings:z3},U3={class:"container-small"},Z3={class:"grid grid-cols-5 gap-6"},q3={key:0,class:"col-span-5"},W3={class:"col-span-5 md:col-span-3"},G3={class:"grid grid-cols-2 gap-3 h-full"},K3={key:0,class:"course-card col-span-2 md:col-span-2"},Y3={class:"course-card col-span-2 md:col-span-2"},J3=["alt","src"],X3={class:"card-body"},Q3={class:"font-bold leading-tight text-xl"},eS={class:"flex gap-x-3 items-center mb-5"},tS={class:"overflow-hidden rounded-lg shadow-lg ring-1 ring-black/5 p-3 bg-slate-50"},nS={class:"table-auto"},rS={class:"border-b"},oS={class:"capitalize"},sS={class:"text-center"},iS={class:"text-center"},lS={class:"text-center"},aS={class:"text-sm mr-1 capitalize"},uS={key:0},cS={class:"flex justify-between items-center"},dS={class:"text-lg font-bold"},fS={class:"text-xs"},pS={__name:"ActiveCourses",setup(e){const t=F([]);let n=F(!1);r();async function r(){n.value=!0;const o=await As.getActiveCourses();if(n.value=!1,!(o!=null&&o.success)){Pe.error("Errore durante il caricamento dei corsi.");return}t.value=o.data}return(o,s)=>{const i=rn("RouterLink");return x(),T("div",U3,[h("div",Z3,[!t.value.length&&!R(n)?(x(),T("div",q3,s[0]||(s[0]=[h("h1",{class:"text-xl font-semibold"},"Non sono attualmente presenti corsi attivi.",-1)]))):ce("",!0),s[5]||(s[5]=Al('<div class="col-span-5 md:col-span-2" data-v-b8dbf4de><div class="course-card desc h-full" data-v-b8dbf4de><div class="card-body" data-v-b8dbf4de><p class="mb-3" data-v-b8dbf4de>In questa sezione troverai i corsi disponibili per i dipendenti del tuo team. Effettua subito la prenotazione!</p><p data-v-b8dbf4de>Ricorda che:</p><ul class="list-disc pl-6" data-v-b8dbf4de><li data-v-b8dbf4de>Per l’ottenimento dei crediti formativi è necessario partecipare ad entrambe le date del corso e superare il test finale</li><li data-v-b8dbf4de>Chi non partecipa alla prima data, non potrà partecipare alla seconda data.</li><li data-v-b8dbf4de>La prenotazione non garantisce la partecipazione dei tuoi dipendenti al corso. Dovrai attendere la conferma dell’avvenuta iscrizione. Riceverai una mail di alert, ma ti consigliamo di tornare sul portale per verificare lo stato delle prenotazioni. </li><li data-v-b8dbf4de>Groupama non invierà comunicazioni ai tuoi dipendenti per cui assicurati di avvertirli per tempo comunicandogli le date e le sedi di svolgimento del corso. </li></ul></div></div></div>',1)),h("div",W3,[h("div",G3,[R(n)?(x(),T("div",K3,s[1]||(s[1]=[Al('<div class="animate-pulse" data-v-b8dbf4de><div class="bg-slate-200 rounded-t-xl w-full" style="min-height:220px;" data-v-b8dbf4de></div><div class="card-body" data-v-b8dbf4de><div class="h-4 bg-slate-200 rounded mb-5" data-v-b8dbf4de></div><div class="h-2 bg-slate-200 rounded mb-1" data-v-b8dbf4de></div><div class="h-2 bg-slate-200 rounded mb-5" data-v-b8dbf4de></div><div class="grid grid-cols-2 gap-3" data-v-b8dbf4de><div class="h-3 bg-slate-200 rounded" data-v-b8dbf4de></div><div class="h-3 bg-slate-200 rounded" data-v-b8dbf4de></div></div></div></div>',1)]))):ce("",!0),t.value.length&&!R(n)?(x(!0),T(he,{key:1},yt(t.value,l=>(x(),T("div",Y3,[h("img",{class:"rounded-t-xl w-full",alt:l.title,src:"https://"+o.window.staticHost+"/assets/apps/formazione/covers/"+l.cover},null,8,J3),h("div",X3,[h("div",Q3,pe(l.title)+": "+pe(l.subtitle),1),h("div",eS,[h("div",null,[M(R(T0),{class:"relative"},{default:X(()=>[M(R(G2),{class:"btn-calendar",type:"button"},{default:X(()=>[M(R(t5),{class:"size-5 text-red-500"})]),_:1}),M(ri,{"enter-active-class":"transition duration-200 ease-out","enter-from-class":"translate-y-1 opacity-0","enter-to-class":"translate-y-0 opacity-100","leave-active-class":"transition duration-150 ease-in","leave-from-class":"translate-y-0 opacity-100","leave-to-class":"translate-y-1 opacity-0"},{default:X(()=>[M(R(K2),{class:"absolute z-10 mt-3 w-screen max-w-max transform"},{default:X(()=>[h("div",tS,[h("table",nS,[s[2]||(s[2]=h("thead",null,[h("tr",null,[h("th",{class:"font-bold"},"Sede"),h("th",{class:"font-bold text-center"},[oe("Il valore"),h("br"),oe("del ruolo")]),h("th",{class:"font-bold text-center"},[oe("Creare"),h("br"),oe("opportunità")]),h("th",{class:"font-bold text-center"},"Posti")])],-1)),h("tbody",null,[(x(!0),T(he,null,yt(l.locations,a=>(x(),T("tr",rS,[h("td",oS,pe(a.city.toLowerCase()),1),h("td",sS,pe(a.days[0]),1),h("td",iS,pe(a.days[1]),1),h("td",lS,pe(a.vacantSeats)+"/"+pe(a.seats),1)]))),256))])])])]),_:2},1024)]),_:2},1024)]),_:2},1024)]),h("div",null,[(x(!0),T(he,null,yt(l.locations,(a,u)=>(x(),T("span",aS,[oe(pe(a.city.toLowerCase())+" ",1),u+1<l.locations.length?(x(),T("span",uS,"|")):ce("",!0)]))),256))])]),h("div",cS,[h("div",null,[h("div",null,[h("span",dS,pe(l.remainingSeats),1),h("span",fS,"/"+pe(l.totalSeats),1)]),s[3]||(s[3]=h("div",{class:"text-xs",style:{"margin-top":"-5px"}},"posti disponibili",-1))]),h("div",null,[M(i,{to:{name:"course-detail",params:{id:l.course_id}},class:"btn btn-outline-red"},{default:X(()=>s[4]||(s[4]=[oe("Prenotazione")])),_:2},1032,["to"])])])])]))),256)):ce("",!0)])])])])}}},hS=On(pS,[["__scopeId","data-v-b8dbf4de"]]);async function vS(e){var n;const t=(n=await Ce.post("/apps/formazione/booking/book",e))==null?void 0:n.data;return t!=null&&t.success,t}async function mS(){var t;const e=(t=await Ce.get("/apps/formazione/booking/uniqum"))==null?void 0:t.data;return e!=null&&e.success?e:[]}const ta={bookUser:vS,currentBookings:mS},gS={class:"relative z-10"},yS={class:"fixed inset-0 overflow-y-auto backdrop-blur-sm"},bS={class:"flex min-h-full items-center justify-center p-4 text-center"},wS={class:"card transition-all overflow-hidden max-w-md w-full"},_S={class:"card-body"},SS={class:"flex justify-center"},$S={class:"text-center text-4xl font-bold mb-3"},AS={class:"card-body mt-5"},xS={class:"text-right"},ES={__name:"ModalDialog",props:{message:String,title:{type:String,default:"Attenzione"},showConfirmButton:{type:Boolean,default:!0},showCancelButton:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Conferma"},cancelButtonText:{type:String,default:"Chiudi"}},emits:["confirm","cancel"],setup(e,{emit:t}){const n=t;return(r,o)=>(x(),T("div",gS,[h("div",yS,[h("div",bS,[h("div",wS,[h("div",_S,[h("div",SS,[M(R(n5),{class:"size-8"})]),h("div",$S,pe(e.title),1),h("div",null,pe(e.message),1)]),h("div",AS,[h("div",xS,[e.showCancelButton?(x(),T("button",{key:0,type:"button",class:"btn btn-secondary mr-2",onClick:o[0]||(o[0]=s=>n("cancel"))},pe(e.cancelButtonText),1)):ce("",!0),e.showConfirmButton?(x(),T("button",{key:1,class:"btn btn-red",onClick:o[1]||(o[1]=s=>n("confirm"))},pe(e.confirmButtonText),1)):ce("",!0)])])])])])]))}},CS={class:"container-small pb-32"},TS={class:"text-3xl font-bold",style:{color:"#00624A"}},OS={class:"bg-slate-100 min-h-screen h-full py-16"},PS={class:"container-small"},RS={class:"card",style:{"margin-top":"-180px"}},MS={class:"card-header border-b mb-6"},kS={class:"card-body"},LS={class:"grid grid-cols-10 gap-5"},IS={class:"col-span-10 md:col-span-4"},NS=["alt","src"],HS={class:"text-center"},FS={class:"col-span-10 md:col-span-6"},VS={class:"mb-3"},DS={class:"bg-slate-100 rounded-xl mb-6"},BS=["onClick"],jS={class:"font-medium text-lg"},zS={key:0,class:"mr-1"},US={key:0},ZS={key:1,class:"btn-pill compact uppercase text-sm"},qS={key:0,class:"border-t p-5 transition-all"},WS={key:0,class:"text-center"},GS={key:1,class:"grid grid-cols-10 gap-5"},KS={class:"col-span-10 sm:col-span-3"},YS={class:"mb-5 leading-tight"},JS={class:"mb-5 leading-tight"},XS={class:"mb-5 leading-tight"},QS={class:"mb-5 leading-tight"},e$={class:"col-span-10 sm:col-span-7"},t$={class:"grid grid-cols-10 items-center px-1 py-3 border-b"},n$={class:"col-span-10 sm:col-span-8 font-bold text-center sm:text-left"},r$={class:"col-span-10 sm:col-span-2 text-center"},o$=["onClick","disabled"],s$={key:0,class:"loader-mini"},i$={key:1,class:"font-bold text-sm"},l$={key:2,class:"font-bold text-sm"},a$={key:3,class:"font-bold text-sm"},u$={__name:"CourseDetail",setup(e){const n=ka().params.id,r=F({}),o=F([]),s=F(0);let i=F([]),l=F(!1);const a=Ap(ES,null,{chore:!0,keepInitial:!0});u();async function u(){const g=await As.getCourse(n);if(!(g!=null&&g.success)){Pe.error("Errore imprevisto.");return}r.value=g.data;const b=await As.getAvailableClassrooms(n);if(!(b!=null&&b.success)){Pe.error("Errore imprevisto.");return}o.value=b.data.classrooms}async function c(g,b){if(o.value[b].isOpen){o.value[b].isOpen=!1;return}l.value=!0,o.value.forEach(S=>{S.isOpen=!1}),o.value[b].isOpen=!0;const y=await As.getClassroomBookings(g);setTimeout(()=>{if(l.value=!1,!(y!=null&&y.success)){Pe.error("Errore imprevisto.");return}i.value=y.data},500)}const f=async(g,b,y,S)=>{if(!await p(g,y))return;i.value[S].isLoading=!0;let $={classroomId:y.id,userId:g.id,action:y.vacantSeats>0?"booked":"queued"};const I=await ta.bookUser($);d(I,g,b,y,S,$)};async function d(g,b,y,S,A,$){if(i.value[A].isLoading=!1,(g==null?void 0:g.errorCode)===118){if(S.vacantSeats=0,!await p(b,S))return;g=await ta.bookUser($)}if(!(g!=null&&g.success)){Pe.error("Errore imprevisto.");return}i.value=g.bookingStatus,o.value[y]=g.classroom,o.value[y].isOpen=!0,v(i.value[A])}async function p(g,b){let y="";b.vacantSeats<1?y=`I posti nell'aula di ${b.city} del ${b.startDate} sono esauriti, proseguendo con la richiesta di prenotazione verrai messo in lista d'attesa (sarai il numero ${b.queuedUsers+1}). Vuoi comunque proseguire con la richiesta di prenotazione?`:y=`Sei sicuro di voler prenotare un posto per ${g.name} ${g.surname} nell'aula di ${b.city} del ${b.startDate}?`;const{data:S,isCanceled:A}=await a.reveal({message:y});return!A}async function v(g){let b="";switch(g.state){case"booked":b="La tua richiesta di prenotazione è stata presa in carico. Riceverai una email di conferma iscrizione a seguito di validazione da parte della Compagnia.";break;case"queued":b="La tua prenotazione in lista d'attesa è stata registrata. Riceverai una email di notifica qualora dovesse liberarsi un posto.";break}const{data:y,isCanceled:S}=await a.reveal({message:b,showConfirmButton:!1,cancelButtonText:"Ho capito"})}function m(g){var b=new Date,y=new Date(g.signupStartDate.split("/").reverse().join("-")),S=new Date(g.signupEndDate.split("/").reverse().join("-"));if(b>=y&&b<=S)return"active";if(b>S)return"expired";if(b<y)return"not started"}return(g,b)=>(x(),T(he,null,[h("div",CS,[h("div",TS,pe(r.value.title),1)]),h("div",OS,[h("div",PS,[h("div",RS,[M(R(k0),{defaultIndex:0,selectedIndex:s.value},{default:X(()=>[h("div",MS,[M(R(L0),{class:"flex gap-x-8 pb-5"},{default:X(()=>[M(R(Or),{class:"ui-selected:font-bold",onClick:b[0]||(b[0]=y=>s.value=0)},{default:X(()=>b[3]||(b[3]=[oe("Info generali")])),_:1}),M(R(Or),{class:"ui-selected:font-bold",onClick:b[1]||(b[1]=y=>s.value=1)},{default:X(()=>b[4]||(b[4]=[oe("Prenotazione")])),_:1})]),_:1})]),h("div",kS,[M(R(I0),null,{default:X(()=>[M(R(Pr),null,{default:X(()=>{var y;return[h("div",LS,[h("div",IS,[h("img",{class:"rounded-lg w-full mb-5",alt:r.value.title,src:"https://"+g.window.staticHost+"/assets/apps/formazione/covers/"+r.value.cover},null,8,NS),h("div",HS,[h("button",{class:"btn btn-red",onClick:b[2]||(b[2]=S=>s.value=1)},"Prenota")])]),h("div",FS,[b[5]||(b[5]=h("div",{class:"text-lg font-bold"},"Tema del corso",-1)),h("p",VS,pe((y=r.value.data)==null?void 0:y.description),1),b[6]||(b[6]=h("div",{class:"text-lg font-bold"},"Programma 1a giornata",-1)),b[7]||(b[7]=h("div",null,[oe("09:30 -> 10:00: "),h("span",{class:"font-semibold"},"welcome caffè")],-1)),b[8]||(b[8]=h("div",null,[oe("10:00 -> 13:00: "),h("span",{class:"font-semibold"},"lavori prima parte")],-1)),b[9]||(b[9]=h("div",null,[oe("13:00 -> 14:00: "),h("span",{class:"font-semibold"},"pranzo")],-1)),b[10]||(b[10]=h("div",null,[oe("14:00 -> 16:30: "),h("span",{class:"font-semibold"},"lavori seconda parte")],-1)),b[11]||(b[11]=h("div",{class:"mb-3"},[oe("16:30 -> 17:00: "),h("span",{class:"font-semibold"},"chiusura lavori")],-1)),b[12]||(b[12]=h("div",{class:"text-lg font-bold"},"Programma 2a giornata",-1)),b[13]||(b[13]=h("div",null,[oe("09:30 -> 10:00: "),h("span",{class:"font-semibold"},"welcome caffè")],-1)),b[14]||(b[14]=h("div",null,[oe("10:00 -> 13:00: "),h("span",{class:"font-semibold"},"lavori prima parte")],-1)),b[15]||(b[15]=h("div",null,[oe("13:00 -> 14:00: "),h("span",{class:"font-semibold"},"pranzo")],-1)),b[16]||(b[16]=h("div",null,[oe("14:00 -> 17:00: "),h("span",{class:"font-semibold"},"lavori seconda parte")],-1)),b[17]||(b[17]=h("div",{class:"mb-3"},[oe("17:00 -> 17:30: "),h("span",{class:"font-semibold"},"erogazione test e consegna attestato")],-1))])])]}),_:1}),M(R(Pr),null,{default:X(()=>[(x(!0),T(he,null,yt(o.value,(y,S)=>(x(),T("div",DS,[h("div",{class:"sm:flex items-center justify-between px-5 py-6 cursor-pointer",onClick:A=>c(y.id,S)},[h("div",jS,[oe(pe(y.city)+" ",1),b[18]||(b[18]=h("br",null,null,-1)),(x(!0),T(he,null,yt(y.days,(A,$)=>(x(),T("span",null,[oe(pe(A)+" ",1),$+1<y.days.length?(x(),T("span",zS,"e")):ce("",!0)]))),256))]),h("div",null,[m(y)==="active"?(x(),T("span",US,pe(y.vacantSeats)+" di "+pe(y.totalSeats),1)):ce("",!0),m(y)==="expired"||m(y)==="not started"?(x(),T("span",ZS,"prenotazioni chiuse")):ce("",!0),M(R(Za),{class:ot(["size-9 inline transition-all",y.isOpen&&"rotate-180"])},null,8,["class"])])],8,BS),y.isOpen?(x(),T("div",qS,[R(l)?(x(),T("div",WS,b[19]||(b[19]=[h("span",{class:"loader"},null,-1)]))):ce("",!0),R(l)?ce("",!0):(x(),T("div",GS,[h("div",KS,[h("div",YS,[b[20]||(b[20]=h("div",{class:"font-bold"},"Orario:",-1)),h("div",null,pe(y.startHour),1)]),h("div",JS,[b[21]||(b[21]=h("div",{class:"font-bold"},"Luogo:",-1)),h("div",null,pe(y.location),1),h("div",null,pe(y.address),1)]),h("div",XS,[b[22]||(b[22]=h("div",{class:"font-bold"},"Apertura iscrizioni:",-1)),h("div",null,pe(y.signupStartDate),1)]),h("div",QS,[b[23]||(b[23]=h("div",{class:"font-bold"},"Chiusura iscrizioni:",-1)),h("div",null,pe(y.signupEndDate),1)])]),h("div",e$,[(x(!0),T(he,null,yt(R(i),(A,$)=>(x(),T("div",t$,[h("div",n$,pe(A.name)+" "+pe(A.surname),1),h("div",r$,[!A.state&&m(y)==="active"?(x(),T("button",{key:0,type:"button",class:"btn btn-red compact uppercase text-xs flex items-center gap-1",onClick:I=>f(A,S,y,$),disabled:A.isLoading},[A.isLoading?(x(),T("span",s$)):ce("",!0),b[24]||(b[24]=oe(" Prenota"))],8,o$)):ce("",!0),A.state==="signedup"?(x(),T("span",i$,"Iscritto")):ce("",!0),A.state==="booked"?(x(),T("span",l$,"Prenotato")):ce("",!0),A.state==="queued"?(x(),T("span",a$,"In lista d'attesa")):ce("",!0)])]))),256))])]))])):ce("",!0)]))),256))]),_:1})]),_:1})])]),_:1},8,["selectedIndex"])])])])],64))}},c$=On(u$,[["__scopeId","data-v-ddba0de3"]]);function d$(e,t=null){const n=new Date(e);return t||(t={day:"2-digit",month:"2-digit",year:"numeric"}),new Intl.DateTimeFormat("it-IT",t).format(n)}const f$={class:"container-small"},p$={class:"card mb-5"},h$={class:"grid grid-cols-10 gap-x-2 mb-3"},v$={class:"col-span-8 flex items-center gap-x-2"},m$=["alt","src"],g$={class:"text-xl font-bold"},y$={class:"text-sm",style:{color:"#4E4E4E"}},b$={class:"mr-1"},w$={key:0},_$={class:"col-span-2 flex items-center"},S$={class:"card grid grid-cols-12 items-center mb-3"},$$={class:"col-span-10"},A$={class:"font-bold",style:{color:"#3E4154"}},x$={class:"col-span-2"},E$={class:"text-center font-bold"},C$={key:0,class:"py-6"},T$={__name:"Bookings",setup(e){const t=F([]);n();async function n(){const o=await ta.currentBookings();if(!(o!=null&&o.success)){Pe.error("Errore durante il caricamento dei corsi.");return}t.value=o.data}function r(o){if(o.result==="ko")return"No show";let s="";switch(o.state){case"signedup":s="Iscritto";break;case"booked":s="Prenotato";break;case"queued":s="In lista d'attesa";break}return s}return(o,s)=>{const i=rn("RouterLink");return x(),T(he,null,[s[2]||(s[2]=h("div",{class:"container-small pb-32"},[h("div",{class:"text-3xl font-bold",style:{color:"#00624A"}},"Le tue prenotazioni")],-1)),h("div",f$,[(x(!0),T(he,null,yt(t.value,l=>(x(),T("div",p$,[(x(!0),T(he,null,yt(l.classrooms,(a,u)=>(x(),T(he,null,[h("div",h$,[h("div",v$,[h("div",null,[h("img",{class:"rounded max-h-20",alt:l.title,src:"https://"+o.window.staticHost+"/assets/apps/formazione/covers/"+l.cover},null,8,m$)]),h("div",null,[h("div",g$,pe(l.title),1),h("div",y$,[oe(pe(a.city)+" ",1),(x(!0),T(he,null,yt(a.data.days,(c,f)=>(x(),T("span",b$,[oe(pe(R(d$)(c.date))+" ",1),f+1<a.data.days.length?(x(),T("span",w$,"e")):ce("",!0)]))),256))])])]),h("div",_$,[M(i,{to:{name:"course-detail",params:{id:l.id}},class:"btn btn-outline flex items-center gap-x-2"},{default:X(()=>[M(R(Jm),{class:"size-6"}),s[0]||(s[0]=oe("Prenota "))]),_:2},1032,["to"])])]),(x(!0),T(he,null,yt(a.bookings,c=>(x(),T("div",S$,[h("div",$$,[h("div",A$,pe(c.nome)+" "+pe(c.cognome),1)]),h("div",x$,[h("div",E$,pe(r(c)),1)])]))),256)),u+1!==l.classrooms.length?(x(),T("div",C$,s[1]||(s[1]=[h("hr",{class:"border-0 border-b"},null,-1)]))):ce("",!0)],64))),256))]))),256))])],64)}}},O$={},P$={class:"bg-white min-h-screen h-full py-4"};function R$(e,t){const n=rn("RouterView");return x(),T("div",P$,[M(n)])}const M$=On(O$,[["render",R$]]),k$={class:"fixed inset-0 overflow-y-auto"},L$={class:"flex min-h-full items-center justify-center"},I$={class:"card card-fit max-w-2xl mb-6",style:{"border-radius":"30px"}},N$={class:"embed-container"},H$={controls:""},F$=["src"],V$={class:"text-center text-white cursor-pointer text-lg"},D$={__name:"VideoModal",setup(e,{expose:t}){let n=F(!1),r=F("");function o(i){r.value=i,n.value=!0}function s(){r.value="",n.value=!1}return t({openModal:o}),(i,l)=>(x(),tt(R(Zr),{appear:"",show:R(n),as:"template"},{default:X(()=>[M(R(qo),{as:"div",onClose:s,class:"relative z-10"},{default:X(()=>[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0","enter-to":"opacity-100",leave:"duration-200 ease-in","leave-from":"opacity-100","leave-to":"opacity-0"},{default:X(()=>l[1]||(l[1]=[h("div",{class:"fixed inset-0",style:{"background-color":"rgba(24,27,49,0.9)"}},null,-1)])),_:1}),h("div",k$,[h("div",L$,[M(R(sn),{as:"template",enter:"duration-300 ease-out","enter-from":"opacity-0 scale-95","enter-to":"opacity-100 scale-100",leave:"duration-200 ease-in","leave-from":"opacity-100 scale-100","leave-to":"opacity-0 scale-95"},{default:X(()=>[h("div",null,[h("div",I$,[h("div",N$,[h("video",H$,[h("source",{src:"https://"+i.window.staticHost+"/uniqum/files/"+R(r),type:"video/mp4"},null,8,F$),l[2]||(l[2]=oe(" Questo browser non supporta il tag video. "))])])]),h("div",V$,[h("span",{class:"p-3",onClick:l[0]||(l[0]=()=>s())},"Chiudi")])])]),_:1})])])]),_:1})]),_:1},8,["show"]))}},ru=On(D$,[["__scopeId","data-v-c34359dd"]]),B$={class:"container-small"},j$={class:"block md:hidden mb-5"},z$={class:"px-1 mb-3"},U$={key:0,class:"font-bold text-xl"},Z$={key:1,class:"text-2xl"},q$={class:"rounded-lg relative"},W$={key:0,class:"absolute right-2 -top-3"},G$=["src"],K$=["src"],Y$=["onClick"],J$={class:"absolute left-7 top-7 bg-white px-5 py-4 rounded-lg"},X$={key:0,class:"font-bold text-lg"},Q$={key:1,class:"my-2"},eA={key:2,class:"text-2xl"},tA={key:3,class:"absolute -right-2.5 -top-2.5"},nA=["src"],rA=["onClick"],oA={class:"flex items-center justify-between p-3"},sA=["onClick"],iA={class:"flex items-center gap-5"},lA={class:"flex-1 leading-tight"},aA={class:"font-bold text-[#00624A]"},uA={key:0},cA={key:1,class:"text-[#E94E1D] font-bold py-1.5"},dA={__name:"Section",props:{category:String,sections:{type:Object,required:!0,default:()=>({})}},setup(e){const t=e,n=F(null),r=F(new Set);function o(a){window.open("https://"+window.staticHost+"/uniqum/files/"+a,"_blank")}function s(a){n.value.openModal(a)}function i(a){r.value.has(a)?r.value.delete(a):r.value.add(a)}const l=O(()=>{const a=Object.fromEntries(Object.entries(t.sections).filter(([u,c])=>c&&Object.keys(c).length>0));return Object.fromEntries(Object.entries(a).map(([u,c])=>{if(!Array.isArray(c))return[u,c];const f=[...c].sort((d,p)=>d.data.isMain?-1:p.data.isMain?1:0);return[u,f]}))});return(a,u)=>(x(),T(he,null,[M(ru,{ref_key:"videoModal",ref:n},null,512),(x(!0),T(he,null,yt(l.value,(c,f)=>(x(),T(he,{key:f},[h("div",B$,[h("div",null,[(x(!0),T(he,null,yt(c,d=>{var p,v,m,g,b,y,S,A;return x(),T(he,null,[(p=d==null?void 0:d.data)!=null&&p.isMain?(x(),T(he,{key:0},[h("div",j$,[h("div",z$,[(v=d==null?void 0:d.data)!=null&&v.course?(x(),T("div",U$,pe((m=d==null?void 0:d.data)==null?void 0:m.course),1)):ce("",!0),d.type!=="MISC"?(x(),T("div",Z$,pe(d.title),1)):ce("",!0)]),h("div",q$,[(g=d==null?void 0:d.data)!=null&&g.isNew?(x(),T("div",W$,u[0]||(u[0]=[h("div",{class:"new-sticker flex items-center justify-center"},"NEW",-1)]))):ce("",!0),d.type==="VID"?(x(),tt(R(Nr),{key:1,class:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",size:a.window.innerWidth<768?80:150,color:"#ffffff"},null,8,["size"])):ce("",!0),d.type==="VID"?(x(),T("img",{key:2,src:"https://"+a.window.staticHost+"/uniqum/covers/"+d.coverAlt,alt:""},null,8,G$)):ce("",!0),d.type==="MISC"?(x(),T("img",{key:3,src:"https://"+a.window.staticHost+"/uniqum/covers/"+d.cover,alt:""},null,8,K$)):ce("",!0)])]),h("div",{class:ot(["rounded-lg overflow-hidden mb-5 relative hidden md:block",{"cursor-pointer":d.type==="VID"}]),onClick:$=>d.type==="VID"?s(d.fileName):null},[d.type==="VID"?(x(),tt(R(Nr),{key:0,class:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",size:a.window.innerWidth<768?80:150,color:"#ffffff"},null,8,["size"])):ce("",!0),h("div",J$,[(b=d==null?void 0:d.data)!=null&&b.course?(x(),T("div",X$,pe((y=d==null?void 0:d.data)==null?void 0:y.course),1)):ce("",!0),(S=d==null?void 0:d.data)!=null&&S.course&&d.type!=="MISC"?(x(),T("hr",Q$)):ce("",!0),d.type!=="MISC"?(x(),T("div",eA,pe(d.title),1)):ce("",!0),(A=d==null?void 0:d.data)!=null&&A.isNew?(x(),T("div",tA,u[1]||(u[1]=[h("div",{class:"new-sticker flex items-center justify-center"},"NEW",-1)]))):ce("",!0)]),h("img",{src:"https://"+a.window.staticHost+"/uniqum/covers/"+d.cover,alt:""},null,8,nA)],10,Y$)],64)):ce("",!0)],64)}),256)),h("div",{class:ot(["card mb-5 hover:shadow-xl transition-all duration-300 cursor-pointer",r.value.has(f)?"card-negative hover:shadow-none":"card"]),onClick:d=>i(f)},[h("div",oA,[u[2]||(u[2]=h("span",{class:"font-bold"},"Visualizza i materiali collegati",-1)),M(R(Xg),{class:ot([{"rotate-180":r.value.has(f)},"transition-transform duration-300"]),size:"35",color:r.value.has(f)?"#00624A":"#E94E1D"},null,8,["class","color"])])],10,rA),M(ri,{"enter-active-class":"transition duration-300 ease-out","enter-from-class":"transform opacity-0 -translate-y-4","enter-to-class":"transform opacity-100 translate-y-0","leave-active-class":"transition duration-200 ease-in","leave-from-class":"transform opacity-100 translate-y-0","leave-to-class":"transform opacity-0 -translate-y-4"},{default:X(()=>[uh(h("div",null,[(x(!0),T(he,null,yt(c,d=>{var p,v,m,g;return x(),T(he,null,[(p=d==null?void 0:d.data)!=null&&p.isMain?ce("",!0):(x(),T(he,{key:0},[(v=d==null?void 0:d.data)!=null&&v.isNoCard?(x(),T("div",cA,pe(d.title),1)):(x(),T("div",{key:0,class:"card mb-5 hover:shadow-xl transition-shadow duration-300 cursor-pointer",onClick:b=>o(d.fileName)},[h("div",iA,[h("div",null,[d.type==="PPT"?(x(),tt(R(F0),{key:0,size:"30"})):ce("",!0),d.type==="PDF"?(x(),tt(R(Il),{key:1,size:"30"})):ce("",!0),d.type==="VID"?(x(),tt(R(Nr),{key:2,size:"30"})):ce("",!0)]),h("div",lA,[h("div",aA,pe(d.title),1),(m=d==null?void 0:d.data)!=null&&m.product?(x(),T("div",uA,pe((g=d==null?void 0:d.data)==null?void 0:g.product),1)):ce("",!0)]),h("div",null,[M(R(jy),{size:"26",color:"#E23614"})])])],8,sA))],64))],64)}),256))],512),[[Ev,r.value.has(f)]])]),_:2},1024)])]),u[3]||(u[3]=h("hr",{class:"mt-20 mb-12"},null,-1))],64))),128))],64))}},ou=On(dA,[["__scopeId","data-v-99de7e78"]]),fA={class:"container-small"},pA={class:"text-3xl font-bold flex items-center mb-4"},hA={__name:"Commercial",setup(e){const t=F({});n();async function n(){const r=await Yo.getAll("commercial");if(!(r!=null&&r.success))return Pe.error("Errore imprevisto");t.value=r.data}return(r,o)=>(x(),T(he,null,[h("div",fA,[o[1]||(o[1]=h("div",{class:"text-lg font-medium"},"Approfondimenti",-1)),h("div",pA,[M(R(za),{size:"35",class:"mr-3"}),o[0]||(o[0]=oe(" Ambito commerciale "))]),o[2]||(o[2]=h("div",{class:"mb-8 font-bold"},"- Relazione col cliente - Gestione della comunicazione e del tempo",-1))]),M(ou,{sections:t.value},null,8,["sections"])],64))}},vA={class:"container-small mb-10"},mA={class:"flex flex-col md:flex-row justify-between items-center gap-4 md:gap-6"},gA={class:"container-small"},yA={class:"card mb-8"},bA={class:"flex flex-col md:flex-row gap-6 md:gap-10"},wA={class:"grid grid-cols-3 gap-8 md:gap-4"},_A={class:"col-span-3 md:col-span-1 text-center leading-tight px-5"},SA=["src"],$A={class:"col-span-3 md:col-span-1 text-center leading-tight px-5 md:border-x"},AA=["src"],xA={class:"col-span-3 md:col-span-1 text-center leading-tight px-5"},EA=["src"],CA={__name:"Description",setup(e){const t=F(null);function n(){t.value.openModal()}return(r,o)=>(x(),T(he,null,[M(jp,{ref_key:"launchVideoModal",ref:t,cover:"/assets/dashboard/uniqum-preview.jpg",source:"/assets/dashboard/Uniqum.mp4"},null,512),o[15]||(o[15]=h("div",{class:"container-small mb-14"},[h("div",{class:"text-3xl font-bold text-white"},"Cos'è Uniqum")],-1)),h("div",vA,[h("div",mA,[o[1]||(o[1]=h("div",{class:"claim"},"Libera il potenziale della tua Agenzia",-1)),h("button",{type:"button",class:"btn btn-red gap-2 whitespace-nowrap",style:{display:"flex!important"},onClick:n},[M(R(Ym),{class:"size-6"}),o[0]||(o[0]=oe("Video di presentazione"))])])]),o[16]||(o[16]=h("div",{class:"container-small mb-5",id:"faq"},[h("div",{class:"card mb-8 text-xl md:text-2xl"},[oe(" Per essere competitivi in un mercato sempre più dinamico e complesso è fondamentale "),h("span",{style:{"font-weight":"800"}},"valorizzare al meglio ogni risorsa a nostra disposizione: è con questa visione che nasce il Progetto Uniqum"),oe(", un’iniziativa pensata per sviluppare il potenziale dei tuoi dipendenti in una forza propulsiva che ci porterà verso nuovi traguardi di crescita e successo. ")])],-1)),h("div",gA,[h("div",yA,[M(R(k0),null,{default:X(()=>[M(R(L0),null,{default:X(()=>[h("div",bA,[M(R(Or),{as:"template"},{default:X(({selected:s})=>[h("button",{class:ot(["tab",{active:s}])},"Cos'è Uniqum",2)]),_:1}),M(R(Or),{as:"template"},{default:X(({selected:s})=>[h("button",{class:ot(["tab",{active:s}])},"Il percorso",2)]),_:1}),M(R(Or),{as:"template"},{default:X(({selected:s})=>[h("button",{class:ot(["tab",{active:s}])},"Le modalità",2)]),_:1}),M(R(Or),{as:"template"},{default:X(({selected:s})=>[h("button",{class:ot(["tab",{active:s}])},"Timing",2)]),_:1})])]),_:1}),M(R(I0),{class:"text-lg",style:{padding:"25px 0"}},{default:X(()=>[M(R(Pr),null,{default:X(()=>[o[11]||(o[11]=h("div",{class:"mb-6"},[h("span",{class:"font-extrabold"},"È un progetto di sviluppo commerciale dell’Agenzia, costruito attorno ad un percorso formativo di crescita professionale creato specificamente per il personale dipendente di Agenzia"),oe(". Sono 3 gli obiettivi principali: ")],-1)),h("div",wA,[h("div",_A,[h("img",{class:"mx-auto mb-3",src:"https://"+r.window.staticHost+"/themes/uniqum/icon-plus.svg",alt:"Icon plus"},null,8,SA),o[2]||(o[2]=h("span",{class:"font-black"},"Indirizzare",-1)),o[3]||(o[3]=h("br",null,null,-1)),o[4]||(o[4]=oe("l’efficacia e l’efficienza operativa "))]),h("div",$A,[h("img",{class:"mx-auto mb-3",src:"https://"+r.window.staticHost+"/themes/uniqum/icon-arrow.svg",alt:"Icon arrow"},null,8,AA),o[5]||(o[5]=h("span",{class:"font-black"},"Indirizzare",-1)),o[6]||(o[6]=h("br",null,null,-1)),o[7]||(o[7]=oe("alla gestione commerciale della clientela "))]),h("div",xA,[h("img",{class:"mx-auto mb-3",src:"https://"+r.window.staticHost+"/themes/uniqum/icon-heart.svg",alt:"Icon heart"},null,8,EA),o[8]||(o[8]=h("span",{class:"font-black"},"Fidelizzare",-1)),o[9]||(o[9]=h("br",null,null,-1)),o[10]||(o[10]=oe("nei confronti dell’Agenzia e della Compagnia "))])])]),_:1}),M(R(Pr),null,{default:X(()=>o[12]||(o[12]=[h("div",{class:"mb-6"},"Uniqum offre moduli formativi e informativi dedicati a:",-1),h("ul",{style:{"list-style":"disc","padding-left":"25px"}},[h("li",null,[oe("Gestione commerciale della clientela e "),h("span",{class:"font-extrabold"},"tecniche di vendita")]),h("li",null,[oe("Approfondimenti su "),h("span",{class:"font-extrabold"},"tematiche assuntive"),oe(" e di processo")]),h("li",null,[oe("Affiancamento operativo per l’"),h("span",{class:"font-extrabold"},"utilizzo degli applicativi"),oe(" di Compagnia")])],-1)])),_:1}),M(R(Pr),null,{default:X(()=>o[13]||(o[13]=[h("div",null,[oe("Il progetto adotta un "),h("span",{class:"font-extrabold"},"modello ibrido"),oe(", che combina sessioni in aula e formazione online.")],-1)])),_:1}),M(R(Pr),null,{default:X(()=>o[14]||(o[14]=[h("div",null,[oe("La distribuzione dei contenuti inizierà entro il "),h("span",{class:"font-extrabold"},"primo trimestre del 2025"),oe(".")],-1)])),_:1})]),_:1})]),_:1})])])],64))}},TA=On(CA,[["__scopeId","data-v-66473212"]]),OA={class:"container-small"},PA={class:"text-3xl font-bold flex items-center mb-4"},RA={__name:"Products",setup(e){const t=F({});n();async function n(){const r=await Yo.getAll("products");if(!(r!=null&&r.success))return toast.error("Errore imprevisto");t.value=r.data}return(r,o)=>(x(),T(he,null,[M(ru,{ref:"videoModal"},null,512),h("div",OA,[o[1]||(o[1]=h("div",{class:"text-lg font-medium"},"Approfondimenti",-1)),h("div",PA,[M(R(ja),{size:"35",class:"mr-3"}),o[0]||(o[0]=oe(" Conoscere i prodotti "))]),o[2]||(o[2]=h("div",{class:"mb-8 font-bold"},"- Materiali e suggerimenti per conoscere al meglio i prodotti Groupama Assicurazioni",-1))]),M(ou,{sections:t.value},null,8,["sections"])],64))}},MA={class:"container-small"},kA={class:"text-3xl font-bold flex items-center mb-4"},LA={__name:"Operative",setup(e){const t=F({});n();async function n(){const r=await Yo.getAll("operative");if(!(r!=null&&r.success))return toast.error("Errore imprevisto");t.value=r.data}return(r,o)=>(x(),T(he,null,[M(ru,{ref:"videoModal"},null,512),h("div",MA,[o[1]||(o[1]=h("div",{class:"text-lg font-medium"},"Approfondimenti",-1)),h("div",kA,[M(R(Ua),{size:"35",class:"mr-3"}),o[0]||(o[0]=oe(" Novità operative e di processo "))]),o[2]||(o[2]=h("div",{class:"mb-8 font-bold"},"- Processi e strumenti per migliorare l'attività in Agenzia",-1))]),M(ou,{sections:t.value},null,8,["sections"])],64))}},sd=Wm({linkExactActiveClass:"active",history:Am("/uniqum/#!/"),routes:[{path:"/",name:"home",component:F3,meta:{isHome:!0}},{path:"/team",name:"team",component:s3},{path:"/corsi-attivi",name:"active-courses",component:hS},{path:"/dettaglio/:id",name:"course-detail",component:c$},{path:"/prenotazioni",name:"bookings",component:T$},{path:"/materiali",name:"materials",component:M$,children:[{path:"ambito-commerciale",name:"commercial",component:hA},{path:"conoscere-i-prodotti",name:"products",component:RA},{path:"ambito-operativo-di-processo",name:"operative",component:LA}]},{path:"/cos'è-uniqum",name:"description",component:TA}]});function zp(e){const n=`; ${document.cookie}`.split(`; ${e}=`);if(n.length===2)return n.pop().split(";").shift()}function id(e){return zp(e)&&(document.cookie=`${e}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`),document.cookie}function IA(){const e=zp(mo.app.sessionCookieName);Ce.defaults.baseURL=mo.app.baseUrl,Ce.defaults.headers={"Content-Type":"application/json",SESSION:e,"Access-Control-Allow-Origin":"*","Access-Control-Allow-Headers":"*"},Ce.defaults.xsrfCookieName=mo.app.xsrfCookieName,Ce.defaults.withXSRFToken=!0,Ce.interceptors.response.use(t=>t,t=>{var n;return((n=t==null?void 0:t.response)==null?void 0:n.status)===401&&console.debug("unauthorized"),t})}const Up={init:IA};async function NA(){const e=await Ce.get("/auth/data");return e==null?void 0:e.data}async function HA(){const e=await su.me(),t=Ya();return e&&(e!=null&&e.success)&&t.$patch({isAuthenticated:!0,...e==null?void 0:e.data}),t}async function FA(){await Ce.get("/auth/logout").then(function(e){var t;(t=e==null?void 0:e.data)!=null&&t.success&&(window.location.href="/")}),id(mo.app.sessionCookieName),id(mo.app.xsrfCookieName),Up.init()}async function VA(){var t;const e=await Ce.get("/apps/uniqum/check-enrolled");return(t=e==null?void 0:e.data)==null?void 0:t.enrolled}function DA(e){return!!(e!=null&&e.GHOST)}const su={me:NA,authenticateWithToken:HA,logout:FA,isGhost:DA,checkEnrolled:VA},BA=["team","active-courses","course-detail","bookings"];function jA(e){const t=Ya();e.beforeEach(async(n,r,o)=>{if(!(t!=null&&t.isAuthenticated)){ro();return}if(t.UTYPE!=="AGENTE"&&t.UTYPE!=="AREAMGR"&&t.UTYPE!=="INTERMEDIARIO"){ro();return}if(t.UTYPE==="INTERMEDIARIO")try{if(await su.checkEnrolled()){o();return}else{ro();return}}catch(s){console.error("Error checking permissions:",s),ro();return}t.UTYPE!=="AGENTE"&&BA.includes(n.name)?ro():o()})}function ro(){window.location.href="../"}function zA(){Up.init()}const UA=["top","right","bottom","left"],ld=["start","end"],ad=UA.reduce((e,t)=>e.concat(t,t+"-"+ld[0],t+"-"+ld[1]),[]),ko=Math.min,ir=Math.max,ZA={left:"right",right:"left",bottom:"top",top:"bottom"},qA={start:"end",end:"start"};function na(e,t,n){return ir(e,ko(t,n))}function Ar(e,t){return typeof e=="function"?e(t):e}function yn(e){return e.split("-")[0]}function en(e){return e.split("-")[1]}function Zp(e){return e==="x"?"y":"x"}function iu(e){return e==="y"?"height":"width"}function Sr(e){return["top","bottom"].includes(yn(e))?"y":"x"}function lu(e){return Zp(Sr(e))}function qp(e,t,n){n===void 0&&(n=!1);const r=en(e),o=lu(e),s=iu(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=Vs(i)),[i,Vs(i)]}function WA(e){const t=Vs(e);return[Fs(e),t,Fs(t)]}function Fs(e){return e.replace(/start|end/g,t=>qA[t])}function GA(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function KA(e,t,n,r){const o=en(e);let s=GA(yn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Fs)))),s}function Vs(e){return e.replace(/left|right|bottom|top/g,t=>ZA[t])}function YA(e){return{top:0,right:0,bottom:0,left:0,...e}}function Wp(e){return typeof e!="number"?YA(e):{top:e,right:e,bottom:e,left:e}}function go(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function ud(e,t,n){let{reference:r,floating:o}=e;const s=Sr(t),i=lu(t),l=iu(i),a=yn(t),u=s==="y",c=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[l]/2-o[l]/2;let p;switch(a){case"top":p={x:c,y:r.y-o.height};break;case"bottom":p={x:c,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:f};break;case"left":p={x:r.x-o.width,y:f};break;default:p={x:r.x,y:r.y}}switch(en(t)){case"start":p[i]-=d*(n&&u?-1:1);break;case"end":p[i]+=d*(n&&u?-1:1);break}return p}const JA=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,l=s.filter(Boolean),a=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=ud(u,r,a),d=r,p={},v=0;for(let m=0;m<l.length;m++){const{name:g,fn:b}=l[m],{x:y,y:S,data:A,reset:$}=await b({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:u,platform:i,elements:{reference:e,floating:t}});c=y??c,f=S??f,p={...p,[g]:{...p[g],...A}},$&&v<=50&&(v++,typeof $=="object"&&($.placement&&(d=$.placement),$.rects&&(u=$.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):$.rects),{x:c,y:f}=ud(u,d,a)),m=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function Ei(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=Ar(t,e),v=Wp(p),g=l[d?f==="floating"?"reference":"floating":f],b=go(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(g)))==null||n?g:g.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:u,rootBoundary:c,strategy:a})),y=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,S=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),A=await(s.isElement==null?void 0:s.isElement(S))?await(s.getScale==null?void 0:s.getScale(S))||{x:1,y:1}:{x:1,y:1},$=go(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:S,strategy:a}):y);return{top:(b.top-$.top+v.top)/A.y,bottom:($.bottom-b.bottom+v.bottom)/A.y,left:(b.left-$.left+v.left)/A.x,right:($.right-b.right+v.right)/A.x}}const XA=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:l,middlewareData:a}=t,{element:u,padding:c=0}=Ar(e,t)||{};if(u==null)return{};const f=Wp(c),d={x:n,y:r},p=lu(o),v=iu(p),m=await i.getDimensions(u),g=p==="y",b=g?"top":"left",y=g?"bottom":"right",S=g?"clientHeight":"clientWidth",A=s.reference[v]+s.reference[p]-d[p]-s.floating[v],$=d[p]-s.reference[p],I=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let H=I?I[S]:0;(!H||!await(i.isElement==null?void 0:i.isElement(I)))&&(H=l.floating[S]||s.floating[v]);const U=A/2-$/2,N=H/2-m[v]/2-1,D=ko(f[b],N),se=ko(f[y],N),G=D,le=H-m[v]-se,fe=H/2-m[v]/2+U,Se=na(G,fe,le),re=!a.arrow&&en(o)!=null&&fe!==Se&&s.reference[v]/2-(fe<G?D:se)-m[v]/2<0,K=re?fe<G?fe-G:fe-le:0;return{[p]:d[p]+K,data:{[p]:Se,centerOffset:fe-Se-K,...re&&{alignmentOffset:K}},reset:re}}});function QA(e,t,n){return(e?[...n.filter(o=>en(o)===e),...n.filter(o=>en(o)!==e)]:n.filter(o=>yn(o)===o)).filter(o=>e?en(o)===e||(t?Fs(o)!==o:!1):!0)}const ex=function(e){return e===void 0&&(e={}),{name:"autoPlacement",options:e,async fn(t){var n,r,o;const{rects:s,middlewareData:i,placement:l,platform:a,elements:u}=t,{crossAxis:c=!1,alignment:f,allowedPlacements:d=ad,autoAlignment:p=!0,...v}=Ar(e,t),m=f!==void 0||d===ad?QA(f||null,p,d):d,g=await Ei(t,v),b=((n=i.autoPlacement)==null?void 0:n.index)||0,y=m[b];if(y==null)return{};const S=qp(y,s,await(a.isRTL==null?void 0:a.isRTL(u.floating)));if(l!==y)return{reset:{placement:m[0]}};const A=[g[yn(y)],g[S[0]],g[S[1]]],$=[...((r=i.autoPlacement)==null?void 0:r.overflows)||[],{placement:y,overflows:A}],I=m[b+1];if(I)return{data:{index:b+1,overflows:$},reset:{placement:I}};const H=$.map(D=>{const se=en(D.placement);return[D.placement,se&&c?D.overflows.slice(0,2).reduce((G,le)=>G+le,0):D.overflows[0],D.overflows]}).sort((D,se)=>D[1]-se[1]),N=((o=H.filter(D=>D[2].slice(0,en(D[0])?2:3).every(se=>se<=0))[0])==null?void 0:o[0])||H[0][0];return N!==l?{data:{index:b+1,overflows:$},reset:{placement:N}}:{}}}},tx=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:c=!0,crossAxis:f=!0,fallbackPlacements:d,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:m=!0,...g}=Ar(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const b=yn(o),y=Sr(l),S=yn(l)===l,A=await(a.isRTL==null?void 0:a.isRTL(u.floating)),$=d||(S||!m?[Vs(l)]:WA(l)),I=v!=="none";!d&&I&&$.push(...KA(l,m,v,A));const H=[l,...$],U=await Ei(t,g),N=[];let D=((r=s.flip)==null?void 0:r.overflows)||[];if(c&&N.push(U[b]),f){const fe=qp(o,i,A);N.push(U[fe[0]],U[fe[1]])}if(D=[...D,{placement:o,overflows:N}],!N.every(fe=>fe<=0)){var se,G;const fe=(((se=s.flip)==null?void 0:se.index)||0)+1,Se=H[fe];if(Se)return{data:{index:fe,overflows:D},reset:{placement:Se}};let re=(G=D.filter(K=>K.overflows[0]<=0).sort((K,me)=>K.overflows[1]-me.overflows[1])[0])==null?void 0:G.placement;if(!re)switch(p){case"bestFit":{var le;const K=(le=D.filter(me=>{if(I){const De=Sr(me.placement);return De===y||De==="y"}return!0}).map(me=>[me.placement,me.overflows.filter(De=>De>0).reduce((De,ht)=>De+ht,0)]).sort((me,De)=>me[1]-De[1])[0])==null?void 0:le[0];K&&(re=K);break}case"initialPlacement":re=l;break}if(o!==re)return{reset:{placement:re}}}return{}}}};async function nx(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=yn(n),l=en(n),a=Sr(n)==="y",u=["left","top"].includes(i)?-1:1,c=s&&a?-1:1,f=Ar(t,e);let{mainAxis:d,crossAxis:p,alignmentAxis:v}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof v=="number"&&(p=l==="end"?v*-1:v),a?{x:p*c,y:d*u}:{x:d*u,y:p*c}}const rx=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:l}=t,a=await nx(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:s+a.y,data:{...a,placement:i}}}}},ox=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:l={fn:g=>{let{x:b,y}=g;return{x:b,y}}},...a}=Ar(e,t),u={x:n,y:r},c=await Ei(t,a),f=Sr(yn(o)),d=Zp(f);let p=u[d],v=u[f];if(s){const g=d==="y"?"top":"left",b=d==="y"?"bottom":"right",y=p+c[g],S=p-c[b];p=na(y,p,S)}if(i){const g=f==="y"?"top":"left",b=f==="y"?"bottom":"right",y=v+c[g],S=v-c[b];v=na(y,v,S)}const m=l.fn({...t,[d]:p,[f]:v});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:s,[f]:i}}}}}},sx=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:l}=t,{apply:a=()=>{},...u}=Ar(e,t),c=await Ei(t,u),f=yn(o),d=en(o),p=Sr(o)==="y",{width:v,height:m}=s.floating;let g,b;f==="top"||f==="bottom"?(g=f,b=d===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(b=f,g=d==="end"?"top":"bottom");const y=m-c.top-c.bottom,S=v-c.left-c.right,A=ko(m-c[g],y),$=ko(v-c[b],S),I=!t.middlewareData.shift;let H=A,U=$;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(U=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(H=y),I&&!d){const D=ir(c.left,0),se=ir(c.right,0),G=ir(c.top,0),le=ir(c.bottom,0);p?U=v-2*(D!==0||se!==0?D+se:ir(c.left,c.right)):H=m-2*(G!==0||le!==0?G+le:ir(c.top,c.bottom))}await a({...t,availableWidth:U,availableHeight:H});const N=await i.getDimensions(l.floating);return v!==N.width||m!==N.height?{reset:{rects:!0}}:{}}}};function Gt(e){var t;return((t=e.ownerDocument)==null?void 0:t.defaultView)||window}function pn(e){return Gt(e).getComputedStyle(e)}const cd=Math.min,yo=Math.max,Ds=Math.round;function Gp(e){const t=pn(e);let n=parseFloat(t.width),r=parseFloat(t.height);const o=e.offsetWidth,s=e.offsetHeight,i=Ds(n)!==o||Ds(r)!==s;return i&&(n=o,r=s),{width:n,height:r,fallback:i}}function Wn(e){return Yp(e)?(e.nodeName||"").toLowerCase():""}let fs;function Kp(){if(fs)return fs;const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?(fs=e.brands.map(t=>t.brand+"/"+t.version).join(" "),fs):navigator.userAgent}function hn(e){return e instanceof Gt(e).HTMLElement}function Un(e){return e instanceof Gt(e).Element}function Yp(e){return e instanceof Gt(e).Node}function dd(e){return typeof ShadowRoot>"u"?!1:e instanceof Gt(e).ShadowRoot||e instanceof ShadowRoot}function Ci(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=pn(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ix(e){return["table","td","th"].includes(Wn(e))}function ra(e){const t=/firefox/i.test(Kp()),n=pn(e),r=n.backdropFilter||n.WebkitBackdropFilter;return n.transform!=="none"||n.perspective!=="none"||!!r&&r!=="none"||t&&n.willChange==="filter"||t&&!!n.filter&&n.filter!=="none"||["transform","perspective"].some(o=>n.willChange.includes(o))||["paint","layout","strict","content"].some(o=>{const s=n.contain;return s!=null&&s.includes(o)})}function Jp(){return!/^((?!chrome|android).)*safari/i.test(Kp())}function au(e){return["html","body","#document"].includes(Wn(e))}function Xp(e){return Un(e)?e:e.contextElement}const Qp={x:1,y:1};function Vr(e){const t=Xp(e);if(!hn(t))return Qp;const n=t.getBoundingClientRect(),{width:r,height:o,fallback:s}=Gp(t);let i=(s?Ds(n.width):n.width)/r,l=(s?Ds(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),l&&Number.isFinite(l)||(l=1),{x:i,y:l}}function Lo(e,t,n,r){var o,s;t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),l=Xp(e);let a=Qp;t&&(r?Un(r)&&(a=Vr(r)):a=Vr(e));const u=l?Gt(l):window,c=!Jp()&&n;let f=(i.left+(c&&((o=u.visualViewport)==null?void 0:o.offsetLeft)||0))/a.x,d=(i.top+(c&&((s=u.visualViewport)==null?void 0:s.offsetTop)||0))/a.y,p=i.width/a.x,v=i.height/a.y;if(l){const m=Gt(l),g=r&&Un(r)?Gt(r):r;let b=m.frameElement;for(;b&&r&&g!==m;){const y=Vr(b),S=b.getBoundingClientRect(),A=getComputedStyle(b);S.x+=(b.clientLeft+parseFloat(A.paddingLeft))*y.x,S.y+=(b.clientTop+parseFloat(A.paddingTop))*y.y,f*=y.x,d*=y.y,p*=y.x,v*=y.y,f+=S.x,d+=S.y,b=Gt(b).frameElement}}return{width:p,height:v,top:d,right:f+p,bottom:d+v,left:f,x:f,y:d}}function Zn(e){return((Yp(e)?e.ownerDocument:e.document)||window.document).documentElement}function Ti(e){return Un(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function e1(e){return Lo(Zn(e)).left+Ti(e).scrollLeft}function Io(e){if(Wn(e)==="html")return e;const t=e.assignedSlot||e.parentNode||dd(e)&&e.host||Zn(e);return dd(t)?t.host:t}function t1(e){const t=Io(e);return au(t)?t.ownerDocument.body:hn(t)&&Ci(t)?t:t1(t)}function Bs(e,t){var n;t===void 0&&(t=[]);const r=t1(e),o=r===((n=e.ownerDocument)==null?void 0:n.body),s=Gt(r);return o?t.concat(s,s.visualViewport||[],Ci(r)?r:[]):t.concat(r,Bs(r))}function fd(e,t,n){return t==="viewport"?go(function(r,o){const s=Gt(r),i=Zn(r),l=s.visualViewport;let a=i.clientWidth,u=i.clientHeight,c=0,f=0;if(l){a=l.width,u=l.height;const d=Jp();(d||!d&&o==="fixed")&&(c=l.offsetLeft,f=l.offsetTop)}return{width:a,height:u,x:c,y:f}}(e,n)):Un(t)?go(function(r,o){const s=Lo(r,!0,o==="fixed"),i=s.top+r.clientTop,l=s.left+r.clientLeft,a=hn(r)?Vr(r):{x:1,y:1};return{width:r.clientWidth*a.x,height:r.clientHeight*a.y,x:l*a.x,y:i*a.y}}(t,n)):go(function(r){const o=Zn(r),s=Ti(r),i=r.ownerDocument.body,l=yo(o.scrollWidth,o.clientWidth,i.scrollWidth,i.clientWidth),a=yo(o.scrollHeight,o.clientHeight,i.scrollHeight,i.clientHeight);let u=-s.scrollLeft+e1(r);const c=-s.scrollTop;return pn(i).direction==="rtl"&&(u+=yo(o.clientWidth,i.clientWidth)-l),{width:l,height:a,x:u,y:c}}(Zn(e)))}function pd(e){return hn(e)&&pn(e).position!=="fixed"?e.offsetParent:null}function hd(e){const t=Gt(e);let n=pd(e);for(;n&&ix(n)&&pn(n).position==="static";)n=pd(n);return n&&(Wn(n)==="html"||Wn(n)==="body"&&pn(n).position==="static"&&!ra(n))?t:n||function(r){let o=Io(r);for(;hn(o)&&!au(o);){if(ra(o))return o;o=Io(o)}return null}(e)||t}function lx(e,t,n){const r=hn(t),o=Zn(t),s=Lo(e,!0,n==="fixed",t);let i={scrollLeft:0,scrollTop:0};const l={x:0,y:0};if(r||!r&&n!=="fixed")if((Wn(t)!=="body"||Ci(o))&&(i=Ti(t)),hn(t)){const a=Lo(t,!0);l.x=a.x+t.clientLeft,l.y=a.y+t.clientTop}else o&&(l.x=e1(o));return{x:s.left+i.scrollLeft-l.x,y:s.top+i.scrollTop-l.y,width:s.width,height:s.height}}const ax={getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=n==="clippingAncestors"?function(u,c){const f=c.get(u);if(f)return f;let d=Bs(u).filter(g=>Un(g)&&Wn(g)!=="body"),p=null;const v=pn(u).position==="fixed";let m=v?Io(u):u;for(;Un(m)&&!au(m);){const g=pn(m),b=ra(m);(v?b||p:b||g.position!=="static"||!p||!["absolute","fixed"].includes(p.position))?p=g:d=d.filter(y=>y!==m),m=Io(m)}return c.set(u,d),d}(t,this._c):[].concat(n),i=[...s,r],l=i[0],a=i.reduce((u,c)=>{const f=fd(t,c,o);return u.top=yo(f.top,u.top),u.right=cd(f.right,u.right),u.bottom=cd(f.bottom,u.bottom),u.left=yo(f.left,u.left),u},fd(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:r}=e;const o=hn(n),s=Zn(n);if(n===s)return t;let i={scrollLeft:0,scrollTop:0},l={x:1,y:1};const a={x:0,y:0};if((o||!o&&r!=="fixed")&&((Wn(n)!=="body"||Ci(s))&&(i=Ti(n)),hn(n))){const u=Lo(n);l=Vr(n),a.x=u.x+n.clientLeft,a.y=u.y+n.clientTop}return{width:t.width*l.x,height:t.height*l.y,x:t.x*l.x-i.scrollLeft*l.x+a.x,y:t.y*l.y-i.scrollTop*l.y+a.y}},isElement:Un,getDimensions:function(e){return hn(e)?Gp(e):e.getBoundingClientRect()},getOffsetParent:hd,getDocumentElement:Zn,getScale:Vr,async getElementRects(e){let{reference:t,floating:n,strategy:r}=e;const o=this.getOffsetParent||hd,s=this.getDimensions;return{reference:lx(t,await o(n),r),floating:{x:0,y:0,...await s(n)}}},getClientRects:e=>Array.from(e.getClientRects()),isRTL:e=>pn(e).direction==="rtl"},ux=(e,t,n)=>{const r=new Map,o={platform:ax,...n},s={...o.platform,_c:r};return JA(e,t,{...o,platform:s})},yr={disabled:!1,distance:5,skidding:0,container:"body",boundary:void 0,instantMove:!1,disposeTimeout:150,popperTriggers:[],strategy:"absolute",preventOverflow:!0,flip:!0,shift:!0,overflowPadding:0,arrowPadding:0,arrowOverflow:!0,autoHideOnMousedown:!1,themes:{tooltip:{placement:"top",triggers:["hover","focus","touch"],hideTriggers:e=>[...e,"click"],delay:{show:200,hide:0},handleResize:!1,html:!1,loadingContent:"..."},dropdown:{placement:"bottom",triggers:["click"],delay:0,handleResize:!0,autoHide:!0},menu:{$extend:"dropdown",triggers:["hover","focus"],popperTriggers:["hover"],delay:{show:0,hide:400}}}};function No(e,t){let n=yr.themes[e]||{},r;do r=n[t],typeof r>"u"?n.$extend?n=yr.themes[n.$extend]||{}:(n=null,r=yr[t]):n=null;while(n);return r}function cx(e){const t=[e];let n=yr.themes[e]||{};do n.$extend&&!n.$resetCss?(t.push(n.$extend),n=yr.themes[n.$extend]||{}):n=null;while(n);return t.map(r=>`v-popper--theme-${r}`)}function vd(e){const t=[e];let n=yr.themes[e]||{};do n.$extend?(t.push(n.$extend),n=yr.themes[n.$extend]||{}):n=null;while(n);return t}let Ho=!1;if(typeof window<"u"){Ho=!1;try{const e=Object.defineProperty({},"passive",{get(){Ho=!0}});window.addEventListener("test",null,e)}catch{}}let n1=!1;typeof window<"u"&&typeof navigator<"u"&&(n1=/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream);const r1=["auto","top","bottom","left","right"].reduce((e,t)=>e.concat([t,`${t}-start`,`${t}-end`]),[]),md={hover:"mouseenter",focus:"focus",click:"click",touch:"touchstart",pointer:"pointerdown"},gd={hover:"mouseleave",focus:"blur",click:"click",touch:"touchend",pointer:"pointerup"};function yd(e,t){const n=e.indexOf(t);n!==-1&&e.splice(n,1)}function dl(){return new Promise(e=>requestAnimationFrame(()=>{requestAnimationFrame(e)}))}const Jt=[];let or=null;const bd={};function wd(e){let t=bd[e];return t||(t=bd[e]=[]),t}let oa=function(){};typeof window<"u"&&(oa=window.Element);function He(e){return function(t){return No(t.theme,e)}}const fl="__floating-vue__popper",o1=()=>_e({name:"VPopper",provide(){return{[fl]:{parentPopper:this}}},inject:{[fl]:{default:null}},props:{theme:{type:String,required:!0},targetNodes:{type:Function,required:!0},referenceNode:{type:Function,default:null},popperNode:{type:Function,required:!0},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:He("disabled")},positioningDisabled:{type:Boolean,default:He("positioningDisabled")},placement:{type:String,default:He("placement"),validator:e=>r1.includes(e)},delay:{type:[String,Number,Object],default:He("delay")},distance:{type:[Number,String],default:He("distance")},skidding:{type:[Number,String],default:He("skidding")},triggers:{type:Array,default:He("triggers")},showTriggers:{type:[Array,Function],default:He("showTriggers")},hideTriggers:{type:[Array,Function],default:He("hideTriggers")},popperTriggers:{type:Array,default:He("popperTriggers")},popperShowTriggers:{type:[Array,Function],default:He("popperShowTriggers")},popperHideTriggers:{type:[Array,Function],default:He("popperHideTriggers")},container:{type:[String,Object,oa,Boolean],default:He("container")},boundary:{type:[String,oa],default:He("boundary")},strategy:{type:String,validator:e=>["absolute","fixed"].includes(e),default:He("strategy")},autoHide:{type:[Boolean,Function],default:He("autoHide")},handleResize:{type:Boolean,default:He("handleResize")},instantMove:{type:Boolean,default:He("instantMove")},eagerMount:{type:Boolean,default:He("eagerMount")},popperClass:{type:[String,Array,Object],default:He("popperClass")},computeTransformOrigin:{type:Boolean,default:He("computeTransformOrigin")},autoMinSize:{type:Boolean,default:He("autoMinSize")},autoSize:{type:[Boolean,String],default:He("autoSize")},autoMaxSize:{type:Boolean,default:He("autoMaxSize")},autoBoundaryMaxSize:{type:Boolean,default:He("autoBoundaryMaxSize")},preventOverflow:{type:Boolean,default:He("preventOverflow")},overflowPadding:{type:[Number,String],default:He("overflowPadding")},arrowPadding:{type:[Number,String],default:He("arrowPadding")},arrowOverflow:{type:Boolean,default:He("arrowOverflow")},flip:{type:Boolean,default:He("flip")},shift:{type:Boolean,default:He("shift")},shiftCrossAxis:{type:Boolean,default:He("shiftCrossAxis")},noAutoFocus:{type:Boolean,default:He("noAutoFocus")},disposeTimeout:{type:Number,default:He("disposeTimeout")}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},data(){return{isShown:!1,isMounted:!1,skipTransition:!1,classes:{showFrom:!1,showTo:!1,hideFrom:!1,hideTo:!0},result:{x:0,y:0,placement:"",strategy:this.strategy,arrow:{x:0,y:0,centerOffset:0},transformOrigin:null},randomId:`popper_${[Math.random(),Date.now()].map(e=>e.toString(36).substring(2,10)).join("_")}`,shownChildren:new Set,lastAutoHide:!0,pendingHide:!1,containsGlobalTarget:!1,isDisposed:!0,mouseDownContains:!1}},computed:{popperId(){return this.ariaId!=null?this.ariaId:this.randomId},shouldMountContent(){return this.eagerMount||this.isMounted},slotData(){return{popperId:this.popperId,isShown:this.isShown,shouldMountContent:this.shouldMountContent,skipTransition:this.skipTransition,autoHide:typeof this.autoHide=="function"?this.lastAutoHide:this.autoHide,show:this.show,hide:this.hide,handleResize:this.handleResize,onResize:this.onResize,classes:{...this.classes,popperClass:this.popperClass},result:this.positioningDisabled?null:this.result,attrs:this.$attrs}},parentPopper(){var e;return(e=this[fl])==null?void 0:e.parentPopper},hasPopperShowTriggerHover(){var e,t;return((e=this.popperTriggers)==null?void 0:e.includes("hover"))||((t=this.popperShowTriggers)==null?void 0:t.includes("hover"))}},watch:{shown:"$_autoShowHide",disabled(e){e?this.dispose():this.init()},async container(){this.isShown&&(this.$_ensureTeleport(),await this.$_computePosition())},triggers:{handler:"$_refreshListeners",deep:!0},positioningDisabled:"$_refreshListeners",...["placement","distance","skidding","boundary","strategy","overflowPadding","arrowPadding","preventOverflow","shift","shiftCrossAxis","flip"].reduce((e,t)=>(e[t]="$_computePosition",e),{})},created(){this.autoMinSize&&console.warn('[floating-vue] `autoMinSize` option is deprecated. Use `autoSize="min"` instead.'),this.autoMaxSize&&console.warn("[floating-vue] `autoMaxSize` option is deprecated. Use `autoBoundaryMaxSize` instead.")},mounted(){this.init(),this.$_detachPopperNode()},activated(){this.$_autoShowHide()},deactivated(){this.hide()},beforeUnmount(){this.dispose()},methods:{show({event:e=null,skipDelay:t=!1,force:n=!1}={}){var r,o;(r=this.parentPopper)!=null&&r.lockedChild&&this.parentPopper.lockedChild!==this||(this.pendingHide=!1,(n||!this.disabled)&&(((o=this.parentPopper)==null?void 0:o.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.$_scheduleShow(e,t),this.$emit("show"),this.$_showFrameLocked=!0,requestAnimationFrame(()=>{this.$_showFrameLocked=!1})),this.$emit("update:shown",!0))},hide({event:e=null,skipDelay:t=!1}={}){var n;if(!this.$_hideInProgress){if(this.shownChildren.size>0){this.pendingHide=!0;return}if(this.hasPopperShowTriggerHover&&this.$_isAimingPopper()){this.parentPopper&&(this.parentPopper.lockedChild=this,clearTimeout(this.parentPopper.lockedChildTimer),this.parentPopper.lockedChildTimer=setTimeout(()=>{this.parentPopper.lockedChild===this&&(this.parentPopper.lockedChild.hide({skipDelay:t}),this.parentPopper.lockedChild=null)},1e3));return}((n=this.parentPopper)==null?void 0:n.lockedChild)===this&&(this.parentPopper.lockedChild=null),this.pendingHide=!1,this.$_scheduleHide(e,t),this.$emit("hide"),this.$emit("update:shown",!1)}},init(){var e;this.isDisposed&&(this.isDisposed=!1,this.isMounted=!1,this.$_events=[],this.$_preventShow=!1,this.$_referenceNode=((e=this.referenceNode)==null?void 0:e.call(this))??this.$el,this.$_targetNodes=this.targetNodes().filter(t=>t.nodeType===t.ELEMENT_NODE),this.$_popperNode=this.popperNode(),this.$_innerNode=this.$_popperNode.querySelector(".v-popper__inner"),this.$_arrowNode=this.$_popperNode.querySelector(".v-popper__arrow-container"),this.$_swapTargetAttrs("title","data-original-title"),this.$_detachPopperNode(),this.triggers.length&&this.$_addEventListeners(),this.shown&&this.show())},dispose(){this.isDisposed||(this.isDisposed=!0,this.$_removeEventListeners(),this.hide({skipDelay:!0}),this.$_detachPopperNode(),this.isMounted=!1,this.isShown=!1,this.$_updateParentShownChildren(!1),this.$_swapTargetAttrs("data-original-title","title"))},async onResize(){this.isShown&&(await this.$_computePosition(),this.$emit("resize"))},async $_computePosition(){if(this.isDisposed||this.positioningDisabled)return;const e={strategy:this.strategy,middleware:[]};(this.distance||this.skidding)&&e.middleware.push(rx({mainAxis:this.distance,crossAxis:this.skidding}));const t=this.placement.startsWith("auto");if(t?e.middleware.push(ex({alignment:this.placement.split("-")[1]??""})):e.placement=this.placement,this.preventOverflow&&(this.shift&&e.middleware.push(ox({padding:this.overflowPadding,boundary:this.boundary,crossAxis:this.shiftCrossAxis})),!t&&this.flip&&e.middleware.push(tx({padding:this.overflowPadding,boundary:this.boundary}))),e.middleware.push(XA({element:this.$_arrowNode,padding:this.arrowPadding})),this.arrowOverflow&&e.middleware.push({name:"arrowOverflow",fn:({placement:r,rects:o,middlewareData:s})=>{let i;const{centerOffset:l}=s.arrow;return r.startsWith("top")||r.startsWith("bottom")?i=Math.abs(l)>o.reference.width/2:i=Math.abs(l)>o.reference.height/2,{data:{overflow:i}}}}),this.autoMinSize||this.autoSize){const r=this.autoSize?this.autoSize:this.autoMinSize?"min":null;e.middleware.push({name:"autoSize",fn:({rects:o,placement:s,middlewareData:i})=>{var l;if((l=i.autoSize)!=null&&l.skip)return{};let a,u;return s.startsWith("top")||s.startsWith("bottom")?a=o.reference.width:u=o.reference.height,this.$_innerNode.style[r==="min"?"minWidth":r==="max"?"maxWidth":"width"]=a!=null?`${a}px`:null,this.$_innerNode.style[r==="min"?"minHeight":r==="max"?"maxHeight":"height"]=u!=null?`${u}px`:null,{data:{skip:!0},reset:{rects:!0}}}})}(this.autoMaxSize||this.autoBoundaryMaxSize)&&(this.$_innerNode.style.maxWidth=null,this.$_innerNode.style.maxHeight=null,e.middleware.push(sx({boundary:this.boundary,padding:this.overflowPadding,apply:({availableWidth:r,availableHeight:o})=>{this.$_innerNode.style.maxWidth=r!=null?`${r}px`:null,this.$_innerNode.style.maxHeight=o!=null?`${o}px`:null}})));const n=await ux(this.$_referenceNode,this.$_popperNode,e);Object.assign(this.result,{x:n.x,y:n.y,placement:n.placement,strategy:n.strategy,arrow:{...n.middlewareData.arrow,...n.middlewareData.arrowOverflow}})},$_scheduleShow(e,t=!1){if(this.$_updateParentShownChildren(!0),this.$_hideInProgress=!1,clearTimeout(this.$_scheduleTimer),or&&this.instantMove&&or.instantMove&&or!==this.parentPopper){or.$_applyHide(!0),this.$_applyShow(!0);return}t?this.$_applyShow():this.$_scheduleTimer=setTimeout(this.$_applyShow.bind(this),this.$_computeDelay("show"))},$_scheduleHide(e,t=!1){if(this.shownChildren.size>0){this.pendingHide=!0;return}this.$_updateParentShownChildren(!1),this.$_hideInProgress=!0,clearTimeout(this.$_scheduleTimer),this.isShown&&(or=this),t?this.$_applyHide():this.$_scheduleTimer=setTimeout(this.$_applyHide.bind(this),this.$_computeDelay("hide"))},$_computeDelay(e){const t=this.delay;return parseInt(t&&t[e]||t||0)},async $_applyShow(e=!1){clearTimeout(this.$_disposeTimer),clearTimeout(this.$_scheduleTimer),this.skipTransition=e,!this.isShown&&(this.$_ensureTeleport(),await dl(),await this.$_computePosition(),await this.$_applyShowEffect(),this.positioningDisabled||this.$_registerEventListeners([...Bs(this.$_referenceNode),...Bs(this.$_popperNode)],"scroll",()=>{this.$_computePosition()}))},async $_applyShowEffect(){if(this.$_hideInProgress)return;if(this.computeTransformOrigin){const t=this.$_referenceNode.getBoundingClientRect(),n=this.$_popperNode.querySelector(".v-popper__wrapper"),r=n.parentNode.getBoundingClientRect(),o=t.x+t.width/2-(r.left+n.offsetLeft),s=t.y+t.height/2-(r.top+n.offsetTop);this.result.transformOrigin=`${o}px ${s}px`}this.isShown=!0,this.$_applyAttrsToTarget({"aria-describedby":this.popperId,"data-popper-shown":""});const e=this.showGroup;if(e){let t;for(let n=0;n<Jt.length;n++)t=Jt[n],t.showGroup!==e&&(t.hide(),t.$emit("close-group"))}Jt.push(this),document.body.classList.add("v-popper--some-open");for(const t of vd(this.theme))wd(t).push(this),document.body.classList.add(`v-popper--some-open--${t}`);this.$emit("apply-show"),this.classes.showFrom=!0,this.classes.showTo=!1,this.classes.hideFrom=!1,this.classes.hideTo=!1,await dl(),this.classes.showFrom=!1,this.classes.showTo=!0,this.noAutoFocus||this.$_popperNode.focus()},async $_applyHide(e=!1){if(this.shownChildren.size>0){this.pendingHide=!0,this.$_hideInProgress=!1;return}if(clearTimeout(this.$_scheduleTimer),!this.isShown)return;this.skipTransition=e,yd(Jt,this),Jt.length===0&&document.body.classList.remove("v-popper--some-open");for(const n of vd(this.theme)){const r=wd(n);yd(r,this),r.length===0&&document.body.classList.remove(`v-popper--some-open--${n}`)}or===this&&(or=null),this.isShown=!1,this.$_applyAttrsToTarget({"aria-describedby":void 0,"data-popper-shown":void 0}),clearTimeout(this.$_disposeTimer);const t=this.disposeTimeout;t!==null&&(this.$_disposeTimer=setTimeout(()=>{this.$_popperNode&&(this.$_detachPopperNode(),this.isMounted=!1)},t)),this.$_removeEventListeners("scroll"),this.$emit("apply-hide"),this.classes.showFrom=!1,this.classes.showTo=!1,this.classes.hideFrom=!0,this.classes.hideTo=!1,await dl(),this.classes.hideFrom=!1,this.classes.hideTo=!0},$_autoShowHide(){this.shown?this.show():this.hide()},$_ensureTeleport(){if(this.isDisposed)return;let e=this.container;if(typeof e=="string"?e=window.document.querySelector(e):e===!1&&(e=this.$_targetNodes[0].parentNode),!e)throw new Error("No container for popover: "+this.container);e.appendChild(this.$_popperNode),this.isMounted=!0},$_addEventListeners(){const e=n=>{this.isShown&&!this.$_hideInProgress||(n.usedByTooltip=!0,!this.$_preventShow&&this.show({event:n}))};this.$_registerTriggerListeners(this.$_targetNodes,md,this.triggers,this.showTriggers,e),this.$_registerTriggerListeners([this.$_popperNode],md,this.popperTriggers,this.popperShowTriggers,e);const t=n=>{n.usedByTooltip||this.hide({event:n})};this.$_registerTriggerListeners(this.$_targetNodes,gd,this.triggers,this.hideTriggers,t),this.$_registerTriggerListeners([this.$_popperNode],gd,this.popperTriggers,this.popperHideTriggers,t)},$_registerEventListeners(e,t,n){this.$_events.push({targetNodes:e,eventType:t,handler:n}),e.forEach(r=>r.addEventListener(t,n,Ho?{passive:!0}:void 0))},$_registerTriggerListeners(e,t,n,r,o){let s=n;r!=null&&(s=typeof r=="function"?r(s):r),s.forEach(i=>{const l=t[i];l&&this.$_registerEventListeners(e,l,o)})},$_removeEventListeners(e){const t=[];this.$_events.forEach(n=>{const{targetNodes:r,eventType:o,handler:s}=n;!e||e===o?r.forEach(i=>i.removeEventListener(o,s)):t.push(n)}),this.$_events=t},$_refreshListeners(){this.isDisposed||(this.$_removeEventListeners(),this.$_addEventListeners())},$_handleGlobalClose(e,t=!1){this.$_showFrameLocked||(this.hide({event:e}),e.closePopover?this.$emit("close-directive"):this.$emit("auto-hide"),t&&(this.$_preventShow=!0,setTimeout(()=>{this.$_preventShow=!1},300)))},$_detachPopperNode(){this.$_popperNode.parentNode&&this.$_popperNode.parentNode.removeChild(this.$_popperNode)},$_swapTargetAttrs(e,t){for(const n of this.$_targetNodes){const r=n.getAttribute(e);r&&(n.removeAttribute(e),n.setAttribute(t,r))}},$_applyAttrsToTarget(e){for(const t of this.$_targetNodes)for(const n in e){const r=e[n];r==null?t.removeAttribute(n):t.setAttribute(n,r)}},$_updateParentShownChildren(e){let t=this.parentPopper;for(;t;)e?t.shownChildren.add(this.randomId):(t.shownChildren.delete(this.randomId),t.pendingHide&&t.hide()),t=t.parentPopper},$_isAimingPopper(){const e=this.$_referenceNode.getBoundingClientRect();if(bo>=e.left&&bo<=e.right&&wo>=e.top&&wo<=e.bottom){const t=this.$_popperNode.getBoundingClientRect(),n=bo-In,r=wo-Nn,o=t.left+t.width/2-In+(t.top+t.height/2)-Nn+t.width+t.height,s=In+n*o,i=Nn+r*o;return ps(In,Nn,s,i,t.left,t.top,t.left,t.bottom)||ps(In,Nn,s,i,t.left,t.top,t.right,t.top)||ps(In,Nn,s,i,t.right,t.top,t.right,t.bottom)||ps(In,Nn,s,i,t.left,t.bottom,t.right,t.bottom)}return!1}},render(){return this.$slots.default(this.slotData)}});if(typeof document<"u"&&typeof window<"u"){if(n1){const e=Ho?{passive:!0,capture:!0}:!0;document.addEventListener("touchstart",t=>_d(t),e),document.addEventListener("touchend",t=>Sd(t,!0),e)}else window.addEventListener("mousedown",e=>_d(e),!0),window.addEventListener("click",e=>Sd(e,!1),!0);window.addEventListener("resize",px)}function _d(e,t){for(let n=0;n<Jt.length;n++){const r=Jt[n];try{r.mouseDownContains=r.popperNode().contains(e.target)}catch{}}}function Sd(e,t){dx(e,t)}function dx(e,t){const n={};for(let r=Jt.length-1;r>=0;r--){const o=Jt[r];try{const s=o.containsGlobalTarget=o.mouseDownContains||o.popperNode().contains(e.target);o.pendingHide=!1,requestAnimationFrame(()=>{if(o.pendingHide=!1,!n[o.randomId]&&$d(o,s,e)){if(o.$_handleGlobalClose(e,t),!e.closeAllPopover&&e.closePopover&&s){let l=o.parentPopper;for(;l;)n[l.randomId]=!0,l=l.parentPopper;return}let i=o.parentPopper;for(;i&&$d(i,i.containsGlobalTarget,e);)i.$_handleGlobalClose(e,t),i=i.parentPopper}})}catch{}}}function $d(e,t,n){return n.closeAllPopover||n.closePopover&&t||fx(e,n)&&!t}function fx(e,t){if(typeof e.autoHide=="function"){const n=e.autoHide(t);return e.lastAutoHide=n,n}return e.autoHide}function px(){for(let e=0;e<Jt.length;e++)Jt[e].$_computePosition()}let In=0,Nn=0,bo=0,wo=0;typeof window<"u"&&window.addEventListener("mousemove",e=>{In=bo,Nn=wo,bo=e.clientX,wo=e.clientY},Ho?{passive:!0}:void 0);function ps(e,t,n,r,o,s,i,l){const a=((i-o)*(t-s)-(l-s)*(e-o))/((l-s)*(n-e)-(i-o)*(r-t)),u=((n-e)*(t-s)-(r-t)*(e-o))/((l-s)*(n-e)-(i-o)*(r-t));return a>=0&&a<=1&&u>=0&&u<=1}const hx={extends:o1()},Oi=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n};function vx(e,t,n,r,o,s){return x(),T("div",{ref:"reference",class:ot(["v-popper",{"v-popper--shown":e.slotData.isShown}])},[Ut(e.$slots,"default",S1(Ff(e.slotData)))],2)}const mx=Oi(hx,[["render",vx]]);function gx(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);var n=e.indexOf("Trident/");if(n>0){var r=e.indexOf("rv:");return parseInt(e.substring(r+3,e.indexOf(".",r)),10)}var o=e.indexOf("Edge/");return o>0?parseInt(e.substring(o+5,e.indexOf(".",o)),10):-1}let xs;function sa(){sa.init||(sa.init=!0,xs=gx()!==-1)}var Pi={name:"ResizeObserver",props:{emitOnMount:{type:Boolean,default:!1},ignoreWidth:{type:Boolean,default:!1},ignoreHeight:{type:Boolean,default:!1}},emits:["notify"],mounted(){sa(),et(()=>{this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitOnMount&&this.emitSize()});const e=document.createElement("object");this._resizeObject=e,e.setAttribute("aria-hidden","true"),e.setAttribute("tabindex",-1),e.onload=this.addResizeHandlers,e.type="text/html",xs&&this.$el.appendChild(e),e.data="about:blank",xs||this.$el.appendChild(e)},beforeUnmount(){this.removeResizeHandlers()},methods:{compareAndNotify(){(!this.ignoreWidth&&this._w!==this.$el.offsetWidth||!this.ignoreHeight&&this._h!==this.$el.offsetHeight)&&(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.emitSize())},emitSize(){this.$emit("notify",{width:this._w,height:this._h})},addResizeHandlers(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers(){this._resizeObject&&this._resizeObject.onload&&(!xs&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};const yx=ah();ih("data-v-b329ee4c");const bx={class:"resize-observer",tabindex:"-1"};lh();const wx=yx((e,t,n,r,o,s)=>(x(),tt("div",bx)));Pi.render=wx;Pi.__scopeId="data-v-b329ee4c";Pi.__file="src/components/ResizeObserver.vue";const s1=(e="theme")=>({computed:{themeClass(){return cx(this[e])}}}),_x=_e({name:"VPopperContent",components:{ResizeObserver:Pi},mixins:[s1()],props:{popperId:String,theme:String,shown:Boolean,mounted:Boolean,skipTransition:Boolean,autoHide:Boolean,handleResize:Boolean,classes:Object,result:Object},emits:["hide","resize"],methods:{toPx(e){return e!=null&&!isNaN(e)?`${e}px`:null}}}),Sx=["id","aria-hidden","tabindex","data-popper-placement"],$x={ref:"inner",class:"v-popper__inner"},Ax=h("div",{class:"v-popper__arrow-outer"},null,-1),xx=h("div",{class:"v-popper__arrow-inner"},null,-1),Ex=[Ax,xx];function Cx(e,t,n,r,o,s){const i=rn("ResizeObserver");return x(),T("div",{id:e.popperId,ref:"popover",class:ot(["v-popper__popper",[e.themeClass,e.classes.popperClass,{"v-popper__popper--shown":e.shown,"v-popper__popper--hidden":!e.shown,"v-popper__popper--show-from":e.classes.showFrom,"v-popper__popper--show-to":e.classes.showTo,"v-popper__popper--hide-from":e.classes.hideFrom,"v-popper__popper--hide-to":e.classes.hideTo,"v-popper__popper--skip-transition":e.skipTransition,"v-popper__popper--arrow-overflow":e.result&&e.result.arrow.overflow,"v-popper__popper--no-positioning":!e.result}]]),style:hr(e.result?{position:e.result.strategy,transform:`translate3d(${Math.round(e.result.x)}px,${Math.round(e.result.y)}px,0)`}:void 0),"aria-hidden":e.shown?"false":"true",tabindex:e.autoHide?0:void 0,"data-popper-placement":e.result?e.result.placement:void 0,onKeyup:t[2]||(t[2]=zv(l=>e.autoHide&&e.$emit("hide"),["esc"]))},[h("div",{class:"v-popper__backdrop",onClick:t[0]||(t[0]=l=>e.autoHide&&e.$emit("hide"))}),h("div",{class:"v-popper__wrapper",style:hr(e.result?{transformOrigin:e.result.transformOrigin}:void 0)},[h("div",$x,[e.mounted?(x(),T(he,{key:0},[h("div",null,[Ut(e.$slots,"default")]),e.handleResize?(x(),tt(i,{key:0,onNotify:t[1]||(t[1]=l=>e.$emit("resize",l))})):ce("",!0)],64)):ce("",!0)],512),h("div",{ref:"arrow",class:"v-popper__arrow-container",style:hr(e.result?{left:e.toPx(e.result.arrow.x),top:e.toPx(e.result.arrow.y)}:void 0)},Ex,4)],4)],46,Sx)}const i1=Oi(_x,[["render",Cx]]),l1={methods:{show(...e){return this.$refs.popper.show(...e)},hide(...e){return this.$refs.popper.hide(...e)},dispose(...e){return this.$refs.popper.dispose(...e)},onResize(...e){return this.$refs.popper.onResize(...e)}}};let ia=function(){};typeof window<"u"&&(ia=window.Element);const Tx=_e({name:"VPopperWrapper",components:{Popper:mx,PopperContent:i1},mixins:[l1,s1("finalTheme")],props:{theme:{type:String,default:null},referenceNode:{type:Function,default:null},shown:{type:Boolean,default:!1},showGroup:{type:String,default:null},ariaId:{default:null},disabled:{type:Boolean,default:void 0},positioningDisabled:{type:Boolean,default:void 0},placement:{type:String,default:void 0},delay:{type:[String,Number,Object],default:void 0},distance:{type:[Number,String],default:void 0},skidding:{type:[Number,String],default:void 0},triggers:{type:Array,default:void 0},showTriggers:{type:[Array,Function],default:void 0},hideTriggers:{type:[Array,Function],default:void 0},popperTriggers:{type:Array,default:void 0},popperShowTriggers:{type:[Array,Function],default:void 0},popperHideTriggers:{type:[Array,Function],default:void 0},container:{type:[String,Object,ia,Boolean],default:void 0},boundary:{type:[String,ia],default:void 0},strategy:{type:String,default:void 0},autoHide:{type:[Boolean,Function],default:void 0},handleResize:{type:Boolean,default:void 0},instantMove:{type:Boolean,default:void 0},eagerMount:{type:Boolean,default:void 0},popperClass:{type:[String,Array,Object],default:void 0},computeTransformOrigin:{type:Boolean,default:void 0},autoMinSize:{type:Boolean,default:void 0},autoSize:{type:[Boolean,String],default:void 0},autoMaxSize:{type:Boolean,default:void 0},autoBoundaryMaxSize:{type:Boolean,default:void 0},preventOverflow:{type:Boolean,default:void 0},overflowPadding:{type:[Number,String],default:void 0},arrowPadding:{type:[Number,String],default:void 0},arrowOverflow:{type:Boolean,default:void 0},flip:{type:Boolean,default:void 0},shift:{type:Boolean,default:void 0},shiftCrossAxis:{type:Boolean,default:void 0},noAutoFocus:{type:Boolean,default:void 0},disposeTimeout:{type:Number,default:void 0}},emits:{show:()=>!0,hide:()=>!0,"update:shown":e=>!0,"apply-show":()=>!0,"apply-hide":()=>!0,"close-group":()=>!0,"close-directive":()=>!0,"auto-hide":()=>!0,resize:()=>!0},computed:{finalTheme(){return this.theme??this.$options.vPopperTheme}},methods:{getTargetNodes(){return Array.from(this.$el.children).filter(e=>e!==this.$refs.popperContent.$el)}}});function Ox(e,t,n,r,o,s){const i=rn("PopperContent"),l=rn("Popper");return x(),tt(l,dt({ref:"popper"},e.$props,{theme:e.finalTheme,"target-nodes":e.getTargetNodes,"popper-node":()=>e.$refs.popperContent.$el,class:[e.themeClass],onShow:t[0]||(t[0]=()=>e.$emit("show")),onHide:t[1]||(t[1]=()=>e.$emit("hide")),"onUpdate:shown":t[2]||(t[2]=a=>e.$emit("update:shown",a)),onApplyShow:t[3]||(t[3]=()=>e.$emit("apply-show")),onApplyHide:t[4]||(t[4]=()=>e.$emit("apply-hide")),onCloseGroup:t[5]||(t[5]=()=>e.$emit("close-group")),onCloseDirective:t[6]||(t[6]=()=>e.$emit("close-directive")),onAutoHide:t[7]||(t[7]=()=>e.$emit("auto-hide")),onResize:t[8]||(t[8]=()=>e.$emit("resize"))}),{default:X(({popperId:a,isShown:u,shouldMountContent:c,skipTransition:f,autoHide:d,show:p,hide:v,handleResize:m,onResize:g,classes:b,result:y})=>[Ut(e.$slots,"default",{shown:u,show:p,hide:v}),M(i,{ref:"popperContent","popper-id":a,theme:e.finalTheme,shown:u,mounted:c,"skip-transition":f,"auto-hide":d,"handle-resize":m,classes:b,result:y,onHide:v,onResize:g},{default:X(()=>[Ut(e.$slots,"popper",{shown:u,hide:v})]),_:2},1032,["popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:3},16,["theme","target-nodes","popper-node","class"])}const uu=Oi(Tx,[["render",Ox]]);({...uu});({...uu});({...uu});const Px=_e({name:"VTooltipDirective",components:{Popper:o1(),PopperContent:i1},mixins:[l1],inheritAttrs:!1,props:{theme:{type:String,default:"tooltip"},html:{type:Boolean,default:e=>No(e.theme,"html")},content:{type:[String,Number,Function],default:null},loadingContent:{type:String,default:e=>No(e.theme,"loadingContent")},targetNodes:{type:Function,required:!0}},data(){return{asyncContent:null}},computed:{isContentAsync(){return typeof this.content=="function"},loading(){return this.isContentAsync&&this.asyncContent==null},finalContent(){return this.isContentAsync?this.loading?this.loadingContent:this.asyncContent:this.content}},watch:{content:{handler(){this.fetchContent(!0)},immediate:!0},async finalContent(){await this.$nextTick(),this.$refs.popper.onResize()}},created(){this.$_fetchId=0},methods:{fetchContent(e){if(typeof this.content=="function"&&this.$_isShown&&(e||!this.$_loading&&this.asyncContent==null)){this.asyncContent=null,this.$_loading=!0;const t=++this.$_fetchId,n=this.content(this);n.then?n.then(r=>this.onResult(t,r)):this.onResult(t,n)}},onResult(e,t){e===this.$_fetchId&&(this.$_loading=!1,this.asyncContent=t)},onShow(){this.$_isShown=!0,this.fetchContent()},onHide(){this.$_isShown=!1}}}),Rx=["innerHTML"],Mx=["textContent"];function kx(e,t,n,r,o,s){const i=rn("PopperContent"),l=rn("Popper");return x(),tt(l,dt({ref:"popper"},e.$attrs,{theme:e.theme,"target-nodes":e.targetNodes,"popper-node":()=>e.$refs.popperContent.$el,onApplyShow:e.onShow,onApplyHide:e.onHide}),{default:X(({popperId:a,isShown:u,shouldMountContent:c,skipTransition:f,autoHide:d,hide:p,handleResize:v,onResize:m,classes:g,result:b})=>[M(i,{ref:"popperContent",class:ot({"v-popper--tooltip-loading":e.loading}),"popper-id":a,theme:e.theme,shown:u,mounted:c,"skip-transition":f,"auto-hide":d,"handle-resize":v,classes:g,result:b,onHide:p,onResize:m},{default:X(()=>[e.html?(x(),T("div",{key:0,innerHTML:e.finalContent},null,8,Rx)):(x(),T("div",{key:1,textContent:pe(e.finalContent)},null,8,Mx))]),_:2},1032,["class","popper-id","theme","shown","mounted","skip-transition","auto-hide","handle-resize","classes","result","onHide","onResize"])]),_:1},16,["theme","target-nodes","popper-node","onApplyShow","onApplyHide"])}const Lx=Oi(Px,[["render",kx]]),a1="v-popper--has-tooltip";function Ix(e,t){let n=e.placement;if(!n&&t)for(const r of r1)t[r]&&(n=r);return n||(n=No(e.theme||"tooltip","placement")),n}function u1(e,t,n){let r;const o=typeof t;return o==="string"?r={content:t}:t&&o==="object"?r=t:r={content:!1},r.placement=Ix(r,n),r.targetNodes=()=>[e],r.referenceNode=()=>e,r}let pl,Fo,Nx=0;function Hx(){if(pl)return;Fo=F([]),pl=Pa({name:"VTooltipDirectiveApp",setup(){return{directives:Fo}},render(){return this.directives.map(t=>Oe(Lx,{...t.options,shown:t.shown||t.options.shown,key:t.id}))},devtools:{hide:!0}});const e=document.createElement("div");document.body.appendChild(e),pl.mount(e)}function Fx(e,t,n){Hx();const r=F(u1(e,t,n)),o=F(!1),s={id:Nx++,options:r,shown:o};return Fo.value.push(s),e.classList&&e.classList.add(a1),e.$_popper={options:r,item:s,show(){o.value=!0},hide(){o.value=!1}}}function c1(e){if(e.$_popper){const t=Fo.value.indexOf(e.$_popper.item);t!==-1&&Fo.value.splice(t,1),delete e.$_popper,delete e.$_popperOldShown,delete e.$_popperMountTarget}e.classList&&e.classList.remove(a1)}function Ad(e,{value:t,modifiers:n}){const r=u1(e,t,n);if(!r.content||No(r.theme||"tooltip","disabled"))c1(e);else{let o;e.$_popper?(o=e.$_popper,o.options.value=r):o=Fx(e,t,n),typeof t.shown<"u"&&t.shown!==e.$_popperOldShown&&(e.$_popperOldShown=t.shown,t.shown?o.show():o.hide())}}const Vx={beforeMount:Ad,updated:Ad,beforeUnmount(e){c1(e)}},Dx=Vx;zA();const Bx=vw(),Vo=Pa(Xw);Vo.config.globalProperties.window=window;Vo.use(Bx,Vue3Toastify,Jw);yp({autoClose:3e3,theme:"colored",position:"top-center"});Vo.directive("tooltip",Dx);su.authenticateWithToken().then(()=>{jA(sd),Vo.use(sd),Vo.mount("#app")});
