{"name": "j<PERSON>y", "title": "j<PERSON><PERSON><PERSON>", "description": "JavaScript library for DOM operations", "version": "3.3.1", "main": "dist/jquery.js", "homepage": "https://jquery.com", "author": {"name": "JS Foundation and other contributors", "url": "https://github.com/jquery/jquery/blob/3.3.1/AUTHORS.txt"}, "repository": {"type": "git", "url": "https://github.com/jquery/jquery.git"}, "keywords": ["j<PERSON>y", "javascript", "browser", "library"], "bugs": {"url": "https://github.com/jquery/jquery/issues"}, "license": "MIT", "dependencies": {}, "devDependencies": {"babel-core": "7.0.0-beta.0", "babel-plugin-transform-es2015-for-of": "7.0.0-beta.0", "commitplease": "2.7.10", "core-js": "2.4.1", "eslint-config-jquery": "1.0.1", "grunt": "1.0.1", "grunt-babel": "7.0.0", "grunt-cli": "1.2.0", "grunt-compare-size": "0.4.2", "grunt-contrib-uglify": "3.0.1", "grunt-contrib-watch": "1.0.0", "grunt-eslint": "20.0.0", "grunt-git-authors": "3.2.0", "grunt-jsonlint": "1.1.0", "grunt-karma": "2.0.0", "grunt-newer": "1.3.0", "grunt-npmcopy": "0.1.0", "gzip-js": "0.3.2", "husky": "0.14.3", "insight": "0.8.4", "jsdom": "5.6.1", "karma": "1.7.0", "karma-browserstack-launcher": "1.3.0", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.0.1", "karma-qunit": "1.2.1", "load-grunt-tasks": "3.5.2", "native-promise-only": "0.8.1", "promises-aplus-tests": "2.1.2", "q": "1.5.0", "qunit-assert-step": "1.0.3", "qunitjs": "1.23.1", "raw-body": "2.2.0", "requirejs": "2.3.3", "sinon": "2.3.7", "sizzle": "2.3.3", "strip-json-comments": "2.0.1", "testswarm": "1.1.0", "uglify-js": "3.3.4"}, "scripts": {"build": "npm install && grunt", "start": "grunt watch", "test:browserless": "grunt && grunt test:slow", "test:browser": "grunt && grunt karma:main", "test": "grunt && grunt test:slow && grunt karma:main", "jenkins": "npm run test:browserless", "precommit": "grunt lint:newer qunit_fixture", "commitmsg": "node node_modules/commitplease"}, "commitplease": {"nohook": true, "components": ["Docs", "Tests", "Build", "Support", "Release", "Core", "Ajax", "Attributes", "Callbacks", "CSS", "Data", "Deferred", "Deprecated", "Dimensions", "Effects", "Event", "Manipulation", "Offset", "Queue", "Selector", "Serialize", "Traversing", "Wrap"], "markerPattern": "^((clos|fix|resolv)(e[sd]|ing))|^(refs?)", "ticketPattern": "^((Closes|Fixes) ([a-zA-Z]{2,}-)[0-9]+)|^(Refs? [^#])"}}