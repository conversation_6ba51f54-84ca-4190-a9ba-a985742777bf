
<div class="row" style="padding-top: 25px; padding-bottom: 25px">
    <div class="col-sm-6 d-flex align-items-center mb-3 mb-sm-0">
        <div style="font-size: 18px; font-weight: 400; color: #000000">Inserisci un'autocertificazione delle ore totali esterne effettuate durante l'anno.<br><strong>Tutti i dati relativi ai singoli corsi precedentemente caricati verranno cancellati.</strong></div>
    </div>
    <div class="col-sm-6 d-flex align-items-center mb-3 mb-sm-0">
        <div class="text-right w-100">
            <a href="/api/download/?path=apps/formazione/Formazione_modulo_Autocertificazione.pdf" class="btn btn-secondary mb-3 mb-sm-0 mr-0 mr-sm-2" style="font-size: 16px"><i class="fa fa-download"></i> Modulo Autocertificazione</a>
            <button type="button" class="btn btn-brand" style="font-size: 16px" ng-click="addSelfCertification()"><i class="fa fa-plus"></i> Inserisci Autocertificazione</button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="dataTables_wrapper dt-bootstrap4 no-footer table-responsive" style="border-top: 5px solid #366CF3;">

            <table ng-table="selfCertificationsTable" class="table table-striped row-hover table-checkable dataTable no-footer dtr-inline overflow-auto" style="border: 1px solid #ebedf2">

                <tr class="tr-hoverable" ng-repeat="selfCertification in $data">

                    <td data-title="'Anno'" sortable="year" filter="{year: 'select'}" filter-data="years" style="width: 50px">
                        {{selfCertification.year}}
                    </td>
                    <td data-title="'Data Caricamento'">{{selfCertification.createdAt | date : 'd/M/y'}}</td>
                    <td data-title="'File caricato'">
                        <a type="button" class="btn btn-outline-hover-success btn-sm btn-icon" tooltip-placement="top" uib-tooltip="Scarica attestato"
                           ng-href="/api/apps/formazione/self-certificated/{{selfCertification.id}}/download"><i class="fa fa-download"></i></a>
                    </td>
                    <td data-title="'Ore formative'">
                        {{selfCertification.credits}}
                    </td>
                    <td data-title="'Azioni'">
                        <span style="width: 120px;">
                            <button ng-click="open(null, null, null, true, selfCertification)" class="btn btn-sm btn-clean btn-icon btn-icon-md" tooltip-placement="top" uib-tooltip="Modifica Autocertificazione">
                                <i class="flaticon2-writing"></i>
                            </button>
                            <button ng-click="delete(selfCertification.id)" class="btn btn-sm btn-clean btn-icon btn-icon-md" tooltip-placement="top" uib-tooltip="Cancella Autocertificazione">
                                <i class="flaticon2-trash"></i>
                            </button>
                        </span>
                    </td>

                </tr>

            </table>
        </div>
    </div>
</div>

<div class="modal">
    <script type="text/ng-template" id="modalSelfCertification.html">
        <div class="modal-header">
            <h3 class="modal-title">Autocertificazione</h3>
        </div>
        <form class="kt-form" name="selfCertification" ng-submit="selfCertification.$valid && save(course)" novalidate>

            <div class="modal-body">

                <div class="kt-portlet__body">

                    <div class="row">
                        <div class="col">
                            <p class="kt-font-lg kt-font-bolder">Caricando il documento di autocertificazione, l'Agente dichiara sotto la propria responsabilità di avere assolto agli obblighi formativi annuali previsti da IVASS.</p>
                        </div>
                    </div>

                    <div class="form-group row validated" ng-if="showYearsSelect">
                        <div class="col-md-4">
                            <label class="form-control-label" for="year">Anno di riferimento</label>
                            <select
                                    name="year" id="year" class="form-control"
                                    ng-class="{ 'is-invalid' : selfCertification.year.$invalid && selfCertification.$submitted }"
                                    ng-options="y as y for y in years"
                                    ng-model="course.year" required></select>
                            <div class="invalid-feedback" ng-show="selfCertification.year.$invalid && selfCertification.$submitted">Questo campo è obbligatorio</div>
                        </div>
                    </div>

                    <div class="form-group row validated">
                        <div class="col-md-8">
                            <label class="form-control-label">Ore formative (es. 02:30)</label>
                            <div class="row no-gutter">
                                <div class="col-5">
                                    <div class="form-group validated kt-margin-0">
                                        <input
                                            type="number" class="form-control" min="0" max="99"
                                            placeholder="Ore"
                                            ng-class="{ 'is-invalid' : selfCertification.hours.$invalid && selfCertification.$submitted}"
                                            id="hours" name="hours" ng-model="course.hours" required>
                                        <div class="invalid-feedback" ng-show="selfCertification.hours.$invalid && selfCertification.$submitted">Questo campo è obbligatorio</div>
                                    </div>
                                </div>
                                <div class="col-1 d-flex align-items-center justify-content-center kt-font-bolder">:</div>
                                <div class="col-5">
                                    <div class="form-group validated kt-margin-0">
                                        <select
                                            name="minutes" id="minutes" class="form-control"
                                            ng-class="{ 'is-invalid' : selfCertification.minutes.$invalid && selfCertification.$submitted }"
                                            ng-model="course.minutes" required>
                                            <option value="" disabled ng-selected="! isEdit">Minuti</option>
                                            <option value="00">00</option>
                                            <option value="15">15</option>
                                            <option value="30">30</option>
                                            <option value="45">45</option>
                                        </select>
                                        <div class="invalid-feedback" ng-show="selfCertification.minutes.$invalid && selfCertification.$submitted">Questo campo è obbligatorio</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row validated">
                        <div class="col-md-12">
                            <label>Carica Autocertificazione</label>
                            <div></div>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="certificate" fileread="course.certificate" ng-model="course.certificate" name="certificate" required>
                                <label class="custom-file-label" for="certificate"><span ng-if="!course.certificate.name">Scegli file</span><span ng-if="course.certificate.name">{{course.certificate.name}}</span></label>
                            </div>
                            <div class="invalid-feedback" ng-show="selfCertification.certificate.$invalid && selfCertification.$submitted">Questo campo è obbligatorio</div>
                        </div>
                    </div>

                </div>

            </div>

            <div class="modal-footer">
                <button class="btn btn-success" type="submit" ng-class="{'kt-spinner kt-spinner--right kt-spinner--sm kt-spinner--light' : waitingResponse}" ng-disabled="waitingResponse">
                    <span ng-hide="waitingResponse">Salva</span>
                    <span ng-show="waitingResponse">Salvataggio...</span>
                </button>
                <button class="btn btn-info" type="button" ng-click="close()">Annulla</button>
            </div>

        </form>

    </script>

</div>