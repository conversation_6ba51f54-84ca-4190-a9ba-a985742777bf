<div class="row">
    <div class="col-sm-3">
        <p class="kt-font-lg kt-font-bold">Inserisci TAG
            <i class="la la-question-circle kt-font-info" tooltip-placement="top"
               uib-tooltip-html="'<p class=\'kt-align-left\'>Per inserire un TAG:<br>- posiziona il cursore nel punto del messaggio in cui vuoi sia inserito<br>- clicca nella lista il tag che vuoi inserire.</p>'"></i></p>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[NOME]')">NOME</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[COGNOME]')">COGNOME</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[TITOLO_CORSO]')">TITOLO_CORSO</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[LUOGO]')">LUOGO</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[DATA]')">DATA</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[SEDE]')">SEDE</button>
        <div class="kt-separator kt-separator--space-sm kt-separator--portlet-fit"></div>
        <button type="button" class="btn btn-outline-hover-info btn-sm btn-tag text-uppercase kt-font-md d-block" ng-click="insertText('[ORE_CERT]')">ORE_CERT</button>
    </div>
    <div class="col-sm-9">
        <form name="courseNoticesSignup" ng-submit="saveSignup(notice)" unsaved-edits-monitor="courseNoticesSignup.$dirty" novalidate>

            <div class="form-group row">
                <label class="col-3 col-form-label" for="subject">Oggetto</label>
                <div class="col-9">
                    <input type="text"
                           ng-model="notice.subject"
                           ng-class="{ 'is-invalid' : courseNoticesOpening.subject.$invalid  && courseNoticesOpening.$submitted }"
                           ng-change="notice.subject = (notice.subject | uppercase)"
                           class="form-control" id="subject" name="subject" required>
                    <div class="invalid-feedback" ng-show="courseNoticesOpening.subject.$invalid  && courseNoticesOpening.$submitted">Questo campo è obbligatorio</div>
                </div>
            </div>

            <div id="editorSignup" ckeditor="editorOptions" name="body" ng-model="notice.body" ready="onReady()"></div>

            <div class="row">
                <div class="col-lg-12 kt-align-right">
                    <button type="submit" class="btn btn-info btn-elevate btn-icon-sm kt-margin-t-15"  ng-if="($root.$authData.UTYPE === 'KA' || $root.$authData.UTYPE === 'FORMAZIONE')">
                        <i class="la la-save"></i>
                        Salva
                    </button>
                </div>
            </div>

        </form>
    </div>
</div>