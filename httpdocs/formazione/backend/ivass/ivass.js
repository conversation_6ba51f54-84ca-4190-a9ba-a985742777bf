(function(angular, module){

    module.config(['$stateProvider', '$urlRouterProvider', function($stateProvider, $urlRouterProvider) {

        //$urlRouterProvider.when('/backend/ivass', '/backend/ivass/tabella2');

        $stateProvider.state('root.backend.ivass', {
            url: '/ivass/{table}',
            views: {
                "content@": {
                    templateUrl: 'backend/ivass/ivass.html',
                    controller: 'IvassController'
                },
                "ivass-table@": {
                    templateUrl: 'backend/ivass/saved.html',
                    controller: "SavedController"
                }
            },
            resolve: {
                years: function($yearsFilter) {
                    return $yearsFilter.getYearsForSelect(true);
                }
            },
            params: {
                year: null,
                table: null,
                dynamic: true
            },
            data: {
                permissions: {
                    only: ['KA', 'FORMAZIONE', 'AMMINISTRATORE'],
                    redirectTo: "root.backend"
                }
            }
        }).state('root.backend.ivass.saved', {
            url: '/saved',
            views: {
                "ivass-table": {
                    templateUrl: 'backend/ivass/saved.html',
                    controller: "SavedController"
                }
            }

        }).state('root.backend.ivass.real-time', {
            url: '/real-time',
            views: {
                "ivass-table": {
                    templateUrl: 'backend/ivass/real-time.html',
                    controller: "RealTimeController"
                }
            }

        });
    }]);

    module.controller('IvassController',  function($scope, $http, $handler, $state, years, $stateParams) {

        $scope.tableType = $stateParams.table;

        $scope.years = years;
        $scope.currentYear = String(new Date().getFullYear());

        if ($stateParams.year) {
            $scope.selectedYear = String($stateParams.year);
        }
        else {
            $scope.selectedYear = $scope.currentYear;
            $stateParams.year = $scope.currentYear;
            $state.go('.', {year: $scope.currentYear});
        }

        $scope.tipoRawTypes = [
            {id: '', title: 'Tutti'},
            {id: 'NORM', title: 'Normativo'},
            {id: 'TECN', title: 'tecnico'},
            {id: 'FISC', title: 'Fiscale'},
            {id: 'ECON', title: 'Economico'},
            {id: 'SSPR', title: 'Su specifico prodotto'},
            {id: 'GIUR', title: 'Area giuridica'},
            {id: 'TECA', title: 'Area Tecincia assicurativa e riassicurativa'},
            {id: 'ADMG', title: 'Area amministrativa e gestionale'},
            {id: 'INFO', title: 'Area informatica'},
            {id: 'ALTRO', title: 'Altro'}
        ];

        $scope.erogatoRawTypes = [
            {id: '', title: 'Tutti'},
            {id: 'DIREZ', title: 'Direzione'},
            {id: 'AGENTI', title: 'Agenti'},
            {id: 'BANCHE', title: 'Banche'},
            {id: 'INTFNZ', title: 'Intermediari finanziari'},
            {id: 'SIM', title: '?'},
            {id: 'POSTEIT', title: '?'},
            {id: 'SOCFOR', title: 'Società di formazione'},
            {id: 'ALTRO', title: 'Altro'}
        ];

        $scope.modalitaRawTypes = [
            {id: '', title: 'Tutti'},
            {id: 'DIST', title: 'A distanza'},
            {id: 'AULA', title: 'In aula'}
        ];

        $scope.codeTypes = [
            {id: '', title: 'Tutti'},
            {id: 'A', title: 'A'},
            {id: 'E', title: 'E'},
            {id: 'I', title: 'I'}
        ];

    });

    module.controller('SavedController',  function($scope, $http, $handler, $state, $stateParams, NgTableParams) {

        // Reload table on year change
        $scope.changeYear = function () {
            $stateParams.year = $scope.selectedYear;
            $scope.savedTable.reload();
            $state.go('.', {year: $scope.selectedYear});
        };

        $scope.update = function() {

            $http({
                method: 'GET',
                url: '/api/apps/formazione/ivass/updateYear/' + $scope.selectedYear
            }).then(function (response) {

                if (! response.data.success) {
                    return $handler.handle(response.data);
                }

                toastr.success('Tabelle IVASS aggiornate.');
                $scope.savedTable.reload();

            });

        };

        $scope.savedTable = new NgTableParams({

        }, {
            counts: [],
            getData: function (params) {

                return $http({
                    method: 'GET',
                    url: '/api/apps/formazione/ivass/currentYear/' + $scope.selectedYear + '/' + $stateParams.table
                }).then(function (response) {

                    if (! response.data.success) {
                        return $handler.handle(response.data);
                    }

                    $scope.savedTotal = response.data.data.total;
                    return response.data.data.items;

                });

            }
        });

    });

    module.controller('RealTimeController',  function($scope, $http, $handler, $state, $stateParams, NgTableParams) {

        $scope.changeYear = function () {
            $stateParams.year = $scope.selectedYear;
            $scope.realtimeTable.reload();
            $state.go('.', {year: $scope.selectedYear});
        };

        $scope.realtimeTable = new NgTableParams({

        }, {
            counts: [],
            getData: function (params) {

                return $http({
                    method: 'GET',
                    url: '/api/apps/formazione/ivass/realTime/' + $scope.selectedYear + '/' + $stateParams.table
                }).then(function (response) {

                    if (! response.data.success) {
                        return $handler.handle(response.data);
                    }

                    $scope.realtimeTotal = response.data.data.total;

                    return response.data.data.items;

                });

            }
        });

    });

})(angular, angular.module('ivass', [

]));