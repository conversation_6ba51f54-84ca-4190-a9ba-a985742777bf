/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('227', 'Target 3', 'Target3', 'OFF', 'OFF', 'OFF', '2019-11-01', '2020-02-14');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_227;
CREATE TABLE IF NOT EXISTS iniz_227 (
	agenzia_id					CHAR(4) NOT NULL,
	premiDimMultiInv			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiFree			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiTarget			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiDouble			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimQuo					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimSilver				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiProgOpen				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiInv			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiFree			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiTarget			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiDouble			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimQuo					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimSilver				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziProgOpen				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiInv		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiFree		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiDouble		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimQuo				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimSilver			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompProgOpen			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz227 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_227;
CREATE VIEW vw_iniz_227 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDimMultiInv,
	st.premiDimMultiFree,
	st.premiDimMultiTarget,
	st.premiDimMultiDouble,
	st.premiDimQuo,
	st.premiDimSilver,
	st.premiProgOpen,
	st.premiTOTALI,
	st.pezziDimMultiInv,
	st.pezziDimMultiFree,
	st.pezziDimMultiTarget,
	st.pezziDimMultiDouble,
	st.pezziDimQuo,
	st.pezziDimSilver,
	st.pezziProgOpen,
	st.pezziTOTALI,
	st.premiCompDimMultiInv,
	st.premiCompDimMultiFree,
	st.premiCompDimMultiTarget,
	st.premiCompDimMultiDouble,
	st.premiCompDimQuo,
	st.premiCompDimSilver,
	st.premiCompProgOpen,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_227 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_227_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_227_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_227 (
		agenzia_id, objGAshow, objGAobjPremi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_227_set_status;
DELIMITER //
CREATE PROCEDURE iniz_227_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 227;
	DECLARE done 							INT DEFAULT 0;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE C_OBIETTIVO						MEDIUMINT UNSIGNED;
	DECLARE v_progress						TINYINT UNSIGNED;

	DECLARE v_premiDimMultiInv				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiFree				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiTarget			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiDouble			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimQuo					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimSilver				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiProgOpen					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziDimMultiInv				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiFree				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiTarget			SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiDouble			SMALLINT UNSIGNED;
	DECLARE v_pezziDimQuo					SMALLINT UNSIGNED;
	DECLARE v_pezziDimSilver				SMALLINT UNSIGNED;
	DECLARE v_pezziProgOpen					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;

	DECLARE v_premiCompDimMultiInv			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiFree			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiDouble		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimQuo				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimSilver			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompProgOpen				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi FROM iniz_227 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02100' SET done = 1;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

	SET C_INIZIATIVA_ID = 227;
	SET C_OBIETTIVO = 300;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiDimMultiInv = 0; SET v_premiDimMultiFree = 0; SET v_premiDimMultiTarget = 0; SET v_premiDimMultiDouble = 0; SET v_premiDimQuo = 0; SET v_premiDimSilver = 0; SET v_premiProgOpen = 0; SET v_premiTOTALI = 0;
		SET v_pezziDimMultiInv = 0; SET v_pezziDimMultiFree = 0; SET v_pezziDimMultiTarget = 0; SET v_pezziDimMultiDouble = 0; SET v_pezziDimQuo = 0; SET v_pezziDimSilver = 0; SET v_pezziProgOpen = 0; SET v_pezziTOTALI = 0;
		SET v_premiCompDimMultiInv = 0; SET v_premiCompDimMultiFree = 0; SET v_premiCompDimMultiTarget = 0; SET v_premiCompDimMultiDouble = 0; SET v_premiCompDimQuo = 0; SET v_premiCompDimSilver = 0; SET v_premiCompProgOpen = 0; SET v_premiCompTOTALI = 0;
		SET v_obiettivoOK = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		-- Dimensione Multivalore Inv (premioUnico == premioComputabile)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimMultiInv, v_premiCompDimMultiInv
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU07','MU15');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiInv
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU07','MU15');
		-- Dimensione Multivalore Free (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimMultiFree, v_premiCompDimMultiFree
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU06','MU13');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiFree
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU06','MU13');
		-- Dimensione Multivalore Target (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimMultiTarget, v_premiCompDimMultiTarget
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU09','MU14');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiTarget
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU09','MU14');
		-- Dimensione Multivalore Double (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimMultiDouble, v_premiCompDimMultiDouble
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU11','MU16');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiDouble
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('MU11','MU16');
		-- Dimensione Quota
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimQuo,v_premiCompDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';
		-- Dimensione Silver
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimSilver,v_premiCompDimSilver
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'PU08';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimSilver
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'PU08';
		-- Programma Open
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiProgOpen,v_premiCompProgOpen
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'FPA' AND premioComputabile >= 1200;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziProgOpen
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'FPA' AND premioComputabile >= 1200;

		-- totali & status

		SET v_premiTOTALI = v_premiDimMultiInv + v_premiDimMultiFree + v_premiDimMultiTarget + v_premiDimMultiDouble + v_premiDimQuo + v_premiDimSilver + v_premiProgOpen;
		SET v_premiCompTOTALI = v_premiCompDimMultiInv + v_premiCompDimMultiFree + v_premiCompDimMultiTarget + v_premiCompDimMultiDouble + v_premiCompDimQuo + v_premiCompDimSilver + v_premiCompProgOpen;
		SET v_pezziTOTALI = v_pezziDimMultiInv + v_pezziDimMultiFree + v_pezziDimMultiTarget + v_pezziDimMultiDouble + v_pezziDimQuo + v_pezziDimSilver + v_pezziProgOpen;

		-- incentivazione base
		IF (((v_premiCompTOTALI - v_premiCompProgOpen) * 0.006 + v_pezziProgOpen * 25.82) >= C_OBIETTIVO) THEN

			SET v_obiettivoOK = 1;
			SET v_importoErogTOTALE = v_premiCompDimQuo * 0.006 +
			                    v_premiCompDimSilver * 0.006 +
								v_premiCompDimMultiInv * 0.006 +
								v_premiCompDimMultiFree * 0.006 +
								v_premiCompDimMultiTarget * 0.006 +
								v_premiCompDimMultiDouble * 0.006 +
			                    v_pezziProgOpen * 25.82;
		END IF;

		-- PROGRESS BAR
		SET v_progress = LEAST(100, ((v_premiCompTOTALI - v_premiCompProgOpen) * 0.006 + v_pezziProgOpen * 25.82) / C_OBIETTIVO * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, NULL, NULL);

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_227 SET
			premiDimMultiInv = v_premiDimMultiInv, premiDimMultiFree = v_premiDimMultiFree, premiDimMultiTarget = v_premiDimMultiTarget, premiDimMultiDouble = v_premiDimMultiDouble, premiDimQuo = v_premiDimQuo, premiDimSilver = v_premiDimSilver, premiProgOpen = v_premiProgOpen, premiTOTALI = v_premiTOTALI,
			pezziDimMultiInv = v_pezziDimMultiInv, pezziDimMultiFree = v_pezziDimMultiFree, pezziDimMultiTarget = v_pezziDimMultiTarget, pezziDimMultiDouble = v_pezziDimMultiDouble, pezziDimQuo = v_pezziDimQuo, pezziDimSilver = v_pezziDimSilver, pezziProgOpen = v_pezziProgOpen, pezziTOTALI = v_pezziTOTALI,
			premiCompDimMultiInv = v_premiCompDimMultiInv, premiCompDimMultiFree = v_premiCompDimMultiFree, premiCompDimMultiTarget = v_premiCompDimMultiTarget, premiCompDimMultiDouble = v_premiCompDimMultiDouble, premiCompDimQuo = v_premiCompDimQuo, premiCompDimSilver = v_premiCompDimSilver, premiCompProgOpen = v_premiCompProgOpen, premiCompTOTALI = v_premiCompTOTALI,
			obiettivoOK = v_obiettivoOK, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
