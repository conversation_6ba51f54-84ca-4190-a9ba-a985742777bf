/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('196', 'Focus Cliente', 'FocusCliente', 'OFF', 'OFF', 'OFF', '2018-03-01', '2018-12-31');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_196_clienti;
DROP TABLE IF EXISTS iniz_196_myangel;
DROP TABLE IF EXISTS iniz_196;


CREATE TABLE IF NOT EXISTS iniz_196 (
	agenzia_id					CHAR(4) NOT NULL,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	clientiARGENTO				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiORO					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiPLATINO				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiARGENTO_F1			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F1_1P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F1_2P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiARGENTO_F2			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F2_1P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F2_2P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiARGENTO_F3			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F3_1P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiOROPLAT_F3_2P		SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiTotali				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	clientiMyAngel				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	percMyAngel					TINYINT(2) UNSIGNED NOT NULL DEFAULT 0,
	importoBase					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoBonusF1				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoBonusF2				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoBonusF3				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoMyAngel				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	-- objGAobjPremi			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	-- objGApercPremi			DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz196 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS iniz_196_clienti (
	agenzia_id					CHAR(4) NOT NULL,
	codiceCliente				VARCHAR(10) NOT NULL,
	nome						VARCHAR(100) NOT NULL,
	codFisc						VARCHAR(16) NOT NULL,
	segmento					ENUM('PLATINO','ORO','ARGENTO','MONOAREA','MONOAREA_AUTO','AREA_C') NOT NULL,
	sottosegmento				ENUM('AAA','AA','A') NOT NULL,
	punteggio					MEDIUMINT UNSIGNED NOT NULL DEFAULT 0,
	pezzi						SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF1_MyP					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF1_PLI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF1_MS					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF2_MyP					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF2_PLI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF2_CSC					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF3_MyP					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF3_CSC					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziF3_MS					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id, codiceCliente),
	CONSTRAINT fk_iniz196_clienti FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS iniz_196_myangel (
	agenzia_id					CHAR(4) NOT NULL,
	codiceCliente				VARCHAR(10) NOT NULL,
	tipo						VARCHAR(7) NOT NULL,
	CF_PIVA						VARCHAR(16) NOT NULL,
	data						DATE NOT NULL,
	nome						VARCHAR(30) NOT NULL,
	cognome						VARCHAR(30) NOT NULL,
	telefono					VARCHAR(16) NOT NULL,
	email						VARCHAR(30) NOT NULL,
	PRIMARY KEY (agenzia_id, codiceCliente),
	CONSTRAINT fk_iniz196_myangel FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_196;
CREATE VIEW vw_iniz_196 AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	i.premiTOTALI,
	i.pezziTOTALI,
	i.premiCompTOTALI,
	i.clientiARGENTO,
	i.clientiORO,
	i.clientiPLATINO,
	i.clientiARGENTO_F1,
	i.clientiOROPLAT_F1_1P,
	i.clientiOROPLAT_F1_2P,
	i.clientiARGENTO_F2,
	i.clientiOROPLAT_F2_1P,
	i.clientiOROPLAT_F2_2P,
	i.clientiARGENTO_F3,
	i.clientiOROPLAT_F3_1P,
	i.clientiOROPLAT_F3_2P,
	i.clientiTotali,
	i.clientiMyAngel,
	i.percMyAngel,
	i.importoBase,
	i.importoBonusF1,
	i.importoBonusF2,
	i.importoBonusF3,
	i.importoMyAngel,
	i.importoErogTOTALE,
	i.objGAshow,
--	i.objGAobjPremi,
	i.objGAobjPezzi,
--	i.objGApercPremi,
	i.objGApercPezzi,
	i.objGAstatus,
	i.objGApercStatus
FROM
	iniz_196 i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_196_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_196_insert_obiettivi (
	IN p_iniziativa_id	TINYINT UNSIGNED,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1) UNSIGNED,
	IN p_objGAobjPremi	DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_objGAobjPezzi	MEDIUMINT UNSIGNED,
	IN p_gruppo			CHAR(2),				-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2) UNSIGNED,	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,				-- NOT USED
	IN p_ptf			DECIMAL(12,2) UNSIGNED	-- NOT USED
)
BEGIN
	INSERT INTO iniz_196 (
		agenzia_id, objGAshow, objGAobjPezzi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPezzi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPezzi = p_objGAobjPezzi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_196_set_status;
DELIMITER //
CREATE PROCEDURE iniz_196_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 196;
	DECLARE done 							INT DEFAULT 0;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;

	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;
	DECLARE v_clientiARGENTO				SMALLINT UNSIGNED;
	DECLARE v_clientiORO					SMALLINT UNSIGNED;
	DECLARE v_clientiPLATINO				SMALLINT UNSIGNED;
	DECLARE v_clientiARGENTO_F1				SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F1_1P			SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F1_2P			SMALLINT UNSIGNED;
	DECLARE v_clientiARGENTO_F2				SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F2_1P			SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F2_2P			SMALLINT UNSIGNED;
	DECLARE v_clientiARGENTO_F3				SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F3_1P			SMALLINT UNSIGNED;
	DECLARE v_clientiOROPLAT_F3_2P			SMALLINT UNSIGNED;
	DECLARE v_clientiTotali					SMALLINT UNSIGNED;
	DECLARE v_clientiMyAngel				SMALLINT UNSIGNED;
	DECLARE v_percMyAngel					TINYINT(2) UNSIGNED;

	DECLARE v_importoBase					DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoBonusF1				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoBonusF2				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoBonusF3				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoMyAngel				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

--	DECLARE v_objGAobjPremi					DECIMAL(12,2);
	DECLARE v_objGAobjPezzi					SMALLINT;
--	DECLARE v_objGApercPremi				DECIMAL(4,1);
	DECLARE v_objGApercPezzi				DECIMAL(4,1);
	DECLARE v_objGAstatus					TINYINT;
	DECLARE v_objGApercStatus				DECIMAL(4,1);

	DECLARE v_segmento						ENUM('PLATINO','ORO','ARGENTO','MONOAREA','MONOAREA_AUTO','AREA_C');
	DECLARE v_codiceCliente					VARCHAR(10);
	DECLARE v_pezzi							SMALLINT UNSIGNED;
	DECLARE v_pezziF1_MyP					SMALLINT UNSIGNED;
	DECLARE v_pezziF1_PLI					SMALLINT UNSIGNED;
	DECLARE v_pezziF1_MS					SMALLINT UNSIGNED;
	DECLARE v_pezziF2_MyP					SMALLINT UNSIGNED;
	DECLARE v_pezziF2_PLI					SMALLINT UNSIGNED;
	DECLARE v_pezziF2_CSC					SMALLINT UNSIGNED;
	DECLARE v_pezziF3_MyP					SMALLINT UNSIGNED;
	DECLARE v_pezziF3_CSC					SMALLINT UNSIGNED;
	DECLARE v_pezziF3_MS					SMALLINT UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPezzi FROM iniz_196 ORDER BY agenzia_id;
	DECLARE cur_cliente CURSOR FOR
		SELECT agenzia_id, codiceCliente, segmento FROM iniz_196_clienti ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 196;

	SET done = 0;
	OPEN cur_cliente;
	FETCH cur_cliente INTO v_agenzia_id, v_codiceCliente, v_segmento;
	WHILE NOT done DO

		SELECT IFNULL(COUNT(*),0) INTO v_pezzi
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente;

		IF v_segmento = 'ARGENTO' AND v_pezzi > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente) < 80 THEN
			SET v_pezzi = 0;
		END IF;
		IF v_segmento IN ('ORO','PLATINO') AND v_pezzi > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente) < 180 THEN
			SET v_pezzi = 0;
		END IF;

		-- FASE 1: 2018-03-01 - 2018-05-31
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF1_MyP
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione <= '2018-05-31' AND codiceProdotto = '001021';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF1_PLI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione <= '2018-05-31' AND codiceProdotto = '000157';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF1_MS
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione <= '2018-05-31' AND codiceProdotto IN('13FA','13FC','13FU','13NA','13NC','13NU','15BC');

		IF v_segmento = 'ARGENTO' AND v_pezziF1_MyP+v_pezziF1_PLI+v_pezziF1_MS > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione <= '2018-05-31') < 100 THEN
			SET v_pezziF1_MyP = 0; SET v_pezziF1_PLI = 0; SET v_pezziF1_MS = 0;
		END IF;
		IF v_segmento IN ('ORO','PLATINO') AND v_pezziF1_MyP+v_pezziF1_PLI+v_pezziF1_MS > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione <= '2018-05-31') < 200 THEN
			SET v_pezziF1_MyP = 0; SET v_pezziF1_PLI = 0; SET v_pezziF1_MS = 0;
		END IF;

		-- FASE 2: 2018-06-01 - 2018-09-30
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF2_MyP
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-06-01' AND dataEmissione <= '2018-09-30' AND codiceProdotto = '001021';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF2_PLI
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-06-01' AND dataEmissione <= '2018-09-30' AND codiceProdotto = '000157';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF2_CSC
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-06-01' AND dataEmissione <= '2018-09-30' AND codiceProdotto = '000171';

		IF v_segmento = 'ARGENTO' AND v_pezziF2_MyP+v_pezziF2_PLI+v_pezziF2_CSC > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-06-01' AND dataEmissione <= '2018-09-30') < 100 THEN
			SET v_pezziF2_MyP = 0; SET v_pezziF2_PLI = 0; SET v_pezziF2_CSC = 0;
		END IF;
		IF v_segmento IN ('ORO','PLATINO') AND v_pezziF2_MyP+v_pezziF2_PLI+v_pezziF2_CSC > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-06-01' AND dataEmissione <= '2018-09-30') < 200 THEN
			SET v_pezziF2_MyP = 0; SET v_pezziF2_PLI = 0; SET v_pezziF2_CSC = 0;
		END IF;

		-- FASE 3: 2018-10-01 - 2018-12-31
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF3_MyP
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-10-01' AND dataEmissione <= '2018-12-31' AND codiceProdotto = '001021';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF3_CSC
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-10-01' AND dataEmissione <= '2018-12-31' AND codiceProdotto = '000171';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziF3_MS
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-10-01' AND dataEmissione <= '2018-12-31' AND codiceProdotto IN('CA18','CU18','DA18','DU18','15BC','001026','001025','001020');

		IF v_segmento = 'ARGENTO' AND v_pezziF3_MyP+v_pezziF3_CSC+v_pezziF3_MS > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-10-01' AND dataEmissione <= '2018-12-31') < 100 THEN
			SET v_pezziF3_MyP = 0; SET v_pezziF3_CSC = 0; SET v_pezziF3_MS = 0;
		END IF;
		IF v_segmento IN ('ORO','PLATINO') AND v_pezziF3_MyP+v_pezziF3_CSC+v_pezziF3_MS > 0 AND (SELECT SUM(premioComputabile) FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente AND dataEmissione >= '2018-10-01' AND dataEmissione <= '2018-12-31') < 200 THEN
			SET v_pezziF3_MyP = 0; SET v_pezziF3_CSC = 0; SET v_pezziF3_MS = 0;
		END IF;


		UPDATE iniz_196_clienti SET
			pezzi = v_pezzi,
			pezziF1_MyP = v_pezziF1_MyP, pezziF1_PLI = v_pezziF1_PLI, pezziF1_MS = v_pezziF1_MS,
			pezziF2_MyP = v_pezziF2_MyP, pezziF2_PLI = v_pezziF2_PLI, pezziF2_CSC = v_pezziF2_CSC,
			pezziF3_MyP = v_pezziF3_MyP, PezziF3_CSC = v_pezziF3_CSC, pezziF3_MS = v_pezziF3_MS
			WHERE agenzia_id = v_agenzia_id AND codiceCliente = v_codiceCliente;

		SET done = 0;
        FETCH cur_cliente INTO v_agenzia_id, v_codiceCliente, v_segmento;
    END WHILE;
    CLOSE cur_cliente;


	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPezzi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiTOTALI = 0; SET v_pezziTOTALI = 0; SET v_premiCompTOTALI = 0;
		SET v_clientiARGENTO = 0; SET v_clientiORO = 0; SET v_clientiPLATINO = 0;
		SET v_clientiARGENTO_F1 = 0; SET v_clientiOROPLAT_F1_1P = 0; SET v_clientiOROPLAT_F1_2P = 0;
		SET v_clientiTotali = 0; SET v_clientiMyAngel = 0; SET v_percMyAngel = 0;
		SET v_importoBase = 0; SET v_importoBonusF1 = 0; SET v_importoMyAngel = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPezzi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- pezzi & premi
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiTOTALI, v_premiCompTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		-- ARGENTO
		SELECT IFNULL(COUNT(*),0) INTO v_clientiARGENTO
			FROM iniz_196_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'ARGENTO' AND pezzi > 0;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiARGENTO_F1
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento = 'ARGENTO' AND ( pezziF1_MyP > 0 OR pezziF1_PLI > 0 OR pezziF1_MS > 0);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiARGENTO_F2
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento = 'ARGENTO' AND ( pezziF2_MyP > 0 OR pezziF2_PLI > 0 OR pezziF2_CSC > 0);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiARGENTO_F3
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento = 'ARGENTO' AND ( pezziF3_MyP > 0 OR pezziF3_CSC > 0 OR pezziF3_MS > 0);
		-- ORO / PLATINO
		SELECT IFNULL(COUNT(*),0) INTO v_clientiORO
			FROM iniz_196_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'ORO' AND pezzi > 0;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiPLATINO
			FROM iniz_196_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'PLATINO' AND pezzi > 0;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F1_1P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND ( pezziF1_MyP > 0 OR pezziF1_PLI > 0 OR pezziF1_MS > 0);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F1_2P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND (
				(pezziF1_MyP > 0 AND pezziF1_PLI > 0) OR
				(pezziF1_MyP > 0 AND pezziF1_MS > 0) OR
				(pezziF1_PLI > 0 AND pezziF1_MS > 0)
			);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F2_1P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND ( pezziF2_MyP > 0 OR pezziF2_PLI > 0 OR pezziF2_CSC > 0);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F2_2P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND (
				(pezziF2_MyP > 0 AND pezziF2_PLI > 0) OR
				(pezziF2_MyP > 0 AND pezziF2_CSC > 0) OR
				(pezziF2_PLI > 0 AND pezziF2_CSC > 0)
			);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F3_1P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND ( pezziF3_MyP > 0 OR pezziF3_CSC > 0 OR pezziF3_MS > 0);
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOROPLAT_F3_2P
			FROM iniz_196_clienti
			WHERE agenzia_id = v_agenzia_id AND segmento IN ('ORO','PLATINO') AND (
				(pezziF3_MyP > 0 AND pezziF3_CSC > 0) OR
				(pezziF3_MyP > 0 AND pezziF3_MS > 0) OR
				(pezziF3_CSC > 0 AND pezziF3_MS > 0)
			);
		SET v_clientiOROPLAT_F1_1P = v_clientiOROPLAT_F1_1P - v_clientiOROPLAT_F1_2P;
		SET v_clientiOROPLAT_F2_1P = v_clientiOROPLAT_F2_1P - v_clientiOROPLAT_F2_2P;
		SET v_clientiOROPLAT_F3_1P = v_clientiOROPLAT_F3_1P - v_clientiOROPLAT_F3_2P;

		-- My Angel
		SELECT COUNT(*) INTO v_clientiMyAngel
			FROM iniz_196_myangel m LEFT JOIN iniz_196_clienti c ON m.codiceCliente = c.codiceCliente
			WHERE c.agenzia_id = v_agenzia_id AND m.agenzia_id = v_agenzia_id;

		-- totali & status
		SET v_clientiTotali = v_clientiARGENTO + v_clientiORO + v_clientiPLATINO;
		SET v_percMyAngel = v_clientiMyAngel / v_clientiTotali * 100;

		SET v_importoBase = v_clientiARGENTO * 20 + (v_clientiORO + v_clientiPLATINO) * 40;

		SET v_importoBonusF1 = (v_clientiARGENTO_F1 + v_clientiOROPLAT_F1_1P) * 5 +
								v_clientiOROPLAT_F1_2P * 10;
		SET v_importoBonusF2 = (v_clientiARGENTO_F2 + v_clientiOROPLAT_F2_1P) * 5 +
								v_clientiOROPLAT_F2_2P * 10;
		SET v_importoBonusF3 = (v_clientiARGENTO_F3 + v_clientiOROPLAT_F3_1P) * 5 +
								v_clientiOROPLAT_F3_2P * 10;

		IF v_percMyAngel >= 30 THEN
			SET v_importoMyAngel = v_clientiMyAngel * 5;
		END IF;

		SET v_importoErogTOTALE = v_importoBase + v_importoBonusF1 + v_importoBonusF2 + v_importoBonusF3 + v_importoMyAngel;

		-- PROGRESS BAR
		SET v_progress = LEAST(100, v_importoBase * 100); -- AS IF BOOLEAN: 0 or 100
		SET v_progress2 = LEAST(100, v_percMyAngel / 20 * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, NULL);

		-- GA OBJ
--		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
		SET v_objGApercPezzi = v_clientiTotali / v_objGAobjPezzi * 100;
--		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		SET v_objGApercStatus = v_clientiTotali / ( v_objGAobjPezzi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_196 SET
			premiTOTALI = v_premiTOTALI, pezziTOTALI = v_pezziTOTALI, premiCompTOTALI = v_premiCompTOTALI,
			clientiARGENTO = v_clientiARGENTO, clientiORO = v_clientiORO, clientiPLATINO = v_clientiPLATINO,
			clientiARGENTO_F1 = v_clientiARGENTO_F1, clientiOROPLAT_F1_1P = v_clientiOROPLAT_F1_1P, clientiOROPLAT_F1_2P = v_clientiOROPLAT_F1_2P,
			clientiARGENTO_F2 = v_clientiARGENTO_F2, clientiOROPLAT_F2_1P = v_clientiOROPLAT_F2_1P, clientiOROPLAT_F2_2P = v_clientiOROPLAT_F2_2P,
			clientiARGENTO_F3 = v_clientiARGENTO_F3, clientiOROPLAT_F3_1P = v_clientiOROPLAT_F3_1P, clientiOROPLAT_F3_2P = v_clientiOROPLAT_F3_2P,
			clientiTotali = v_clientiTotali, clientiMyAngel = v_clientiMyAngel, percMyAngel = v_percMyAngel,
			importoBase = v_importoBase, importoBonusF1 = v_importoBonusF1, importoBonusF2 = v_importoBonusF2, importoBonusF3 = v_importoBonusF3, importoMyAngel = v_importoMyAngel, importoErogTOTALE = v_importoErogTOTALE,
--			objGApercPremi = v_objGApercPremi,
			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPezzi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
