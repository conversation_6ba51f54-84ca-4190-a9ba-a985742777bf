/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS `iniz_23`;
CREATE TABLE IF NOT EXISTS `iniz_23` (
	`agenzia_id`						char(4) NOT NULL,
	`gruppo`						CHAR(2) NOT NULL,
	`obiettivo`						MEDIUMINT unsigned NOT NULL COMMENT 'xtype=MONETARY',
	`numeroPolizze`					MEDIUMINT unsigned NOT NULL default 0,
	`totPremiComputabili`			DECIMAL(12,2) unsigned NOT NULL COMMENT 'xtype=MONETARY',
	`percentualeObiettivo`			DECIMAL(5,1) unsigned NOT NULL,
	`importoErogabile`				DECIMAL(12,2) unsigned NOT NULL COMMENT 'xtype=MONETARY',
	PRIMARY KEY (`agenzia_id`),
	CONSTRAINT `fk_iniz23` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS `vw_iniz_23`;
CREATE VIEW `vw_iniz_23` AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.numeroPolizze,
	st.totPremiComputabili,
	st.percentualeObiettivo,
	SIGN(TRUNCATE((st.percentualeObiettivo/100),0)) AS obiettivoOK,
	st.importoErogabile
FROM
	`iniz_23` st
	LEFT JOIN `agenzie` ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS `iniz_23_insert_obiettivi`;
DELIMITER //
CREATE PROCEDURE `iniz_23_insert_obiettivi` (
	IN p_iniziativa_id	TINYINT, /* NOT USED */
	IN p_idCompagnia	CHAR(1),
	IN p_idAgenzia		CHAR(3),
	IN p_gruppo			CHAR(15),
	IN p_obiettivo		MEDIUMINT
)
BEGIN
	DECLARE p_agenzia_id CHAR(4);
	SET p_agenzia_id = CONCAT(p_idCompagnia, p_idAgenzia);

	INSERT INTO `iniz_23` (
		agenzia_id,
		gruppo, obiettivo
	) VALUES (
		p_agenzia_id,
		p_gruppo, p_obiettivo
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivo = p_obiettivo
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS `iniz_23_set_status`;
DELIMITER //
CREATE PROCEDURE `iniz_23_set_status` (
)
BEGIN
	DECLARE C_INIZIATIVA_ID				INT default 23;
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_gruppo					CHAR(2);
	DECLARE v_obiettivo					MEDIUMINT;
	DECLARE v_numeroPolizze				MEDIUMINT;
	DECLARE v_totPremiComputabili		DECIMAL(12,2);
	DECLARE v_percentualeObiettivo		DECIMAL(5,1);
	DECLARE v_importoErogabile			DECIMAL(12,2);
	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT `agenzia_id` FROM `iniz_23` ORDER BY `agenzia_id`;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	/* PROCEDURE */
	SET C_INIZIATIVA_ID = 23;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id;
	WHILE NOT done DO
		/* set default values */
		SET v_totPremiComputabili = 0; SET v_importoErogabile = 0;
		/* get other values */
		SELECT gruppo, obiettivo INTO v_gruppo, v_obiettivo
			FROM `iniz_23` WHERE agenzia_id = v_agenzia_id;
		IF v_gruppo IS NULL THEN SET v_gruppo = 0; END IF;
		IF v_obiettivo IS NULL THEN SET v_obiettivo = 0; END IF;
		SELECT COUNT(*) INTO v_numeroPolizze
			FROM `iniz_polizze` WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		SELECT SUM(`premioComputabile`) INTO v_totPremiComputabili
			FROM `iniz_polizze` WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		IF v_totPremiComputabili IS NULL THEN SET v_totPremiComputabili = 0; END IF;
		/* calculate */
		SET v_percentualeObiettivo = v_totPremiComputabili / v_obiettivo * 100;
		IF v_percentualeObiettivo >= 100 THEN SET v_importoErogabile = v_totPremiComputabili * 0.04; END IF;
		/* update tables */
		UPDATE `iniz_23` SET
			numeroPolizze = v_numeroPolizze, totPremiComputabili = v_totPremiComputabili, percentualeObiettivo = v_percentualeObiettivo, importoErogabile = v_importoErogabile
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;

