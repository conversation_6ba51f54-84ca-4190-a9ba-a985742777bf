/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('191', 'Pronti al lancio 2018', 'ProntiAlLancio2018', 'OFF', 'OFF', 'OFF', '2018-02-01', '2018-05-31');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_191;
CREATE TABLE IF NOT EXISTS iniz_191 (
	agenzia_id					char(4) NOT NULL,
	gruppo						CHAR(1) NOT NULL,
	ptfInizio					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	ptfFine						DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPezzi				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivo1					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	obiettivo2					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziPlInf					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziMsPremium				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziMsDomus				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziMsEasy					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziMsElisir				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiPlInf					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiMsPremium				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiMsDomus				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiMsEasy					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiMsElisir				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompPlInf				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompMsPemium			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompMsDomus			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompMsEasy				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompMsElisir			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivo1OK				TINYINT(1) UNSIGNED NOT NULL default 0,
	obiettivo2OK				TINYINT(1) UNSIGNED NOT NULL default 0,
	perc						TINYINT(2) UNSIGNED NOT NULL default 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL default 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz191 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_191;
CREATE VIEW vw_iniz_191 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.ptfInizio,
	st.ptfFine,
	st.obiettivoPezzi,
	st.obiettivo1,
	st.obiettivo2,
	st.pezziPlInf,
	st.pezziMsPremium,
	st.pezziMsDomus,
	st.pezziMsEasy,
	st.pezziMsElisir,
	st.pezziTOTALI,
	st.premiPlInf,
	st.premiMsPremium,
	st.premiMsDomus,
	st.premiMsEasy,
	st.premiMsElisir,
	st.premiTOTALI,
	st.premiCompPlInf,
	st.premiCompMsPemium,
	st.premiCompMsDomus,
	st.premiCompMsEasy,
	st.premiCompMsElisir,
	st.premiCompTOTALI,
	st.obiettivo1OK,
	st.obiettivo2OK,
	st.perc,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus,
	geop.id		AS provincia_id,
	geop.nome	AS provincia,
	geor.id		AS regione_id,
	geor.nome	AS regione
FROM
	iniz_191 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
	LEFT JOIN geo_province geop ON ( ag.provincia_id = geop.id )
	LEFT JOIN geo_regioni geor ON ( ag.regione_id = geor.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_191_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_191_insert_obiettivi (
	IN p_iniziativa_id	TINYINT UNSIGNED,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1) UNSIGNED,
	IN p_objGAobjPremi	DECIMAL(12,2) UNSIGNED,
	IN p_objGAobjPezzi	MEDIUMINT UNSIGNED,		-- NOT USED
	IN p_gruppo			CHAR(1),
	IN p_objInizPremi	DECIMAL(12,2) UNSIGNED,
	IN p_objInizPezzi	MEDIUMINT UNSIGNED,
	IN p_ptf			DECIMAL(12,2) UNSIGNED
)
BEGIN
	DECLARE v_obiettivoPezzi	TINYINT UNSIGNED;
	DECLARE v_obiettivo1		SMALLINT UNSIGNED;
	DECLARE v_obiettivo2		SMALLINT UNSIGNED;

	CASE p_gruppo
		WHEN 1 THEN SET v_obiettivoPezzi = 4;	SET v_obiettivo1 = 2000;	SET v_obiettivo2 = 3000;
		WHEN 2 THEN SET v_obiettivoPezzi = 4;	SET v_obiettivo1 = 4000;	SET v_obiettivo2 = 5000;
		WHEN 3 THEN SET v_obiettivoPezzi = 5;	SET v_obiettivo1 = 5000;	SET v_obiettivo2 = 7000;
		WHEN 4 THEN SET v_obiettivoPezzi = 5;	SET v_obiettivo1 = 7000;	SET v_obiettivo2 = 10000;
		WHEN 5 THEN SET v_obiettivoPezzi = 6;	SET v_obiettivo1 = 10000;	SET v_obiettivo2 = 15000;
		WHEN 6 THEN SET v_obiettivoPezzi = 6;	SET v_obiettivo1 = 15000;	SET v_obiettivo2 = 20000;
		WHEN 7 THEN SET v_obiettivoPezzi = 6;	SET v_obiettivo1 = 22000;	SET v_obiettivo2 = 32000;
		WHEN 8 THEN SET v_obiettivoPezzi = 7;	SET v_obiettivo1 = 35000;	SET v_obiettivo2 = 45000;
	END CASE;
	-- delete obiettivoPezzi IF p_objInizPezzi = 0
	IF p_objInizPezzi = 0 THEN SET v_obiettivoPezzi = 0; END IF;

	INSERT INTO iniz_191 (
		agenzia_id, objGAshow, objGAobjPremi, gruppo, ptfInizio, ptfFine, obiettivoPezzi, obiettivo1, obiettivo2
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_gruppo, p_ptf, p_objInizPremi, v_obiettivoPezzi, v_obiettivo1, v_obiettivo2
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, gruppo = p_gruppo, ptfInizio = p_objInizPremi, ptfFine = p_ptf, obiettivoPezzi =  v_obiettivoPezzi, obiettivo1 = v_obiettivo1, obiettivo2 = v_obiettivo2
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_191_set_status;
DELIMITER //
CREATE PROCEDURE iniz_191_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					SMALLINT DEFAULT 191;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_DATI_FINALI					TINYINT UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;
	DECLARE v_progress3						TINYINT UNSIGNED;
	DECLARE v_ptfInizio						DECIMAL(12,2) UNSIGNED;
	DECLARE v_ptfFine						DECIMAL(12,2) UNSIGNED;
	DECLARE v_obiettivoPezzi				TINYINT UNSIGNED;
	DECLARE v_obiettivo1					SMALLINT UNSIGNED;
	DECLARE v_obiettivo2					SMALLINT UNSIGNED;
	DECLARE v_pezziPlInf					SMALLINT UNSIGNED;
	DECLARE v_pezziMsPremium				SMALLINT UNSIGNED;
	DECLARE v_pezziMsDomus					SMALLINT UNSIGNED;
	DECLARE v_pezziMsEasy					SMALLINT UNSIGNED;
	DECLARE v_pezziMsElisir					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiPlInf					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiMsPremium				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiMsDomus					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiMsEasy					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiMsElisir					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompPlInf				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompMsPemium				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompMsDomus				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompMsEasy				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompMsElisir				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;
	DECLARE v_obiettivo1OK					TINYINT UNSIGNED;
	DECLARE v_obiettivo2OK					TINYINT UNSIGNED;
	DECLARE v_perc							TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2);

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, ptfInizio, ptfFine, obiettivoPezzi, obiettivo1, obiettivo2, objGAobjPremi FROM iniz_191 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 191;

	-- get switch bonus ON/OFF
	SELECT datiFinali INTO C_DATI_FINALI FROM iniz WHERE id = C_INIZIATIVA_ID;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_ptfInizio, v_ptfFine, v_obiettivoPezzi, v_obiettivo1, v_obiettivo2, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_pezziPlInf = 0; SET v_pezziMsPremium = 0; SET v_pezziMsDomus = 0; SET v_pezziMsEasy = 0; SET v_pezziMsElisir = 0; SET v_pezziTOTALI = 0;
		SET v_premiPlInf = 0; SET v_premiMsPremium = 0; SET v_premiMsDomus = 0; SET v_premiMsEasy = 0; SET v_premiMsElisir = 0; SET v_premiTOTALI = 0;
		SET v_premiCompPlInf = 0; SET v_premiCompMsPemium = 0; SET v_premiCompMsDomus = 0; SET v_premiCompMsEasy = 0; SET v_premiCompMsElisir = 0; SET v_premiCompTOTALI = 0;
		SET v_obiettivo1OK = 0; SET v_obiettivo2OK = 0; SET v_perc = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- Pluriattiva Infortuni
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiPlInf, v_premiCompPlInf
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = '000157';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPlInf
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = '000157';
		-- Mente Serena Premium
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiMsPremium, v_premiCompMsPemium
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('13NC','13FC');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziMsPremium
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('13NC','13FC');
		-- Mente Serena Domus
		SELECT IFNULL(SUM(premioAnnuo+premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiMsDomus, v_premiCompMsDomus
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('13NA','13FA','13NU','13FU');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziMsDomus
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('13NA','13FA','13NU','13FU');
		-- Mente Serena Easy
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiMsEasy, v_premiCompMsEasy
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('15BC','1020');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziMsEasy
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('15BC','1020');
		-- Mente Serena Elisir
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiMsElisir, v_premiCompMsElisir
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'LTC0';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziMsElisir
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'LTC0';

		-- totali & status
		SET v_pezziTOTALI = v_pezziPlInf + v_pezziMsPremium + v_pezziMsDomus + v_pezziMsEasy + v_pezziMsElisir;
		SET v_premiTOTALI = v_premiPlInf + v_premiMsPremium + v_premiMsDomus + v_premiMsEasy + v_premiMsElisir;
		SET v_premiCompTOTALI = v_premiCompPlInf + v_premiCompMsPemium + v_premiCompMsDomus + v_premiCompMsEasy + v_premiCompMsElisir;

		-- solo pezzi TCM (MenteSerena)
		IF v_pezziMsPremium + v_pezziMsDomus + v_pezziMsEasy + v_pezziMsElisir >= v_obiettivoPezzi AND v_premiCompTOTALI >= v_obiettivo1 THEN
			SET v_obiettivo1OK = 1;
			SET v_perc = 8;
		END IF;
		-- solo pezzi TCM (MenteSerena)
		IF v_pezziMsPremium + v_pezziMsDomus + v_pezziMsEasy + v_pezziMsElisir >= v_obiettivoPezzi AND v_premiCompTOTALI >= v_obiettivo2 AND v_ptfFine >= v_ptfInizio AND C_DATI_FINALI THEN
			SET v_obiettivo2OK = 1;
			SET v_perc = 12;
		END IF;
		SET v_importoErogTOTALE = v_premiCompTOTALI * v_perc / 100;

		-- PROGRESS BAR
		SET v_progress = LEAST(100, (v_pezziMsPremium + v_pezziMsDomus + v_pezziMsEasy + v_pezziMsElisir) / v_obiettivoPezzi * 100);
		SET v_progress2 = LEAST(100, v_premiCompTOTALI / v_obiettivo1 * 100);
		SET v_progress3 = LEAST(100, v_premiCompTOTALI / v_obiettivo2 * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, v_progress3);

		-- GA OBJ
		SET v_objGApercPremi = v_premiCompTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiCompTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_191 SET
			pezziPlInf = v_pezziPlInf, pezziMsPremium = v_pezziMsPremium, pezziMsDomus = v_pezziMsDomus, pezziMsEasy = v_pezziMsEasy, pezziMsElisir = v_pezziMsElisir, pezziTOTALI = v_pezziTOTALI,
			premiPlInf = v_premiPlInf, premiMsPremium = v_premiMsPremium, premiMsDomus = v_premiMsDomus, premiMsEasy = v_premiMsEasy, premiMsElisir = v_premiMsElisir, premiTOTALI = v_premiTOTALI,
			premiCompPlInf = v_premiCompPlInf, premiCompMsPemium = v_premiCompMsPemium, premiCompMsDomus = v_premiCompMsDomus, premiCompMsEasy = v_premiCompMsEasy, premiCompMsElisir = v_premiCompMsElisir, premiCompTOTALI = v_premiCompTOTALI,
			obiettivo1OK = v_obiettivo1OK, obiettivo2OK = v_obiettivo2OK, perc = v_perc, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_ptfInizio, v_ptfFine, v_obiettivoPezzi, v_obiettivo1, v_obiettivo2, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
