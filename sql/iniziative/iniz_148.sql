/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, startDate, endDate) VALUES ('148', 'Doppia Chance Golf', 'DoppiaChanceGolf','OFF', 'OFF', 'OFF', '2016-09-22', '2016-12-31');
-- ALTER TABLE stats_users_access_daily  ADD I148 SMALLINT UNSIGNED NOT NULL DEFAULT 0 AFTER I147;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_148;
CREATE TABLE IF NOT EXISTS iniz_148 (
	agenzia_id					CHAR(4) NOT NULL,
	gruppo						TINYINT(1) UNSIGNED not NULL DEFAULT 0,
	premiDimMultiInv			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiFree			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiTarget			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimQuo					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiInv			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiFree			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiTarget			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimQuo					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiInv		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiFree		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimQuo				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	frazDimMultiInv_UL			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiFree_UL			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiTarget_UL		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	bonusUnitLinkedOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoProQuotaOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErog					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogBonusUnitLinked	DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogProQuota 		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz148 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_148;
CREATE VIEW vw_iniz_148 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.premiDimMultiInv,
	st.premiDimMultiFree,
	st.premiDimMultiTarget,
	st.premiDimQuo,
	st.premiTOTALI,
	st.pezziDimMultiInv,
	st.pezziDimMultiFree,
	st.pezziDimMultiTarget,
	st.pezziDimQuo,
	st.pezziTOTALI,
	st.premiCompDimMultiInv,
	st.premiCompDimMultiFree,
	st.premiCompDimMultiTarget,
	st.premiCompDimQuo,
	st.premiCompTOTALI,
	st.frazDimMultiInv_UL,
	st.frazDimMultiFree_UL,
	st.frazDimMultiTarget_UL,
	st.obiettivoOK,
	st.bonusUnitLinkedOK,
	st.obiettivoProQuotaOK,
	st.importoErog,
	st.importoErogBonusUnitLinked,
	st.importoErogProQuota,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_148 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_148_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_148_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_148 (
		agenzia_id, objGAshow, objGAobjPremi, gruppo
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_gruppo
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, gruppo = p_gruppo
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_148_set_status;
DELIMITER //
CREATE PROCEDURE iniz_148_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 148;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_BONUS_MULTI_INV				DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_FREE				DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_TARGET			DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_QUO						DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS							DECIMAL(2,1) UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_gruppo						TINYINT UNSIGNED;
	DECLARE C_OBIETTIVO						MEDIUMINT UNSIGNED;
	DECLARE C_SOGLIA_PROQUOTA				MEDIUMINT UNSIGNED;

	DECLARE v_premiDimMultiInv				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiFree				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiTarget			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimQuo					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziDimMultiInv				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiFree				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiTarget			SMALLINT UNSIGNED;
	DECLARE v_pezziDimQuo					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;

	DECLARE v_premiCompDimMultiInv			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiFree			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimQuo				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiInv_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiFree_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiTarget_UL			DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_bonusUnitLinkedOK				TINYINT UNSIGNED;
	DECLARE v_obiettivoProQuotaOK			TINYINT UNSIGNED;
	DECLARE v_importoErog					DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogBonusUnitLinked	DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogProQuota			DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, gruppo, objGAobjPremi FROM iniz_148 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 148;
	SET C_OBIETTIVO = 40000;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_gruppo, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiDimMultiInv = 0; SET v_premiDimMultiFree = 0; SET v_premiDimMultiTarget = 0; SET v_premiDimQuo = 0; SET v_premiTOTALI = 0;
		SET v_pezziDimMultiInv = 0; SET v_pezziDimMultiFree = 0; SET v_pezziDimMultiTarget = 0; SET v_pezziDimQuo = 0; SET v_pezziTOTALI = 0;
		SET v_premiCompDimMultiInv = 0; SET v_premiCompDimMultiFree = 0; SET v_premiCompDimMultiTarget = 0; SET v_premiCompDimQuo = 0; SET v_premiCompTOTALI = 0;
		SET v_frazDimMultiInv_UL = 0; SET v_frazDimMultiFree_UL = 0; SET v_frazDimMultiTarget_UL = 0;
		SET v_obiettivoOK = 0; SET v_bonusUnitLinkedOK = 0; SET v_obiettivoProQuotaOK = 0;
		SET v_importoErog = 0; SET v_importoErogBonusUnitLinked = 0; SET v_importoErogProQuota = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		-- Dimensione Multivalore Inv (premioUnico == premioComputabile)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiInv, v_premiCompDimMultiInv, v_frazDimMultiInv_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU07';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiInv
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU07';
		-- Dimensione Multivalore Free (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiFree, v_premiCompDimMultiFree, v_frazDimMultiFree_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU06';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiFree
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU06';
		-- Dimensione Multivalore Target (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiTarget, v_premiCompDimMultiTarget, v_frazDimMultiTarget_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU09';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiTarget
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU09';
		-- Dimensione Quota
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimQuo,v_premiCompDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';

		-- totali & status

		SET v_premiTOTALI = v_premiDimMultiInv + v_premiDimMultiFree + v_premiDimMultiTarget + v_premiDimQuo;
		SET v_premiCompTOTALI = v_premiCompDimMultiInv + v_premiCompDimMultiFree + v_premiCompDimMultiTarget + v_premiCompDimQuo;
		SET v_pezziTOTALI = v_pezziDimMultiInv + v_pezziDimMultiFree + v_pezziDimMultiTarget + v_pezziDimQuo;

		-- incentivazione base
		IF v_premiTOTALI >= C_OBIETTIVO THEN
			SET v_obiettivoOK = 1;

			IF v_premiTOTALI >= 250001 THEN
				SET C_BONUS_MULTI_INV = 1.0; SET C_BONUS_QUO = 1.4; SET C_BONUS_MULTI_FREE = 1.0; SET C_BONUS_MULTI_TARGET = 1.4;
			ELSEIF v_premiTOTALI >= 120001 THEN
				SET C_BONUS_MULTI_INV = 0.8; SET C_BONUS_QUO = 1.2; SET C_BONUS_MULTI_FREE = 0.8; SET C_BONUS_MULTI_TARGET = 1.2;
			ELSEIF v_premiTOTALI >= 40000 THEN
				SET C_BONUS_MULTI_INV = 0.6; SET C_BONUS_QUO = 1.0; SET C_BONUS_MULTI_FREE = 0.6; SET C_BONUS_MULTI_TARGET = 1.0;
			END IF;

			SET v_importoErog = v_premiCompDimMultiInv * C_BONUS_MULTI_INV/100 +
								v_premiCompDimQuo * C_BONUS_QUO/100 +
								v_premiCompDimMultiFree * C_BONUS_MULTI_FREE/100 +
								v_premiCompDimMultiTarget * C_BONUS_MULTI_TARGET/100;

			-- Bonus Unit-Linked
			IF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL >= 150000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.30;
			ELSEIF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL >= 75000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.20;
			ELSEIF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL >= 25000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.10;
			END IF;

			SET v_importoErogTOTALE = v_importoErog + v_importoErogBonusUnitLinked;
		END IF;

		-- incentivazione Unit-Linked Pro-Quota
		IF v_premiTOTALI < C_OBIETTIVO AND v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL >= 20000 THEN
			SET v_obiettivoProQuotaOK = 1;
			SET v_importoErogProQuota = v_frazDimMultiInv_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.006 +
										v_frazDimMultiFree_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.006 +
										v_frazDimMultiTarget_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.010 +
										v_premiCompDimQuo * v_premiCompTOTALI / C_OBIETTIVO * 0.010;
			SET v_importoErogTOTALE = v_importoErogProQuota;
		END IF;

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_148 SET
			premiDimMultiInv = v_premiDimMultiInv, premiDimMultiFree = v_premiDimMultiFree, premiDimMultiTarget = v_premiDimMultiTarget, premiDimQuo = v_premiDimQuo, premiTOTALI = v_premiTOTALI,
			pezziDimMultiInv = v_pezziDimMultiInv, pezziDimMultiFree = v_pezziDimMultiFree, pezziDimMultiTarget = v_pezziDimMultiTarget, pezziDimQuo = v_pezziDimQuo, pezziTOTALI = v_pezziTOTALI,
			premiCompDimMultiInv = v_premiCompDimMultiInv, premiCompDimMultiFree = v_premiCompDimMultiFree, premiCompDimMultiTarget = v_premiCompDimMultiTarget, premiCompDimQuo = v_premiCompDimQuo, premiCompTOTALI = v_premiCompTOTALI,
			frazDimMultiInv_UL = v_frazDimMultiInv_UL, frazDimMultiFree_UL = v_frazDimMultiFree_UL, frazDimMultiTarget_UL = v_frazDimMultiTarget_UL,
			obiettivoOK = v_obiettivoOK, bonusUnitLinkedOK = v_bonusUnitLinkedOK, obiettivoProQuotaOK = v_obiettivoProQuotaOK,
			importoErog = v_importoErog, importoErogBonusUnitLinked = v_importoErogBonusUnitLinked, importoErogProQuota = v_importoErogProQuota, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_gruppo, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
