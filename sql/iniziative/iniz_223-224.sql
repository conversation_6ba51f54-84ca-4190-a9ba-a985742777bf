/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('223', 'Double Protection', 'DoubleProtection', 'OFF', 'OFF', 'OFF', '2019-05-15', '2019-12-31');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_223;
CREATE TABLE IF NOT EXISTS iniz_223 (
	agenzia_id					CHAR(4) NOT NULL,
	ptfInizioPLINF				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	ptfInizioTCM				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	ptfFinePLINF				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	ptfFineTCM					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPezzi				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	premiPLINF					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTCM					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziPLINF					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziPLINF250				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziPLINF350				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziPLINF500				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTCM					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTCM250					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTCM300					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTCM500					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivoPezziPLINFOK		TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPezziTCMOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPtfPLINFOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoPtfTCMOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErogPLINF			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTCM				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz223 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_223;
CREATE VIEW vw_iniz_223 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.ptfInizioPLINF,
	st.ptfInizioTCM,
	st.ptfFinePLINF,
	st.ptfFineTCM,
	st.obiettivoPezzi,
	st.premiPLINF,
	st.premiTCM,
	st.premiTOTALI,
	st.pezziPLINF,
	st.pezziPLINF250,
	st.pezziPLINF350,
	st.pezziPLINF500,
	st.pezziTCM,
	st.pezziTCM250,
	st.pezziTCM300,
	st.pezziTCM500,
	st.pezziTOTALI,
	st.premiCompTOTALI,
	st.obiettivoPezziPLINFOK,
	st.obiettivoPezziTCMOK,
	st.obiettivoPtfPLINFOK,
	st.obiettivoPtfTCMOK,
	st.importoErogPLINF,
	st.importoErogTCM,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_223 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_223_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_223_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)
)
BEGIN
	INSERT INTO iniz_223 (
		agenzia_id, objGAshow, objGAobjPremi, ptfFinePLINF, ptfInizioPLINF, obiettivoPezzi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_objInizPremi, p_ptf, p_objInizPezzi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, ptfFinePLINF = p_objInizPremi, ptfInizioPLINF = p_ptf, obiettivoPezzi = p_objInizPezzi
	;
END; //
DELIMITER ;

DROP PROCEDURE IF EXISTS iniz_224_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_224_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)
)
BEGIN
	INSERT INTO iniz_223 (
		agenzia_id, objGAshow, objGAobjPremi, ptfFineTCM, ptfInizioTCM
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_objInizPremi, p_ptf
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, ptfFineTCM = p_objInizPremi, ptfInizioTCM = p_ptf
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_223_set_status;
DELIMITER //
CREATE PROCEDURE iniz_223_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 223;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_DATI_FINALI					TINYINT UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;
	DECLARE v_ptfInizioPLINF				DECIMAL(12,2) UNSIGNED;
	DECLARE v_ptfInizioTCM					DECIMAL(12,2) UNSIGNED;
	DECLARE v_ptfFinePLINF					DECIMAL(12,2) UNSIGNED;
	DECLARE v_ptfFineTCM					DECIMAL(12,2) UNSIGNED;
	DECLARE C_OBIETTIVO_PEZZI				TINYINT UNSIGNED;

	DECLARE v_premiPLINF					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTCM						DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziPLINF					SMALLINT UNSIGNED;
	DECLARE v_pezziPLINF250					SMALLINT UNSIGNED;
	DECLARE v_pezziPLINF350					SMALLINT UNSIGNED;
	DECLARE v_pezziPLINF500					SMALLINT UNSIGNED;
	DECLARE v_pezziTCM						SMALLINT UNSIGNED;
	DECLARE v_pezziTCM250					SMALLINT UNSIGNED;
	DECLARE v_pezziTCM300					SMALLINT UNSIGNED;
	DECLARE v_pezziTCM500					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoPezziPLINFOK			TINYINT UNSIGNED;
	DECLARE v_obiettivoPezziTCMOK			TINYINT UNSIGNED;
	DECLARE v_obiettivoPtfPLINFOK			TINYINT UNSIGNED;
	DECLARE v_obiettivoPtfTCMOK				TINYINT UNSIGNED;

	DECLARE v_importoErogPLINF				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogTCM				DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi, ptfInizioPLINF, ptfInizioTCM, ptfFinePLINF, ptfFineTCM FROM iniz_223 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 223;
	SET C_OBIETTIVO_PEZZI = 5;

	-- get switch bonus ON/OFF
	SELECT datiFinali INTO C_DATI_FINALI FROM iniz WHERE id = C_INIZIATIVA_ID;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi, v_ptfInizioPLINF, v_ptfInizioTCM, v_ptfFinePLINF, v_ptfFineTCM;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiPLINF = 0; SET v_premiTCM = 0; SET v_premiTOTALI = 0;
		SET v_pezziPLINF = 0; SET v_pezziPLINF250 = 0; SET v_pezziPLINF350 = 0; SET v_pezziPLINF500 = 0; SET v_pezziTCM = 0; SET v_pezziTCM250 = 0; SET v_pezziTCM300 = 0; SET v_pezziTCM500 = 0; SET v_pezziTOTALI = 0;
		SET v_premiCompTOTALI = 0;
		SET v_obiettivoPezziPLINFOK = 0; SET v_obiettivoPezziTCMOK = 0; SET v_obiettivoPtfPLINFOK = 0; SET v_obiettivoPtfTCMOK = 0;
		SET v_importoErogPLINF = 0; SET v_importoErogTCM = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		SELECT IFNULL(SUM(premioComputabile),0) INTO v_premiPLINF
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto = '157' AND premioAnnuo >= 250;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPLINF
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto = '157' AND premioAnnuo >= 250;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPLINF250
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto = '157' AND premioAnnuo >= 250 AND premioAnnuo < 350;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPLINF350
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto = '157' AND premioAnnuo >= 350 AND premioAnnuo < 500;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPLINF500
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto = '157' AND premioAnnuo >= 500;

		SELECT IFNULL(SUM(premioComputabile),0) INTO v_premiTCM
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('CA18','CU18','DA18','DU18') AND ((premioAnnuo >= 300 OR premioUnico >= 3000) OR ( (premioAnnuo >= 250 OR premioUnico >= 2500) AND dataIncasso >= '2019-11-01'));
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTCM -- mancano i TCM250 novembre/dicembre
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('CA18','CU18','DA18','DU18') AND (premioAnnuo >= 300 OR premioUnico >= 3000);
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTCM250
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('CA18','CU18','DA18','DU18') AND ((premioAnnuo >= 250 AND premioAnnuo < 300) OR (premioUnico >= 2500 AND premioUnico < 3000)) AND dataIncasso >= '2019-11-01';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTCM300
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('CA18','CU18','DA18','DU18') AND ((premioAnnuo >= 300 AND premioAnnuo < 500) OR (premioUnico >= 3000 AND premioUnico < 5000));
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTCM500
			FROM iniz_polizze WHERE iniziativa_id IN(C_INIZIATIVA_ID, 224) AND agenzia_id = v_agenzia_id AND codiceProdotto IN ('CA18','CU18','DA18','DU18') AND (premioAnnuo >= 500 OR premioUnico >= 5000);
		SET v_pezziTCM = v_pezziTCM + v_pezziTCM250;

		-- incentivazione

	    SET v_pezziTOTALI = v_pezziPLINF + v_pezziTCM;

		IF v_pezziPLINF >= C_OBIETTIVO_PEZZI THEN SET v_obiettivoPezziPLINFOK = 1; END IF;
	    IF v_ptfFinePLINF >= v_ptfInizioPLINF THEN SET v_obiettivoPtfPLINFOK = 1; END IF;
	    IF v_obiettivoPezziPLINFOK AND v_obiettivoPtfPLINFOK THEN
			SET v_importoErogPLINF = 15 * v_pezziPLINF250 + 25 * v_pezziPLINF350 + 40 * v_pezziPLINF500;
			IF C_DATI_FINALI AND v_ptfFinePLINF <= v_ptfInizioPLINF * 1.05 THEN SET v_importoErogPLINF = v_importoErogPLINF * 1.10; END IF;
			IF C_DATI_FINALI AND v_ptfFinePLINF > v_ptfInizioPLINF * 1.05 THEN SET v_importoErogPLINF = v_importoErogPLINF * 1.20; END IF;
		END IF;

	    IF v_pezziTCM >= C_OBIETTIVO_PEZZI THEN SET v_obiettivoPezziTCMOK = 1; END IF;
	    IF v_ptfFineTCM >= v_ptfInizioTCM THEN SET v_obiettivoPtfTCMOK = 1; END IF;
		IF v_obiettivoPezziTCMOK AND v_obiettivoPtfTCMOK THEN
			SET v_importoErogTCM = 20 * v_pezziTCM250 + 30 * v_pezziTCM300 + 50 * v_pezziTCM500;
		END IF;

	    SET v_importoErogTOTALE = v_importoErogPLINF + v_importoErogTCM;

		-- PROGRESS BAR
		SET v_progress = LEAST(100, v_pezziTOTALI / C_OBIETTIVO_PEZZI * 100);
		SET v_progress2 = LEAST(100, (v_ptfFinePLINF + v_ptfFineTCM) / (v_ptfInizioPLINF + v_ptfInizioTCM) * 1.01);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, NULL);

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_223 SET
			premiPLINF = v_premiPLINF, premiTCM = v_premiTCM, premiTOTALI = v_premiTOTALI,
			pezziPLINF = v_pezziPLINF, pezziPLINF250 = v_pezziPLINF250, pezziPLINF350 = v_pezziPLINF350, pezziPLINF500 = v_pezziPLINF500, pezziTCM = v_pezziTCM, pezziTCM250 = v_pezziTCM250, pezziTCM300 = v_pezziTCM300, pezziTCM500 = v_pezziTCM500, pezziTOTALI = v_pezziTOTALI,
			premiCompTOTALI = v_premiCompTOTALI,
			obiettivoPezziPLINFOK = v_obiettivoPezziPLINFOK, obiettivoPezziTCMOK = v_obiettivoPezziTCMOK,
			obiettivoPtfPLINFOK = v_obiettivoPtfPLINFOK, obiettivoPtfTCMOK = v_obiettivoPtfTCMOK,
			importoErogPLINF = v_importoErogPLINF, importoErogTCM = v_importoErogTCM, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi, v_ptfInizioPLINF, v_ptfInizioTCM, v_ptfFinePLINF, v_ptfFineTCM;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
