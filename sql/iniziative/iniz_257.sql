/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('257', 'Rinnoviamoci', 'Rinnoviamoci', 'OFF', 'OFF', 'OFF', '2021-03-01', '2021-05-31');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_257;
CREATE TABLE IF NOT EXISTS iniz_257 (
	agenzia_id					CHAR(4) NOT NULL,
	tassoRinnovo				DECIMAL(3,1) UNSIGNED NOT NULL DEFAULT 0,
	variazionePremi				DECIMAL(3,1) SIGNED NOT NULL DEFAULT 0,
	tenutaPtf					DECIMAL(3,1) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivo1OK				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivo2OK				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivo3OK				TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,

	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz257 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_257;
CREATE VIEW vw_iniz_257 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.tassoRinnovo,
	st.variazionePremi,
	st.tenutaPtf,
	st.premiTOTALI,
	st.pezziTOTALI,
	st.premiCompTOTALI,
	st.obiettivo1OK,
	st.obiettivo2OK,
	st.obiettivo3OK,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_257 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_257_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_257_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_257 (
		agenzia_id, objGAshow, objGAobjPremi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_257_set_status;
DELIMITER //
CREATE PROCEDURE iniz_257_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 257;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_DATI_FINALI					TINYINT UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;
	DECLARE v_tassoRinnovo					DECIMAL(3,1) UNSIGNED;
	DECLARE v_variazionePremi				DECIMAL(3,1) SIGNED;
	DECLARE v_tenutaPtf						DECIMAL(3,1) UNSIGNED;


	DECLARE v_pezziPtf						SMALLINT UNSIGNED;
	DECLARE v_premiPtf						DECIMAL(12,2) UNSIGNED;

	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivo1OK					TINYINT UNSIGNED;
	DECLARE v_obiettivo2OK					TINYINT UNSIGNED;
	DECLARE v_obiettivo3OK					TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi  FROM iniz_257 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 257;

	-- get switch bonus ON/OFF
	SELECT datiFinali INTO C_DATI_FINALI FROM iniz WHERE id = C_INIZIATIVA_ID;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiTOTALI = 0; SET v_premiPtf = 0;
		SET v_pezziTOTALI = 0; SET v_pezziPtf = 0;
		SET v_premiCompTOTALI = 0;
		SET v_obiettivo1OK = 0; SET v_obiettivo2OK = 0; SET v_obiettivo3OK = 0;
		SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiPtf, v_premiTOTALI, v_premiCompTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND dataIncasso > 0;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziPtf -- pezzi rinnovabili
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTOTALI -- pezzi rinnovati
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND dataIncasso > 0;

		-- incentivazione
		SET v_tassoRinnovo = v_pezziTOTALI / v_pezziPtf * 100;
		SET v_variazionePremi = v_premiTOTALI / v_premiPtf * 100 -100;
		SET v_tenutaPtf = v_tassoRinnovo + v_variazionePremi;

		IF v_tenutaPtf >= 80 THEN
			SET v_obiettivo1OK = 1;
			SET v_importoErogTOTALE = (v_premiTOTALI * LEAST(v_tenutaPtf, 88)/100 - v_premiTOTALI * 0.80) * 0.10;
		END IF;
		IF v_tenutaPtf >= 88 THEN
			SET v_obiettivo2OK = 1;
			SET v_importoErogTOTALE = v_importoErogTOTALE + (v_premiTOTALI * LEAST(v_tenutaPtf, 95)/100 - v_premiTOTALI * 0.88) * 0.15;
		END IF;
		IF v_tenutaPtf >= 95 THEN
			SET v_obiettivo3OK = 1;
			SET v_importoErogTOTALE = v_importoErogTOTALE + (v_premiTOTALI * LEAST(v_tenutaPtf, 100)/100 - v_premiTOTALI * 0.95) * 0.20;
		END IF;


		-- PROGRESS BAR
		SET v_progress = LEAST(100, v_pezziTOTALI / v_pezziPtf * 100);
		SET v_progress2 = LEAST(100, v_premiTOTALI / v_premiPtf * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, NULL);

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_257 SET
			tassoRinnovo = v_tassoRinnovo, variazionePremi = v_variazionePremi, tenutaPtf = v_tenutaPtf,
			premiTOTALI = v_premiTOTALI, pezziTOTALI = v_pezziTOTALI, premiCompTOTALI = v_premiCompTOTALI,
			obiettivo1OK = v_obiettivo1OK, obiettivo2OK = v_obiettivo2OK, obiettivo3OK = v_obiettivo3OK,
			importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
