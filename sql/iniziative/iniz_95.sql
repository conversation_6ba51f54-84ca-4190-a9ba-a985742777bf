/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, startDate, endDate) VALUES ('95', 'Uno+Uno 2014', 'Uno+Uno', 'OFF', 'OFF', 'OFF', '2014-10-01', '2014-12-31');
-- ALTER TABLE stats_users_access_daily  ADD I95 SMALLINT UNSIGNED NOT NULL DEFAULT '0' AFTER I92;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_95;
CREATE TABLE IF NOT EXISTS iniz_95 (
	agenzia_id					char(4) NOT NULL,
	obiettivo					smallint unsigned not NULL default 0,
	clientiArgento				smallint unsigned not NULL default 0,
	clientiOro					smallint unsigned not NULL default 0,
	clientiPlatino				smallint unsigned not NULL default 0,
	clientiOttobre				smallint unsigned not NULL default 0,
	clientiNovembre				smallint unsigned not NULL default 0,
	clientiDicembre				smallint unsigned not NULL default 0,
	clientiTOTALI				smallint unsigned not NULL default 0,
	obiettivoOK					tinyint(1) unsigned not NULL default 0,
	bonusContinuitaOK			tinyint(1) unsigned not NULL default 0,
	bonusCompagniaOK			tinyint(1) unsigned not NULL default 0,
	importoErog2014Base			DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErog2014Totale		DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErog2015				DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErog2016				DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErogTOTALE			DECIMAL(12,2) unsigned NOT NULL default 0,
	objGAshow					TINYINT(1) unsigned NOT NULL default 0,
	-- objGAobjPremi				DECIMAL(12,2) unsigned NOT NULL default 0,
	objGAobjPezzi				SMALLINT unsigned NOT NULL default 0,
	-- objGApercPremi				DECIMAL(4,1) unsigned NOT NULL default 0,
	objGApercPezzi				DECIMAL(4,1) unsigned NOT NULL default 0,
	objGAstatus					TINYINT unsigned NOT NULL default 0,
	objGApercStatus				DECIMAL(4,1) unsigned NOT NULL default 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz95 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS iniz_95_intermediari;
CREATE TABLE IF NOT EXISTS iniz_95_intermediari (
	user_id						mediumint unsigned NOT NULL,
	clienti						smallint unsigned not NULL default 0,
	createdAt					TIMESTAMP NOT NULL default CURRENT_TIMESTAMP,
	PRIMARY KEY (user_id),
	CONSTRAINT fk_iniz95_intermediari FOREIGN KEY (user_id) REFERENCES users (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS iniz_95_clienti;
CREATE TABLE IF NOT EXISTS iniz_95_clienti (
	agenzia_id					char(4) NOT NULL,
	codEsazione					varchar(6) NULL default NULL,
	nome						varchar(100) not NULL,
	codFisc						varchar(16) NOT NULL,
	segmento					enum('PLATINO','ORO','ARGENTO','MONOAREA','MONOAREA_AUTO','AREA_C') not NULL,
	sottosegmento				enum('AAA','AA','A') not NULL,
	punteggio					mediumint unsigned not NULL default 0,
	mese						tinyint unsigned not NULL default 0,
	PRIMARY KEY (agenzia_id, codFisc),
	CONSTRAINT fk_iniz95_clienti FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


SET FOREIGN_KEY_CHECKS=1;

/* ======================= TRIGGERS ======================================================= */

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_95;
CREATE VIEW vw_iniz_95 AS
SELECT
	i.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	i.obiettivo,
	i.clientiArgento,
	i.clientiOro,
	i.clientiPlatino,
	i.clientiOttobre,
	i.clientiNovembre,
	i.clientiDicembre,
	i.clientiTOTALI,
	i.obiettivoOK,
	i.bonusContinuitaOK,
	i.bonusCompagniaOK,
	i.importoErog2014Base,
	i.importoErog2014Totale,
	i.importoErog2015,
	i.importoErog2016,
	i.importoErogTOTALE,
	i.objGAshow,
--	i.objGAobjPremi,
	i.objGAobjPezzi,
--	i.objGApercPremi,
	i.objGApercPezzi,
	i.objGAstatus,
	i.objGApercStatus
FROM
	iniz_95 i
	LEFT JOIN agenzie ag ON ( i.agenzia_id = ag.id )
;

DROP VIEW IF EXISTS vw_iniz_95_intermediari;
CREATE VIEW vw_iniz_95_intermediari AS
SELECT
	u.agenzia_id,
	ag.area,
	ag.district,
	i.user_id,
	u.nome,
	u.cognome,
	u.codEsazione,
	i.clienti,
	i.createdAt
FROM
	iniz_95_intermediari i
	LEFT JOIN users u ON (i.user_id = u.id)
	LEFT JOIN agenzie ag ON ( u.agenzia_id = ag.id )

;

DROP VIEW IF EXISTS vw_iniz_95_iscritti;
CREATE VIEW vw_iniz_95_iscritti AS
SELECT
	u.agenzia_id,
	u.id,
	u.nome,
	u.cognome,
	u.codEsazione,
	u.email,
	SIGN(i.user_id) AS enabled
FROM
	users u
	LEFT JOIN iniz_95_intermediari i ON ( u.id = i.user_id)
	WHERE u.active = 1 AND u.type = 'INTERMEDIARIO'
;


/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_95_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_95_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objGAobjPezzi	MEDIUMINT,
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_95 (
		agenzia_id, objGAshow, objGAobjPezzi, obiettivo
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPezzi, p_objInizPezzi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPezzi = p_objGAobjPezzi, obiettivo = p_objInizPezzi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_95_set_status;
DELIMITER //
CREATE PROCEDURE iniz_95_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT default 95;
	DECLARE done 							INT default 0;
	DECLARE C_NUOVI_CLIENTI_COMPAGNIA		MEDIUMINT UNSIGNED;

	DECLARE v_agenzia_id					char(4);
	DECLARE v_obiettivo						smallint unsigned;

	DECLARE v_clientiArgento				smallint unsigned;
	DECLARE v_clientiOro					smallint unsigned;
	DECLARE v_clientiPlatino				smallint unsigned;
	DECLARE v_clientiOttobre				smallint unsigned;
	DECLARE v_clientiNovembre				smallint unsigned;
	DECLARE v_clientiDicembre				smallint unsigned;
	DECLARE v_clientiTOTALI					smallint unsigned;

	DECLARE v_obiettivoOK					tinyint(1) unsigned;
	DECLARE v_bonusContinuitaOK				tinyint(1) unsigned;
	DECLARE v_bonusCompagniaOK				tinyint(1) unsigned;
	DECLARE v_importoErog2014Base			DECIMAL(12,2);
	DECLARE v_importoErog2014Totale			DECIMAL(12,2);
	DECLARE v_importoErog2015				DECIMAL(12,2);
	DECLARE v_importoErog2016				DECIMAL(12,2);
	DECLARE v_importoErogTOTALE				DECIMAL(12,2);

--	DECLARE v_objGAobjPremi					DECIMAL(12,2);
	DECLARE v_objGAobjPezzi					SMALLINT;
--	DECLARE v_objGApercPremi				DECIMAL(4,1);
	DECLARE v_objGApercPezzi				DECIMAL(4,1);
	DECLARE v_objGAstatus					TINYINT;
	DECLARE v_objGApercStatus				DECIMAL(4,1);

	DECLARE p_user_id						mediumint unsigned;
	DECLARE p_codEsazione					varchar(6);
	DECLARE v_clienti						smallint unsigned;

	DECLARE v_codFisc						varchar(16);
	DECLARE v_mese							tinyint unsigned;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, obiettivo, objGAobjPezzi FROM iniz_95 ORDER BY agenzia_id;
	DECLARE cur_intermediario CURSOR FOR
		SELECT user_id, agenzia_id, codEsazione FROM vw_iniz_95_intermediari ORDER BY agenzia_id;
	DECLARE cur_clienti CURSOR FOR
		SELECT agenzia_id, codFisc FROM iniz_95_clienti ORDER BY agenzia_id, codFisc;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 95;

	-- calculate global clients
	SELECT COUNT(*) INTO C_NUOVI_CLIENTI_COMPAGNIA FROM iniz_95_clienti;
	UPDATE iniz SET c1 = C_NUOVI_CLIENTI_COMPAGNIA WHERE id = C_INIZIATIVA_ID;

	SET done = 0;
	OPEN cur_intermediario;
	FETCH cur_intermediario INTO p_user_id, v_agenzia_id, p_codEsazione;
	WHILE NOT done DO

		SELECT COUNT(*) INTO v_clienti
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND codEsazione = p_codEsazione;

		UPDATE iniz_95_intermediari SET clienti = v_clienti WHERE user_id = p_user_id;

		SET done = 0;
        FETCH cur_intermediario INTO p_user_id, v_agenzia_id, p_codEsazione;
    END WHILE;
    CLOSE cur_intermediario;

	-- assign clients months
	SET done = 0;
	OPEN cur_clienti;
	FETCH cur_clienti INTO v_agenzia_id, v_codFisc;
	WHILE NOT done DO

		SELECT MONTH(dataIncasso) INTO v_mese
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND CF_PIVA = v_codFisc ORDER BY dataIncasso LIMIT 1;

		UPDATE iniz_95_clienti SET mese = v_mese WHERE agenzia_id = v_agenzia_id AND codFisc = v_codFisc;

		SET done = 0;
        FETCH cur_clienti INTO v_agenzia_id, v_codFisc;
    END WHILE;
    CLOSE cur_clienti;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo, v_objGAobjPezzi;
	WHILE NOT done DO
		-- set default values
		SET v_clientiArgento = 0; SET v_clientiOro = 0; SET v_clientiPlatino = 0; SET v_clientiOttobre = 0; SET v_clientiNovembre = 0; SET v_clientiDicembre = 0; SET v_clientiTOTALI = 0;
		SET v_obiettivoOK = 0; SET v_bonusContinuitaOK = 0; SET v_bonusCompagniaOK = 0;
		SET v_importoErog2014Base = 0; SET v_importoErog2014Totale = 0; SET v_importoErog2015 = 0; SET v_importoErog2016 = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPezzi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count CLIENTI
		SELECT IFNULL(COUNT(*),0) INTO v_clientiArgento
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'ARGENTO';
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOro
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'ORO';
		SELECT IFNULL(COUNT(*),0) INTO v_clientiPlatino
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND segmento = 'PLATINO';
		SELECT IFNULL(COUNT(*),0) INTO v_clientiOttobre
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND mese = 10;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiNovembre
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND mese = 11;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiDicembre
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id AND mese = 12;
		SELECT IFNULL(COUNT(*),0) INTO v_clientiTOTALI
			FROM iniz_95_clienti WHERE agenzia_id = v_agenzia_id;

		-- totali & status
		IF v_clientiTOTALI >= v_obiettivo THEN
			SET v_obiettivoOK = 1;
			SET v_importoErog2014Base = 40 * v_clientiTOTALI;
			SET v_importoErog2015 = 25 * v_clientiTOTALI;
			SET v_importoErog2016 = 25 * v_clientiTOTALI;
			IF v_clientiOttobre >= 2 AND v_clientiNovembre >= 2 AND v_clientiDicembre >= 2 THEN
				SET v_bonusContinuitaOK = 1;
			END IF;
			IF C_NUOVI_CLIENTI_COMPAGNIA >= 4000 THEN
				SET v_bonusCompagniaOK = 1;
			END IF;
			SET v_importoErog2014Totale = v_importoErog2014Base + v_importoErog2014Base * 0.10 * v_bonusContinuitaOK + v_importoErog2014Base * 0.10 * v_bonusCompagniaOK;
			SET v_importoErogTOTALE = v_importoErog2014Totale + v_importoErog2015 + v_importoErog2016;
		END IF;

		-- GA OBJ
--		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
		SET v_objGApercPezzi = v_clientiTOTALI / v_objGAobjPezzi * 100;
--		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		SET v_objGApercStatus = v_clientiTOTALI / ( v_objGAobjPezzi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_95 SET
			clientiArgento = v_clientiArgento, clientiOro = v_clientiOro, clientiPlatino = v_clientiPlatino,
			clientiOttobre = v_clientiOttobre, clientiNovembre = v_clientiNovembre, clientiDicembre = v_clientiDicembre,
			clientiTOTALI = v_clientiTOTALI,
			obiettivoOK = v_obiettivoOK, bonusContinuitaOK = v_bonusContinuitaOK, bonusCompagniaOK = v_bonusCompagniaOK,
			importoErog2014Base = v_importoErog2014Base, importoErog2014Totale = v_importoErog2014Totale,
			importoErog2015 = v_importoErog2015, importoErog2016 = v_importoErog2016,
			importoErogTOTALE = v_importoErogTOTALE,
--			objGApercPremi = v_objGApercPremi,
			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo, v_objGAobjPezzi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
