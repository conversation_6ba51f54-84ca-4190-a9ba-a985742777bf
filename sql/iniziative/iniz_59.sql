/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, statusAMM, statusAGT, statusINT) VALUES ('59', 'DoppioRelax', 'OFF', 'OFF', 'OFF');
-- ALTER TABLE stats_users_access_daily  ADD I59 SMALLINT UNSIGNED NOT NULL DEFAULT '0' AFTER I58;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_59;
CREATE TABLE IF NOT EXISTS iniz_59 (
	agenzia_id				char(4) NOT NULL,
	gruppo					TINYINT(1),
	obiettivo				TINYINT unsigned NOT NULL default 0,

	polizzeQAC				MEDIUMINT unsigned NOT NULL default 0,
	premiQAC				DECIMAL(12,2) unsigned NOT NULL,
	polizzeDOI				MEDIUMINT unsigned NOT NULL default 0,
	premiDOI				DECIMAL(12,2) unsigned NOT NULL,

	polizzeTOT				MEDIUMINT unsigned NOT NULL default 0,
	premiTOT				DECIMAL(12,2) unsigned NOT NULL,
	obiettivoOK				TINYINT(1) unsigned NOT NULL default 0,
	importoErogabile		DECIMAL(12,2) unsigned NOT NULL,
	PRIMARY KEY (agenzia_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='I59 StatusAgenzie';
ALTER TABLE iniz_59
	ADD CONSTRAINT fk_iniz59_agenzie FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
;


SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_59;
CREATE VIEW vw_iniz_59 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.gruppo,
	st.obiettivo,
	st.polizzeQAC,
	st.premiQAC,
	st.polizzeDOI,
	st.premiDOI,
	st.polizzeTOT,
	st.premiTOT,
	st.obiettivoOK,
	st.importoErogabile
FROM
	iniz_59 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_59_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_59_insert_obiettivi (
	IN p_iniziativa_id	TINYINT, /* NOT USED */
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),		/* NOT USED */
	IN p_objGApremi		DECIMAL(12,2),	/* NOT USED */
	IN p_objGApezzi		MEDIUMINT,		/* NOT USED */
	IN p_gruppo			CHAR(2),
	IN p_objInizPremi	DECIMAL(12,2),	/* NOT USED */
	IN p_objInizPezzi	MEDIUMINT
)
BEGIN
	INSERT IGNORE INTO iniz_59 (
		agenzia_id, gruppo, obiettivo
	) VALUES (
		p_agenzia_id, p_gruppo, p_objInizPezzi
	) ON DUPLICATE KEY UPDATE
		gruppo = p_gruppo, obiettivo = p_objInizPezzi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_59_set_status;
DELIMITER //
CREATE PROCEDURE iniz_59_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID				INT default 59;
	DECLARE C_BONUS_FISSO				DECIMAL(3,2);
	DECLARE C_BONUS_PERC				DECIMAL(3,2);
	DECLARE done 						INT default 0;
	DECLARE v_agenzia_id				CHAR(4);

	DECLARE v_obiettivo					TINYINT;
	DECLARE v_polizzeQAC				MEDIUMINT;
	DECLARE v_premiQAC					DECIMAL(12,2);
	DECLARE v_polizzeDOI				MEDIUMINT;
	DECLARE v_premiDOI					DECIMAL(12,2);

	DECLARE v_polizzeTOT				MEDIUMINT;
	DECLARE v_premiTOT					DECIMAL(12,2);
	DECLARE v_obiettivoOK				TINYINT;
	DECLARE v_importoErogabile			DECIMAL(12,2);

	/* CURSORS  */
	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, obiettivo FROM iniz_59 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	/* PROCEDURE */
	SET C_INIZIATIVA_ID = 59;
	SET C_BONUS_FISSO = 7.50;
	SET C_BONUS_PERC = 0.05;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo;
	WHILE NOT done DO
		/* set default values */
		SET v_polizzeQAC = 0; SET v_premiQAC = 0; SET v_polizzeDOI = 0; SET v_premiDOI = 0;
		SET v_polizzeTOT = 0; SET v_premiTOT = 0; SET v_obiettivoOK = 0; SET v_importoErogabile = 0;

		SELECT COUNT(*) INTO v_polizzeQAC							-- QuiAbitoCasa
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'T';
		IF v_polizzeQAC IS NULL THEN SET v_polizzeQAC = 0; END IF;
		SELECT SUM(premioComputabile) INTO v_premiQAC
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'T';
		IF v_premiQAC IS NULL THEN SET v_premiQAC = 0; END IF;

		SELECT COUNT(*) INTO v_polizzeDOI							-- Doimo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'C';
		IF v_polizzeDOI IS NULL THEN SET v_polizzeDOI = 0; END IF;
		SELECT SUM(premioComputabile) INTO v_premiDOI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'C';
		IF v_premiDOI IS NULL THEN SET v_premiDOI = 0; END IF;

		SET v_polizzeTOT = v_polizzeQAC + v_polizzeDOI;
		SET v_premiTOT = v_premiQAC + v_premiDOI;
		IF (v_polizzeTOT >= v_obiettivo) THEN
			SET v_obiettivoOK = 1;
			IF v_premiQAC > (v_polizzeQAC * 117) THEN SET v_importoErogabile = C_BONUS_FISSO * v_polizzeTOT + ( v_premiQAC - v_polizzeQAC * 117 ) * C_BONUS_PERC;
			ELSE SET v_importoErogabile = C_BONUS_FISSO * v_polizzeTOT;
			END IF;
		END IF;

		/* update tables */
		UPDATE iniz_59 SET
			polizzeQAC = v_polizzeQAC, premiQAC = v_premiQAC, polizzeDOI = v_polizzeDOI, premiDOI = v_premiDOI,
			polizzeTOT = v_polizzeTOT, premiTOT = v_premiTOT,
			obiettivoOK = v_obiettivoOK, importoErogabile = v_importoErogabile
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
		FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo;
	END WHILE;
	CLOSE cur_agenzia;

END; //
DELIMITER ;
