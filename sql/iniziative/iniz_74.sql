/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, startDate, endDate) VALUES ('74', 'Dieci Piu', 'DieciPiu', 'OFF', 'OFF', 'OFF', '2013-09-01', '2013-12-31');
-- ALTER TABLE stats_users_access_daily  ADD I74 SMALLINT UNSIGNED NOT NULL DEFAULT '0' AFTER I73;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_74;
CREATE TABLE IF NOT EXISTS iniz_74 (
	agenzia_id					char(4) NOT NULL,
	pezziMS						SMALLINT unsigned NOT NULL default 0,
	pezziDimStu					SMALLINT unsigned NOT NULL default 0,
	pezziDimRis					SMALLINT unsigned NOT NULL default 0,
	pezziDimPro					SMALLINT unsigned NOT NULL default 0,
	pezziDimTut					SMALLINT unsigned NOT NULL default 0,
	pezziDIM					SMALLINT unsigned NOT NULL default 0,
	pezziTOTALI					SMALLINT unsigned NOT NULL default 0,
	premiMS						DECIMAL(12,2) unsigned NOT NULL default 0,
	premiDimStu					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiDimRis					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiDimPro					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiDimTut					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiDIM					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiTOTALI					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompMS					DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompDimStu				DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompDimRis				DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompDimPro				DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompDimTut				DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompDIM				DECIMAL(12,2) unsigned NOT NULL default 0,
	premiCompTOTALI				DECIMAL(12,2) unsigned NOT NULL default 0,
	obiettivoOK					TINYINT(1) unsigned NOT NULL default 0,
	bonusOK						TINYINT(1) unsigned NOT NULL default 0,
	bonusPerc					TINYINT(1) unsigned NOT NULL default 0,
	importoErogMS				DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErogDIM				DECIMAL(12,2) unsigned NOT NULL default 0,
	importoErogabile			DECIMAL(12,2) unsigned NOT NULL default 0,
	objGAshow					TINYINT(1) unsigned NOT NULL default 0,
	objGAobjPremi				DECIMAL(12,2) unsigned NOT NULL default 0,
--	objGAobjPezzi				SMALLINT unsigned NOT NULL default 0,
	objGApercPremi				DECIMAL(4,1) unsigned NOT NULL default 0,
--	objGApercPezzi				DECIMAL(4,1) unsigned NOT NULL default 0,
	objGAstatus					TINYINT unsigned NOT NULL default 0,
	objGApercStatus				DECIMAL(4,1) unsigned NOT NULL default 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz74 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_74;
CREATE VIEW vw_iniz_74 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.pezziMS,
	st.pezziDimStu,
	st.pezziDimRis,
	st.pezziDimPro,
	st.pezziDimTut,
	st.pezziDIM,
	st.pezziTOTALI,
	st.premiMS,
	st.premiDimStu,
	st.premiDimRis,
	st.premiDimPro,
	st.premiDimTut,
	st.premiDIM,
	st.premiTOTALI,
	st.premiCompMS,
	st.premiCompDimStu,
	st.premiCompDimRis,
	st.premiCompDimPro,
	st.premiCompDimTut,
	st.premiCompDIM,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.bonusOK,
	st.bonusPerc,
	st.importoErogMS,
	st.importoErogDIM,
	st.importoErogabile,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_74 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_74_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_74_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(1),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_74 (
		agenzia_id, objGAshow, objGAobjPremi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_74_set_status;
DELIMITER //
CREATE PROCEDURE iniz_74_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT default 74;
	DECLARE done 							INT default 0;

	DECLARE v_agenzia_id					CHAR(4);

	DECLARE v_pezziMS						SMALLINT unsigned;
	DECLARE v_pezziDimStu					SMALLINT unsigned;
	DECLARE v_pezziDimRis					SMALLINT unsigned;
	DECLARE v_pezziDimPro					SMALLINT unsigned;
	DECLARE v_pezziDimTut					SMALLINT unsigned;
	DECLARE v_pezziDIM						SMALLINT unsigned;
	DECLARE v_pezziTOTALI					SMALLINT unsigned;

	DECLARE v_premiMS						DECIMAL(12,2) unsigned;
	DECLARE v_premiDimStu					DECIMAL(12,2) unsigned;
	DECLARE v_premiDimRis					DECIMAL(12,2) unsigned;
	DECLARE v_premiDimPro					DECIMAL(12,2) unsigned;
	DECLARE v_premiDimTut					DECIMAL(12,2) unsigned;
	DECLARE v_premiDIM						DECIMAL(12,2) unsigned;
	DECLARE v_premiTOTALI					DECIMAL(12,2) unsigned;

	DECLARE v_premiCompMS					DECIMAL(12,2) unsigned;
	DECLARE v_premiCompDimStu				DECIMAL(12,2) unsigned;
	DECLARE v_premiCompDimRis				DECIMAL(12,2) unsigned;
	DECLARE v_premiCompDimPro				DECIMAL(12,2) unsigned;
	DECLARE v_premiCompDimTut				DECIMAL(12,2) unsigned;
	DECLARE v_premiCompDIM					DECIMAL(12,2) unsigned;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) unsigned;

	DECLARE v_obiettivoOK					TINYINT unsigned;
	DECLARE v_bonusOK						TINYINT unsigned;
	DECLARE v_bonusPerc						TINYINT unsigned;
	DECLARE v_importoErogMS					DECIMAL(12,2);
	DECLARE v_importoErogDIM				DECIMAL(12,2);
	DECLARE v_importoErogabile				DECIMAL(12,2);

	DECLARE v_objGAobjPremi					DECIMAL(12,2);
--	DECLARE v_objGAobjPezzi					SMALLINT;
	DECLARE v_objGApercPremi				DECIMAL(4,1);
--	DECLARE v_objGApercPezzi				DECIMAL(4,1);
	DECLARE v_objGAstatus					TINYINT;
	DECLARE v_objGApercStatus				DECIMAL(4,1);

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi FROM iniz_74 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 74;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
	WHILE NOT done DO
		-- set default values
		SET v_pezziMS = 0; SET v_pezziDimStu = 0; SET v_pezziDimRis = 0; SET v_pezziDimPro = 0; SET v_pezziDimTut = 0; SET v_pezziDIM = 0; SET v_pezziTOTALI = 0;
		SET v_premiMS = 0; SET v_premiDimStu = 0; SET v_premiDimRis = 0; SET v_premiDimPro = 0; SET v_premiDimTut = 0; SET v_premiDIM = 0; SET v_premiTOTALI = 0;
		SET v_premiCompMS = 0; SET v_premiCompDimStu = 0; SET v_premiCompDimRis = 0; SET v_premiCompDimPro = 0; SET v_premiCompDimTut = 0; SET v_premiCompDIM = 0; SET v_premiCompTOTALI = 0;
		SET v_obiettivoOK = 0; SET v_bonusOK = 0; SET v_bonusPerc = 0; SET v_importoErogMS = 0; SET v_importoErogDIM = 0; SET v_importoErogabile = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- Mente Serena
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiMS, v_premiCompMS
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'TCM';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziMS
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND famigliaProdotto = 'TCM';
		-- DimensionePlus Studi
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimStu, v_premiCompDimStu
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('ST12','SR12');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimStu
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('ST12','SR12');
		-- DimensionePlus Risparmio
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimRis, v_premiCompDimRis
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('CC12','CR12');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimRis
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('CC12','CR12');
		-- DimensionePlus Protezione
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimPro, v_premiCompDimPro
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MC12','MR12');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimPro
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MC12','MR12');
		-- DimensionePlus Tutela
		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimTut, v_premiCompDimTut
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('VC12','VR12');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimTut
		FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('VC12','VR12');

		-- totali & status
		SET v_pezziDIM = v_pezziDimStu + v_pezziDimRis + v_pezziDimPro + v_pezziDimTut;
		SET v_pezziTOTALI = v_pezziMS + v_pezziDIM;
		SET v_premiDIM = v_premiDimStu + v_premiDimRis + v_premiDimPro + v_premiDimTut;
		SET v_premiTOTALI = v_premiMS + v_premiDIM;
		SET v_premiCompDIM = v_premiCompDimStu + v_premiCompDimRis + v_premiCompDimPro + v_premiCompDimTut;
		SET v_premiCompTOTALI = v_premiCompMS + v_premiCompDIM;

		IF v_pezziTOTALI >= 10 AND v_premiCompTOTALI >= 4000 THEN

			SET v_obiettivoOK = 1;

			SET v_importoErogDIM = v_premiCompDIM * 0.07;

			IF v_premiCompMS >= 4000 THEN SET v_bonusPerc = 12; SET v_bonusOK = 1;
			ELSEIF v_premiCompMS >= 2000 THEN SET v_bonusPerc = 10; SET v_bonusOK = 1;
			ELSE SET v_bonusPerc = 7;
			END IF;
			SET v_importoErogMS = v_premiCompMS * v_bonusPerc / 100;

			SET v_importoErogabile = v_importoErogMS + v_importoErogDIM;

		END IF;


		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_74 SET
			pezziMS = v_pezziMS, pezziDimStu = v_pezziDimStu, pezziDimRis = v_pezziDimRis, pezziDimPro = v_pezziDimPro, pezziDimTut = v_pezziDimTut, pezziDIM = v_pezziDIM, pezziTOTALI = v_pezziTOTALI,
			premiMS = v_premiMS, premiDimStu = v_premiDimStu, premiDimRis = v_premiDimRis, premiDimPro = v_premiDimPro, premiDimTut = v_premiDimTut, premiDIM = v_premiDIM, premiTOTALI = v_premiTOTALI,
			premiCompMS = v_premiCompMS, premiCompDimStu = v_premiCompDimStu, premiCompDimRis = v_premiCompDimRis, premiCompDimPro = v_premiCompDimPro, premiCompDimTut = v_premiCompDimTut, premiCompDIM = v_premiCompDIM, premiCompTOTALI = v_premiCompTOTALI,
			obiettivoOK = v_obiettivoOK, bonusOK = v_bonusOK, bonusPerc = v_bonusPerc,
			importoErogMS = v_importoErogMS, importoErogDIM = v_importoErogDIM, importoErogabile = v_importoErogabile,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
