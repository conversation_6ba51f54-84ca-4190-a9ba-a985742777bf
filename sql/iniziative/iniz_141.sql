/* ======================= SETUP ======================================================== */

-- INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, startDate, endDate) VALUES ('141', 'Transformer', 'Transformer', 'OFF', 'OFF', 'OFF', '2016-06-01', '2016-10-31');
-- ALTER TABLE stats_users_access_daily  ADD I141 SMALLINT UNSIGNED NOT NULL DEFAULT 0 AFTER I138;

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_141;
CREATE TABLE IF NOT EXISTS iniz_141 (
	agenzia_id					CHAR(4) NOT NULL,
	obiettivo					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) NOT NULL DEFAULT 0,
	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz141 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_141;
CREATE VIEW vw_iniz_141 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.obiettivo,
	st.premiTOTALI,
	st.pezziTOTALI,
	st.premiCompTOTALI,
	st.obiettivoOK,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_141 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_141_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_141_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_141 (
		agenzia_id, objGAshow, objGAobjPremi, obiettivo
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi, p_objInizPremi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi, obiettivo = p_objInizPremi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_141_set_status;
DELIMITER //
CREATE PROCEDURE iniz_141_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 141;
	DECLARE done 							INT DEFAULT 0;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE v_obiettivo						DECIMAL(12,2) UNSIGNED;

	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2);

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, obiettivo, objGAobjPremi FROM iniz_141 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;

	SET C_INIZIATIVA_ID = 141;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiTOTALI = 0; SET v_pezziTOTALI = 0; SET v_premiCompTOTALI = 0;
		SET v_obiettivoOK = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		SELECT IFNULL(SUM(premioAnnuo),0), IFNULL(SUM(premioComputabile),0) INTO v_premiTOTALI, v_premiCompTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;
		SELECT IFNULL(COUNT(*),0) INTO v_pezziTOTALI
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id;

		-- totali & status
		IF v_premiCompTOTALI >= v_obiettivo THEN
			SET v_obiettivoOK = 1;
			SET v_importoErogTOTALE = v_premiCompTOTALI * 0.10;
		END IF;

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_141 SET
			premiTOTALI = v_premiTOTALI, pezziTOTALI = v_pezziTOTALI, premiCompTOTALI = v_premiCompTOTALI,
			obiettivoOK = v_obiettivoOK, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_obiettivo, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
