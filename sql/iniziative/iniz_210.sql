/* ======================= SETUP ======================================================== */

INSERT IGNORE INTO iniz (id, nome, url, statusAMM, statusAGT, statusINT, dataInizio, dataFine) VALUES ('210', 'Epic Land', 'EpicLand', 'OFF', 'OFF', 'OFF', '2018-06-04', '2018-09-30');

/* ======================= TABLES ======================================================= */

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

DROP TABLE IF EXISTS iniz_210;
CREATE TABLE IF NOT EXISTS iniz_210 (
	agenzia_id					CHAR(4) NOT NULL,
	premiDimMultiInv			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiFree			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiTarget			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiDouble			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiItalia			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimMultiFlash			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiDimQuo					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiTOTALI					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiInv			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiFree			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiTarget			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiDouble			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiItalia			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimMultiFlash			SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziDimQuo					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	pezziTOTALI					SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiInv		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiFree		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiDouble		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiItalia		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimMultiFlash		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompDimQuo				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	premiCompTOTALI				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	frazDimMultiInv_UL			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiFree_UL			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiTarget_UL		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiDouble_UL		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiItalia_UL		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	frazDimMultiFlash_UL		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,

	obiettivoOK					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	bonusUnitLinkedOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	obiettivoProQuotaOK			TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	importoErog					DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogBonusUnitLinked	DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogProQuota 		DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	importoErogTOTALE			DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
	objGAshow					TINYINT(1) UNSIGNED NOT NULL DEFAULT 0,
	objGAobjPremi				DECIMAL(12,2) UNSIGNED NOT NULL DEFAULT 0,
--	objGAobjPezzi				SMALLINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercPremi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
--	objGApercPezzi				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	objGAstatus					TINYINT UNSIGNED NOT NULL DEFAULT 0,
	objGApercStatus				DECIMAL(4,1) UNSIGNED NOT NULL DEFAULT 0,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_iniz210 FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

SET FOREIGN_KEY_CHECKS=1;

/* ======================= VIEWS ======================================================= */

DROP VIEW IF EXISTS vw_iniz_210;
CREATE VIEW vw_iniz_210 AS
SELECT
	st.agenzia_id,
	ag.area,
	ag.district,
	ag.localita,
	ag.nome,
	ag.status,
	st.premiDimMultiInv,
	st.premiDimMultiFree,
	st.premiDimMultiTarget,
	st.premiDimMultiDouble,
	st.premiDimMultiItalia,
	st.premiDimMultiFlash,
	st.premiDimQuo,
	st.premiTOTALI,
	st.pezziDimMultiInv,
	st.pezziDimMultiFree,
	st.pezziDimMultiTarget,
	st.pezziDimMultiDouble,
	st.pezziDimMultiItalia,
	st.pezziDimMultiFlash,
	st.pezziDimQuo,
	st.pezziTOTALI,
	st.premiCompDimMultiInv,
	st.premiCompDimMultiFree,
	st.premiCompDimMultiTarget,
	st.premiCompDimMultiDouble,
	st.premiCompDimMultiItalia,
	st.premiCompDimMultiFlash,
	st.premiCompDimQuo,
	st.premiCompTOTALI,
	st.frazDimMultiInv_UL,
	st.frazDimMultiFree_UL,
	st.frazDimMultiTarget_UL,
	st.frazDimMultiDouble_UL,
	st.frazDimMultiItalia_UL,
	st.frazDimMultiFlash_UL,
	st.obiettivoOK,
	st.bonusUnitLinkedOK,
	st.obiettivoProQuotaOK,
	st.importoErog,
	st.importoErogBonusUnitLinked,
	st.importoErogProQuota,
	st.importoErogTOTALE,
	st.objGAshow,
	st.objGAobjPremi,
--	st.objGAobjPezzi,
	st.objGApercPremi,
--	st.objGApercPezzi,
	st.objGAstatus,
	st.objGApercStatus
FROM
	iniz_210 st
	LEFT JOIN agenzie ag ON ( st.agenzia_id = ag.id )
;

/* ======================= STORED PROCEDURES =========================================== */

DROP PROCEDURE IF EXISTS iniz_210_insert_obiettivi;
DELIMITER //
CREATE PROCEDURE iniz_210_insert_obiettivi (
	IN p_iniziativa_id	TINYINT,		-- NOT USED
	IN p_agenzia_id		CHAR(4),
	IN p_objGAshow		TINYINT(1),
	IN p_objGAobjPremi	DECIMAL(12,2),
	IN p_objGAobjPezzi	MEDIUMINT,		-- NOT USED
	IN p_gruppo			CHAR(2),		-- NOT USED
	IN p_objInizPremi	DECIMAL(12,2),	-- NOT USED
	IN p_objInizPezzi	MEDIUMINT,		-- NOT USED
	IN p_ptf			DECIMAL(12,2)	-- NOT USED
)
BEGIN
	INSERT INTO iniz_210 (
		agenzia_id, objGAshow, objGAobjPremi
	) VALUES (
		p_agenzia_id, p_objGAshow, p_objGAobjPremi
	) ON DUPLICATE KEY UPDATE
		objGAshow = p_objGAshow, objGAobjPremi = p_objGAobjPremi
	;
END; //
DELIMITER ;


DROP PROCEDURE IF EXISTS iniz_210_set_status;
DELIMITER //
CREATE PROCEDURE iniz_210_set_status (
	IN p_date			DATE,
	IN p_days			SMALLINT,
	IN p_currentDay		SMALLINT
)
BEGIN
	DECLARE C_INIZIATIVA_ID					INT DEFAULT 210;
	DECLARE done 							INT DEFAULT 0;
	DECLARE C_BONUS_MULTI_INV				DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_FREE				DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_TARGET			DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_DOUBLE			DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_ITALIA			DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_MULTI_FLASH				DECIMAL(2,1) UNSIGNED;
	DECLARE C_BONUS_QUO						DECIMAL(2,1) UNSIGNED;

	DECLARE v_agenzia_id					CHAR(4);
	DECLARE C_OBIETTIVO						MEDIUMINT UNSIGNED;
	DECLARE v_progress						TINYINT UNSIGNED;
	DECLARE v_progress2						TINYINT UNSIGNED;

	DECLARE v_premiDimMultiInv				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiFree				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiTarget			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiDouble			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiItalia			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimMultiFlash			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiDimQuo					DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiTOTALI					DECIMAL(12,2) UNSIGNED;
	DECLARE v_pezziDimMultiInv				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiFree				SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiTarget			SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiDouble			SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiItalia			SMALLINT UNSIGNED;
	DECLARE v_pezziDimMultiFlash			SMALLINT UNSIGNED;
	DECLARE v_pezziDimQuo					SMALLINT UNSIGNED;
	DECLARE v_pezziTOTALI					SMALLINT UNSIGNED;

	DECLARE v_premiCompDimMultiInv			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiFree			DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiTarget		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiDouble		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiItalia		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimMultiFlash		DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompDimQuo				DECIMAL(12,2) UNSIGNED;
	DECLARE v_premiCompTOTALI				DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiInv_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiFree_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiTarget_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiDouble_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiItalia_UL			DECIMAL(12,2) UNSIGNED;
	DECLARE v_frazDimMultiFlash_UL			DECIMAL(12,2) UNSIGNED;

	DECLARE v_obiettivoOK					TINYINT UNSIGNED;
	DECLARE v_bonusUnitLinkedOK				TINYINT UNSIGNED;
	DECLARE v_obiettivoProQuotaOK			TINYINT UNSIGNED;
	DECLARE v_importoErog					DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogBonusUnitLinked	DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogProQuota			DECIMAL(12,2) UNSIGNED;
	DECLARE v_importoErogTOTALE				DECIMAL(12,2) UNSIGNED;

	DECLARE v_objGAobjPremi					DECIMAL(12,2) UNSIGNED;
--	DECLARE v_objGAobjPezzi					SMALLINT UNSIGNED;
	DECLARE v_objGApercPremi				DECIMAL(4,1) UNSIGNED;
--	DECLARE v_objGApercPezzi				DECIMAL(4,1) UNSIGNED;
	DECLARE v_objGAstatus					TINYINT UNSIGNED;
	DECLARE v_objGApercStatus				DECIMAL(4,1) UNSIGNED;

	DECLARE cur_agenzia CURSOR FOR
		SELECT agenzia_id, objGAobjPremi FROM iniz_210 ORDER BY agenzia_id;
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02100' SET done = 1;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

	SET C_INIZIATIVA_ID = 210;
	SET C_OBIETTIVO = 40000;

	SET done = 0;
	OPEN cur_agenzia;
	FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
	WHILE NOT done DO
		-- set DEFAULT values
		SET v_premiDimMultiInv = 0; SET v_premiDimMultiFree = 0; SET v_premiDimMultiTarget = 0; SET v_premiDimMultiDouble = 0; SET v_premiDimMultiItalia = 0; SET v_premiDimMultiFlash = 0; SET v_premiDimQuo = 0; SET v_premiTOTALI = 0;
		SET v_pezziDimMultiInv = 0; SET v_pezziDimMultiFree = 0; SET v_pezziDimMultiTarget = 0; SET v_pezziDimMultiDouble = 0; SET v_pezziDimMultiItalia = 0; SET v_pezziDimMultiFlash = 0; SET v_pezziDimQuo = 0; SET v_pezziTOTALI = 0;
		SET v_premiCompDimMultiInv = 0; SET v_premiCompDimMultiFree = 0; SET v_premiCompDimMultiTarget = 0; SET v_premiCompDimMultiDouble = 0; SET v_premiCompDimMultiItalia = 0; SET v_premiCompDimMultiFlash = 0; SET v_premiCompDimQuo = 0; SET v_premiCompTOTALI = 0;
		SET v_frazDimMultiInv_UL = 0; SET v_frazDimMultiFree_UL = 0; SET v_frazDimMultiTarget_UL = 0; SET v_frazDimMultiDouble_UL = 0; SET v_frazDimMultiItalia_UL = 0; SET v_frazDimMultiFlash_UL = 0;
		SET v_obiettivoOK = 0; SET v_bonusUnitLinkedOK = 0; SET v_obiettivoProQuotaOK = 0;
		SET v_importoErog = 0; SET v_importoErogBonusUnitLinked = 0; SET v_importoErogProQuota = 0; SET v_importoErogTOTALE = 0;
		SET v_objGApercPremi = 0; SET v_objGAstatus = 0; SET v_objGApercStatus = 0;

		-- count POLIZZE
		-- Dimensione Multivalore Inv (premioUnico == premioComputabile)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiInv, v_premiCompDimMultiInv, v_frazDimMultiInv_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU07','MU15');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiInv
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU07','MU15');
		-- Dimensione Multivalore Free (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiFree, v_premiCompDimMultiFree, v_frazDimMultiFree_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU06','MU13');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiFree
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU06','MU13');
		-- Dimensione Multivalore Target (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiTarget, v_premiCompDimMultiTarget, v_frazDimMultiTarget_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU09','MU14');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiTarget
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU09','MU14');
		-- Dimensione Multivalore Double (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiDouble, v_premiCompDimMultiDouble, v_frazDimMultiDouble_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU11','MU16');
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiDouble
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto IN('MU11','MU16');
		-- Dimensione Multivalore Italia (premioComputabile == premioComputabile)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiItalia, v_premiCompDimMultiItalia, v_frazDimMultiItalia_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'PR01';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiItalia
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'PR01';
		-- Dimensione Multivalore Flash (premioComputabile == deltaPremio: solo la parte UL)
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0), IFNULL(SUM(deltaPremio),0) INTO v_premiDimMultiFlash, v_premiCompDimMultiFlash, v_frazDimMultiFlash_UL
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU04';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimMultiFlash
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'MU04';
		-- Dimensione Quota
		SELECT IFNULL(SUM(premioUnico),0), IFNULL(SUM(premioComputabile),0) INTO v_premiDimQuo,v_premiCompDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';
		SELECT IFNULL(COUNT(*),0) INTO v_pezziDimQuo
			FROM iniz_polizze WHERE iniziativa_id = C_INIZIATIVA_ID AND agenzia_id = v_agenzia_id AND codiceProdotto = 'UL10';

		-- totali & status

		SET v_premiTOTALI = v_premiDimMultiInv + v_premiDimMultiFree + v_premiDimMultiTarget + v_premiDimMultiDouble + v_premiDimMultiItalia + v_premiDimMultiFlash + v_premiDimQuo;
		SET v_premiCompTOTALI = v_premiCompDimMultiInv + v_premiCompDimMultiFree + v_premiCompDimMultiTarget + v_premiCompDimMultiDouble + v_premiCompDimMultiItalia + v_premiCompDimMultiFlash + v_premiCompDimQuo;
		SET v_pezziTOTALI = v_pezziDimMultiInv + v_pezziDimMultiFree + v_pezziDimMultiTarget + v_pezziDimMultiDouble + v_pezziDimMultiItalia + v_pezziDimMultiFlash + v_pezziDimQuo;

		-- incentivazione base
		IF v_premiTOTALI >= C_OBIETTIVO THEN
			SET v_obiettivoOK = 1;

			IF v_premiTOTALI >= 250001 THEN
				SET C_BONUS_QUO = 1.4; SET C_BONUS_MULTI_INV = 1.2; SET C_BONUS_MULTI_FREE = 0.9; SET C_BONUS_MULTI_TARGET = 0.9; SET C_BONUS_MULTI_DOUBLE = 1.2; SET C_BONUS_MULTI_ITALIA = 1.4; SET C_BONUS_MULTI_FLASH = 0.9;
			ELSEIF v_premiTOTALI >= 120001 THEN
				SET C_BONUS_QUO = 1.2; SET C_BONUS_MULTI_INV = 1.0; SET C_BONUS_MULTI_FREE = 0.7; SET C_BONUS_MULTI_TARGET = 0.7; SET C_BONUS_MULTI_DOUBLE = 1.0; SET C_BONUS_MULTI_ITALIA = 1.2; SET C_BONUS_MULTI_FLASH = 0.7;
			ELSEIF v_premiTOTALI >= 40000 THEN
				SET C_BONUS_QUO = 1.0; SET C_BONUS_MULTI_INV = 0.8; SET C_BONUS_MULTI_FREE = 0.5; SET C_BONUS_MULTI_TARGET = 0.5; SET C_BONUS_MULTI_DOUBLE = 0.8; SET C_BONUS_MULTI_ITALIA = 1.0; SET C_BONUS_MULTI_FLASH = 0.5;
			END IF;

			SET v_importoErog = v_premiCompDimQuo * C_BONUS_QUO/100 +
								v_premiCompDimMultiInv * C_BONUS_MULTI_INV/100 +
								v_premiCompDimMultiFree * C_BONUS_MULTI_FREE/100 +
								v_premiCompDimMultiTarget * C_BONUS_MULTI_TARGET/100 +
								v_premiCompDimMultiDouble * C_BONUS_MULTI_DOUBLE/100 +
								v_premiCompDimMultiItalia * C_BONUS_MULTI_ITALIA/100 +
								v_premiCompDimMultiFlash * C_BONUS_MULTI_FLASH/100;

			-- Bonus Unit-Linked
			IF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL + v_frazDimMultiDouble_UL + v_frazDimMultiItalia_UL + v_frazDimMultiFlash_UL >= 150000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.30;
			ELSEIF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL + v_frazDimMultiDouble_UL + v_frazDimMultiItalia_UL + v_frazDimMultiFlash_UL >= 75000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.20;
			ELSEIF v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL + v_frazDimMultiDouble_UL + v_frazDimMultiItalia_UL + v_frazDimMultiFlash_UL >= 25000 THEN
				SET v_bonusUnitLinkedOK = 1;
				SET v_importoErogBonusUnitLinked =  v_importoErog * 0.10;
			END IF;

			SET v_importoErogTOTALE = v_importoErog + v_importoErogBonusUnitLinked;
		END IF;

		-- incentivazione Unit-Linked Pro-Quota
		IF v_premiTOTALI < C_OBIETTIVO AND v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL + v_frazDimMultiDouble_UL + v_frazDimMultiItalia_UL >= 21000 THEN
			SET v_obiettivoProQuotaOK = 1;
			SET v_importoErogProQuota = v_premiCompDimQuo * v_premiCompTOTALI / C_OBIETTIVO * 0.010 +
										v_frazDimMultiInv_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.008 +
										v_frazDimMultiFree_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.005 +
										v_frazDimMultiTarget_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.005 +
										v_frazDimMultiDouble_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.008 +
										v_frazDimMultiItalia_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.010 +
										v_frazDimMultiFlash_UL * v_premiCompTOTALI / C_OBIETTIVO * 0.010 ;
			SET v_importoErogTOTALE = v_importoErogProQuota;
		END IF;

		-- PROGRESS BAR
		SET v_progress = LEAST(100, v_premiTOTALI / C_OBIETTIVO * 100);
		SET v_progress2 = LEAST(100, (v_premiCompDimQuo + v_frazDimMultiInv_UL + v_frazDimMultiFree_UL + v_frazDimMultiTarget_UL + v_frazDimMultiDouble_UL + v_frazDimMultiItalia_UL + v_frazDimMultiFlash_UL) / 25000 * 100);
		REPLACE iniz_cache VALUES (C_INIZIATIVA_ID, v_agenzia_id, v_progress, v_progress2, NULL);

		-- GA OBJ
		SET v_objGApercPremi = v_premiTOTALI / v_objGAobjPremi * 100;
--		SET v_objGApercPezzi = v_pezziTOTALI / v_objGAobjPezzi * 100;
		SET v_objGApercStatus = v_premiTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
--		SET v_objGApercStatus = v_pezziTOTALI / ( v_objGAobjPremi / p_days * p_currentDay ) * 100;
		IF v_objGApercStatus >= 100 THEN SET v_objGAstatus = 1; END IF;

		-- update tables
		UPDATE iniz_210 SET
			premiDimMultiInv = v_premiDimMultiInv, premiDimMultiFree = v_premiDimMultiFree, premiDimMultiTarget = v_premiDimMultiTarget, premiDimMultiDouble = v_premiDimMultiDouble, premiDimMultiItalia = v_premiDimMultiItalia, premiDimMultiFlash = v_premiDimMultiFlash, premiDimQuo = v_premiDimQuo, premiTOTALI = v_premiTOTALI,
			pezziDimMultiInv = v_pezziDimMultiInv, pezziDimMultiFree = v_pezziDimMultiFree, pezziDimMultiTarget = v_pezziDimMultiTarget, pezziDimMultiDouble = v_pezziDimMultiDouble, pezziDimMultiItalia = v_pezziDimMultiItalia, pezziDimMultiFlash = v_pezziDimMultiFlash, pezziDimQuo = v_pezziDimQuo, pezziTOTALI = v_pezziTOTALI,
			premiCompDimMultiInv = v_premiCompDimMultiInv, premiCompDimMultiFree = v_premiCompDimMultiFree, premiCompDimMultiTarget = v_premiCompDimMultiTarget, premiCompDimMultiDouble = v_premiCompDimMultiDouble, premiCompDimMultiItalia = v_premiCompDimMultiItalia, premiCompDimMultiFlash = v_premiCompDimMultiFlash, premiCompDimQuo = v_premiCompDimQuo, premiCompTOTALI = v_premiCompTOTALI,
			frazDimMultiInv_UL = v_frazDimMultiInv_UL, frazDimMultiFree_UL = v_frazDimMultiFree_UL, frazDimMultiTarget_UL = v_frazDimMultiTarget_UL, frazDimMultiDouble_UL = v_frazDimMultiDouble_UL, frazDimMultiItalia_UL = v_frazDimMultiItalia_UL, frazDimMultiFlash_UL = v_frazDimMultiFlash_UL,
			obiettivoOK = v_obiettivoOK, bonusUnitLinkedOK = v_bonusUnitLinkedOK, obiettivoProQuotaOK = v_obiettivoProQuotaOK,
			importoErog = v_importoErog, importoErogBonusUnitLinked = v_importoErogBonusUnitLinked, importoErogProQuota = v_importoErogProQuota, importoErogTOTALE = v_importoErogTOTALE,
			objGApercPremi = v_objGApercPremi,
--			objGApercPezzi = v_objGApercPezzi,
			objGAstatus = v_objGAstatus, objGApercStatus = v_objGApercStatus
			WHERE agenzia_id = v_agenzia_id
		;
		SET done = 0;
        FETCH cur_agenzia INTO v_agenzia_id, v_objGAobjPremi;
    END WHILE;
    CLOSE cur_agenzia;

END; //
DELIMITER ;
