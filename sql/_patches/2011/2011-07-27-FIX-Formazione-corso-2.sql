/*
===== doppioni =======================
SELECT `iduser`, `idcorso`, count(*) AS n FROM `FORiscrizioni` WHERE `idcorso` = 1 GROUP BY `iduser` HAVING n > 1
929
SELECT `iduser`, `idcorso`, count(*) AS n FROM `FORiscrizioni` WHERE `idcorso` = 2 GROUP BY `iduser` HAVING n > 1
1000
5478 Severina Garbarini OK x il 6 ottobre a Torino
10780
==== fantasmi ========================
SELECT * FROM `FORiscrizioni` WHERE `iduser` NOT IN (SELECT `id` FROM `Users`);
INSERT INTO `FORiscrizioni` (`id`, `iduser`, `tipo`, `agenzia`, `idcorso`, `booked`, `bookedvia`, `bookedby`, `sede`, `groupamavalid`, `groupamavalidation`, `groupamavaliduser`, `iscritto`, `iscrittowhen`, `presenze`, `esito`, `questionario`, `createdby`, `created`, `updateby`, `updateat`, `promemoria`, `comunicazione`) VALUES
(639, 10650, 'AGT', 'G031', 1, '2011-03-29 12:28:59', 'pdf', 0, 2, 'w', '0000-00-00 00:00:00', 0, 'deleted', '0000-00-00 00:00:00', 'G1=0;G2=0', 'none', '0', 2526, '2011-03-23 16:23:23', 2526, '2011-03-23 16:23:23', '0', '1');
DELETE FROM `FORiscrizioni` WHERE `iduser` NOT IN (SELECT `id` FROM `Users`);
*/
ALTER TABLE `FORiscrizioni`
	CHANGE `iduser` `iduser` mediumint unsigned NOT NULL,
	ADD CONSTRAINT UNIQUE KEY `uk_user_corso` (`iduser`, `idcorso`),
	ADD CONSTRAINT FOREIGN KEY `fk_Users` (`iduser`) REFERENCES `Users` (`id`)
;


/*
Utenti di partenza:	4905
dopo SP:			5216 (DIFF: 311)
Query Davide: 506 => 828 (DIFF: 322)

Anagrafica: DIP 830 + INT 3328 + AGT 1083 = 5241
Invitati:   DIP 834 + INT 3363 + AGT 1085 = 5282
*/
DROP PROCEDURE IF EXISTS `batch_FIX_Formazione_corso_2`;
DELIMITER //
CREATE PROCEDURE `batch_FIX_Formazione_corso_2` (
)
BEGIN
	DECLARE done 						INT default 0;
	DECLARE v_userID					MEDIUMINT;
	DECLARE v_agenziaID					CHAR(4);
	DECLARE v_corsoID					MEDIUMINT;
	DECLARE v_createdby					MEDIUMINT;
	/* CURSORS  */
	DECLARE cur_userAGT CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Agenti`
		WHERE active = '1';
	DECLARE cur_userINT CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Intermediari`
		WHERE type = 'INT' AND active = '1' AND ruolo IN ('1','3','4')
			AND nome IS NOT NULL AND cognome IS NOT NULL AND nome <> '' AND cognome <> '';
	DECLARE cur_userDIP CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Intermediari`
		WHERE type = 'INT' AND active = '1' AND ruolo IN ('2','5')
			AND nome IS NOT NULL AND cognome IS NOT NULL AND nome <> '' AND cognome <> '';
	DECLARE cur_AGT CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Agenti`;
	DECLARE cur_INT CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Intermediari`
		WHERE ruolo IN ('1','3','4');
	DECLARE cur_DIP CURSOR FOR
		SELECT id, agenziaID
		FROM `vw_Users-Intermediari`
		WHERE ruolo IN ('2','5');
	DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET done = 1;
	/* PROCEDURE */
	SET done = 0;
	SET v_corsoID = 2;

	OPEN cur_userAGT;
	FETCH cur_userAGT INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		INSERT IGNORE INTO `FORiscrizioni` VALUES (
			'', v_userID, 'AGT', v_agenziaID, v_corsoID,
			0, 'none', 0, 0, 'w', 0, 0,
			'0', '', 'G1=0', 'none', '0',
			v_createdby, '2011-06-03 11:45:03', v_createdby,'2011-06-03 11:45:03',
			'0', '0'
		);
		FETCH cur_userAGT INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_userAGT;

	SET done = 0;
	OPEN cur_userINT;
	FETCH cur_userINT INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		INSERT IGNORE INTO `FORiscrizioni` VALUES (
			'', v_userID, 'INT', v_agenziaID, v_corsoID,
			0, 'none', 0, 0, 'w', 0, 0,
			'0', '', 'G1=0', 'none', '0',
			v_createdby, '2011-06-03 11:45:03', v_createdby,'2011-06-03 11:45:03',
			'0', '0'
		);
		FETCH cur_userINT INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_userINT;

	SET done = 0;
	OPEN cur_userDIP;
	FETCH cur_userDIP INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		INSERT IGNORE INTO `FORiscrizioni` VALUES (
			'', v_userID, 'DIP', v_agenziaID, v_corsoID,
			0, 'none', 0, 0, 'w', 0, 0,
			'0', '', 'G1=0', 'none', '0',
			v_createdby, '2011-06-03 11:45:03', v_createdby,'2011-06-03 11:45:03',
			'0', '0'
		);
		FETCH cur_userDIP INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_userDIP;

	SET done = 0;
	OPEN cur_AGT;
	FETCH cur_AGT INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		UPDATE `FORiscrizioni` SET `tipo` = 'AGT', `agenzia` = v_agenziaID WHERE `iduser` = v_userID AND `idcorso` = v_corsoID;
		FETCH cur_AGT INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_AGT;

	SET done = 0;
	OPEN cur_INT;
	FETCH cur_INT INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		UPDATE `FORiscrizioni` SET `tipo` = 'INT', `agenzia` = v_agenziaID WHERE `iduser` = v_userID AND `idcorso` = v_corsoID;
		FETCH cur_INT INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_INT;

	SET done = 0;
	OPEN cur_DIP;
	FETCH cur_DIP INTO v_userID, v_agenziaID;
	WHILE NOT done DO
		UPDATE `FORiscrizioni` SET `tipo` = 'DIP', `agenzia` = v_agenziaID WHERE `iduser` = v_userID AND `idcorso` = v_corsoID;
		FETCH cur_DIP INTO v_userID, v_agenziaID;
	END WHILE;
	CLOSE cur_DIP;
	
	DELETE FROM `FORiscrizioni` WHERE `idcorso` = 2 AND `iduser` IN (
		SELECT id FROM `Users` WHERE active = '0'
	);
	
	UPDATE `FORiscrizioni` SET `booked` = '2011-06-03 13:00:00' WHERE `idcorso` = 2 AND `booked` = '0000-00-00 00:00:00' AND `bookedvia` != 'none';

END; //
DELIMITER ;
CALL `batch_FIX_Formazione_corso_2`();
DROP PROCEDURE IF EXISTS `batch_FIX_Formazione_corso_2`;
