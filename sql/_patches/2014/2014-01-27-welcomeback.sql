/* 2014-01-26 */
ALTER TABLE welcomeback_clientiauto
		ADD targa varchar(9) not NULL AFTER email
;

CREATE TABLE IF NOT EXISTS welcomeback_gold (
	agenzia_id			char(4) NOT NULL,
	PRIMARY KEY (agenzia_id),
	CONSTRAINT fk_welcomeback_gold FOREIGN KEY (agenzia_id) REFERENCES agenzie (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/* 2014-01-27 */
ALTER TABLE welcomeback_clientiauto
	DROP PRIMARY KEY,
	ADD PRIMARY KEY(id, targa)
;