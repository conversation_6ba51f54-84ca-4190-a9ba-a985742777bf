DROP TABLE IF EXISTS tra_product;
CREATE TABLE tra_product (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(256) NOT NULL UNIQUE,
    shortName VARCHAR(64),
    startDate DATE,
    description TEXT,
    active TINYINT DEFAULT 0
);

INSERT INTO
    tra_product (id, name, shortName, description, startDate)
VALUES
    (
        1,
        'Progetto Attivo',
        'attivo',
        'Corso di formazione per attivo',
        '2024-01-01'
    ),
    (
        2,
        'DinamicaPlus Commercio',
        'commercio',
        'Corso di formazione per commercio',
        '2024-01-01'
    ),
    (
        3,
        'Ondamica',
        'ondamica',
        'Corso di formazione per ondamica',
        '2024-01-01'
    ),
    (
        4,
        'Guidamica',
        'guidamica',
        'Corso di formazione per guidamica',
        '2024-01-01'
    ),
    (
        5,
        'Benessere InSalute',
        'benessere',
        'Corso di formazione per benessere',
        '2024-01-01'
    ),
    (
        6,
        'Casa Senza Confini',
        'casa',
        'Corso di formazione per casa',
        '2024-01-01'
    ),
    (
        7,
        'FPA',
        'fpa',
        'Corso di formazione per fpa',
        '2024-01-01'
    ),
    (
        8,
        'Responsabilità Civile Amministratori',
        'amministratori',
        'Corso di formazione per amministratori',
        '2024-01-01'
    ),
    (
        9,
        'Merci Trasportate',
        'trasportate',
        'Corso di formazione per trasportate',
        '2024-01-01'
    ),
    (
        10,
        'Obiettivo Protetto',
        'protetto',
        'Corso di formazione per protetto',
        '2024-01-01'
    );

ALTER TABLE
    tra_course
ADD
    COLUMN product_id INT;

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'attivo'
        LIMIT
            1
    )
WHERE
    id IN (13765, 13740, 13736, 14040, 14317);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'commercio'
        LIMIT
            1
    )
WHERE
    id IN (
        13400,
        13415,
        13427,
        13733,
        14010,
        14320,
        14362,
        14365
    );

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'ondamica'
        LIMIT
            1
    )
WHERE
    id IN (13813, 14115);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'guidamica'
        LIMIT
            1
    )
WHERE
    id IN (14157);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'benessere'
        LIMIT
            1
    )
WHERE
    id IN (13835, 13868, 13886, 14190, 14301);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'casa'
        LIMIT
            1
    )
WHERE
    id IN (
        13883,
        13889,
        14311,
        13922,
        14202,
        13977,
        13965,
        14304
    );

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'fpa'
        LIMIT
            1
    )
WHERE
    id IN (13944, 14232, 13880, 14329, 13871);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'amministratori'
        LIMIT
            1
    )
WHERE
    id IN (13838, 13898, 14214);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'trasportate'
        LIMIT
            1
    )
WHERE
    id IN (14307);

UPDATE
    tra_course
SET
    product_id = (
        SELECT
            id
        FROM
            tra_product
        WHERE
            shortname = 'protetto'
        LIMIT
            1
    )
WHERE
    id IN (14332, 14338);

ALTER TABLE
    tra_course
ADD
    FOREIGN KEY (product_id) REFERENCES tra_product(id) ON DELETE CASCADE;

DROP VIEW IF EXISTS vw_tra_course_products;
CREATE VIEW vw_tra_course_products AS
SELECT
    tc.product_id,
    tp.name AS productName,
    tp.active,
    tc.id as course_id,
    ta.class_id,
    tc.title,
    tc.year,
    tcl.firstDay,
    ta.completedAt,
    tcl.signupStart,
    ta.state,
    ta.credits,
    tc.groupamaType,
    ta.user_id,
    u.area,
    u.district
FROM
    `tra_course` tc
   LEFT JOIN tra_attendance ta ON ta.course_id = tc.id
    JOIN users u ON ta.user_id = u.id
    LEFT JOIN tra_class tcl ON ta.class_id = tcl.id
    JOIN tra_product tp ON tc.product_id = tp.id
WHERE
    ta.state = 'signedup' AND u.type = 'AGENTE' AND u.active =1 AND tp.active;
