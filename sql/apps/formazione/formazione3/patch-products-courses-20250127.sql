DROP VIEW IF EXISTS vw_tra_course;
CREATE VIEW vw_tra_course AS
SELECT co.id,
       co.year,
       co.ivass,
       co.tag,
       co.title,
       co.cover,
       co.code,
       co.`data`,
       co.`type`,
       co.credits,
       co.groupamaType,
       co.`tipo`,
       co.erogato,
       co.modalita,
       CASE WHEN co.groupamaType = 'e-learning' THEN cl.id END AS class_id,
       co.filters,
       co.remoteId,
       co.`status`,
       co.`certificate`,
       co.`dmAllowed`,
       p.`id` as product_id,
       p.`name` as productName
FROM tra_course co
         LEFT JOIN tra_class cl ON cl.course_id = co.id
         LEFT JOIN tra_product p ON co.product_id = p.id
GROUP BY co.id;