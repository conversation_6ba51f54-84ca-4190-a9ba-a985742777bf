DROP VIEW IF EXISTS vw_tra_survey_training_offer;
CREATE VIEW vw_tra_survey_training_offer AS
SELECT u.agenzia_id, a.nome as agencyName, ga.nome as areaName, gd.nome as districtName, u.nome, u.cognome, s.product, s.productOther, s.collaborators, s.createdAt
FROM `tra_survey_training_offer` s
         JOIN `users` `u` ON `s`.`user_id` = `u`.`id`
         JOIN agenzie a ON u.agenzia_id = a.id
         JOIN geo_aree ga ON u.area = ga.id
         JOIN geo_districts gd ON u.district = gd.id
;