drop table if exists inc_focus_data;
CREATE TABLE `inc_focus_data` (
  `iniziativa_id` smallint(8) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `polizza` varchar(64) NOT NULL,
  `data` date NOT NULL,
  `dataEffetto` date NOT NULL,
  `prodotto` varchar(32) NOT NULL,
  `motivo` varchar(4) NOT NULL,
  `tipoTrasformazione` varchar(64) NOT NULL,
  `partenza` varchar(32) NOT NULL,
  `arrivo` varchar(32) NOT NULL,
  `premio` float NOT NULL,
  `cf` varchar(32) NOT NULL,
  `codiceCliente` varchar(32) NOT NULL,
  `nominativo` varchar(128) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


drop table if exists inc_focus_semester;
CREATE TABLE `inc_focus_semester` (
  `agenzia_id` char(4) NOT NULL,
  `bonus` float NOT NULL,
  `bonusMA` float NOT NULL,
  `ratioMA` float NOT NULL,
  `totMA` smallint(6) NOT NULL,
  `soglia70NumMA` tinyint(4) NOT NULL,
  `soglia80NumMA` tinyint(4) NOT NULL,
  `soglia70FlagMA` tinyint(1) NOT NULL,
  `soglia80FlagMA` tinyint(1) NOT NULL,
  `bonusMX` float NOT NULL,
  `ratioMX` float NOT NULL,
  `totMX` smallint(6) NOT NULL,
  `soglia70NumMX` tinyint(4) NOT NULL,
  `soglia80NumMX` tinyint(4) NOT NULL,
  `soglia70FlagMX` tinyint(1) NOT NULL,
  `soglia80FlagMX` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

drop table if exists inc_focus_static;
CREATE TABLE `inc_focus_static` (
  `iniziativa_id` smallint(5) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `sogliaMA` tinyint(4) NOT NULL,
  `sogliaMX` tinyint(4) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

drop table if exists inc_focus_status;
CREATE TABLE `inc_focus_status` (
  `agenzia_id` char(4) NOT NULL,
  `totMA` smallint(5) UNSIGNED NOT NULL,
  `totMANOA` smallint(5) UNSIGNED NOT NULL,
  `objMA` tinyint(3) UNSIGNED NOT NULL,
  `objMAOk` tinyint(1) NOT NULL,
  `totMX` smallint(5) UNSIGNED NOT NULL,
  `objMX` tinyint(3) UNSIGNED NOT NULL,
  `objMXOk` tinyint(1) NOT NULL,
  `bonusMA` float NOT NULL,
  `bonusMX` float NOT NULL,
  `bonus` float NOT NULL,
  `bonusTot` float NOT NULL,
  `superbonus` float NOT NULL,
  `superbonus2x` float NOT NULL,
  `superbonus2xMA` float NOT NULL,
  `superbonus2xMX` float NOT NULL,
  `booster` float NOT NULL,
  `G1_MA_ARGENTO` smallint(5) UNSIGNED NOT NULL,
  `G1_MX_ARGENTO` smallint(5) UNSIGNED NOT NULL,
  `G1_MA_ORO_PLATINO` smallint(5) UNSIGNED NOT NULL,
  `G1_MX_ORO_PLATINO` smallint(5) UNSIGNED NOT NULL,
  `G1_AA_ORO_PLATINO` smallint(5) UNSIGNED NOT NULL,
  `G2_MX_ARGENTO` smallint(5) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

drop table if exists inc_focus_clients;
CREATE TABLE `inc_focus_clients` (
  `iniziativa_id` smallint(5) UNSIGNED NOT NULL,
  `agenzia_id` char(4) NOT NULL,
  `codiceCliente` varchar(32) NOT NULL,
  `nominativo` varchar(128) NOT NULL,
  `partenza` varchar(32) NOT NULL,
  `arrivo` varchar(32) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Indici per le tabelle `inc_focus_clients`
--
ALTER TABLE `inc_focus_clients`
ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`,`codiceCliente`),
ADD KEY `agenzia_id` (`agenzia_id`);

--
-- Limiti per la tabella `inc_focus_clients`
--
ALTER TABLE `inc_focus_clients`
ADD CONSTRAINT `inc_focus_clients_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
ADD CONSTRAINT `inc_focus_clients_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

--
-- Indici per le tabelle `inc_focus_data`
--
ALTER TABLE `inc_focus_data`
ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`,`polizza`),
ADD KEY `agenzia_id` (`agenzia_id`);

--
-- Indici per le tabelle `inc_focus_semester`
--
ALTER TABLE `inc_focus_semester`
ADD PRIMARY KEY (`agenzia_id`);

--
-- Indici per le tabelle `inc_focus_static`
--
ALTER TABLE `inc_focus_static`
ADD PRIMARY KEY (`iniziativa_id`,`agenzia_id`),
ADD KEY `agenzia_id` (`agenzia_id`);

--
-- Indici per le tabelle `inc_focus_status`
--
ALTER TABLE `inc_focus_status`
ADD PRIMARY KEY (`agenzia_id`);

--
-- Limiti per la tabella `inc_focus_data`
--
ALTER TABLE `inc_focus_data`
ADD CONSTRAINT `inc_focus_data_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
ADD CONSTRAINT `inc_focus_data_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

--
-- Limiti per la tabella `inc_focus_semester`
--
ALTER TABLE `inc_focus_semester`
ADD CONSTRAINT `inc_focus_semester_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);

--
-- Limiti per la tabella `inc_focus_static`
--
ALTER TABLE `inc_focus_static`
ADD CONSTRAINT `inc_focus_static_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`),
ADD CONSTRAINT `inc_focus_static_ibfk_2` FOREIGN KEY (`iniziativa_id`) REFERENCES `incentive` (`id`);

--
-- Limiti per la tabella `inc_focus_status`
--
ALTER TABLE `inc_focus_status`
ADD CONSTRAINT `inc_focus_status_ibfk_1` FOREIGN KEY (`agenzia_id`) REFERENCES `agenzie` (`id`);


drop view if exists	vw_inc_focus_tras;
create view vw_inc_focus_tras as SELECT
a.area,
a.district,
agenzia_id,

sogliaMA,
sogliaMX,

sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G1_AA_ORO_PLATINO',0)) as totMA,
sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMANOA,
sum(coalesce(tipoTrasformazione='G1_MX_ARGENTO', 0)+coalesce(tipoTrasformazione='G1_MX_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G2_MX_ARGENTO',0)) as totMX,

sum(tipoTrasformazione='G1_MA_ARGENTO') as G1_MA_ARGENTO,
sum(tipoTrasformazione='G1_MX_ARGENTO') as G1_MX_ARGENTO,

sum(tipoTrasformazione='G1_MA_ORO_PLATINO') as G1_MA_ORO_PLATINO,
sum(tipoTrasformazione='G1_MX_ORO_PLATINO') as G1_MX_ORO_PLATINO,

sum(tipoTrasformazione='G1_AA_ORO_PLATINO') as G1_AA_ORO_PLATINO,

sum(tipoTrasformazione='G2_MX_ARGENTO') as G2_MX_ARGENTO

FROM

(
SELECT
       `a`.`area` AS `area`,
       `a`.`district` AS `district`,
       `d`.`iniziativa_id` AS `iniziativa_id`,
       `s`.`agenzia_id` AS `agenzia_id`,
       `d`.`polizza` AS `polizza`,
       `d`.`data` AS `data`,
       `d`.`dataEffetto` AS `dataEffetto`,
       `d`.`prodotto` AS `prodotto`,
       `d`.`motivo` AS `motivo`,
       `d`.`tipoTrasformazione` AS `tipoTrasformazione`,
       `d`.`partenza` AS `partenza`,
       `d`.`arrivo` AS `arrivo`,
       `d`.`premio` AS `premio`,
       `d`.`cf` AS `cf`,
       `d`.`codiceCliente` AS `codiceCliente`,
       `d`.`nominativo` AS `nominativo`,
       `s`.`sogliaMA` AS `sogliaMA`,
       `s`.`sogliaMX` AS `sogliaMX`
  from inc_focus_static s
	left join inc_focus_data d on s.agenzia_id = d.agenzia_id
    join agenzie a on a.id = s.agenzia_id
	group by s.agenzia_id, cf, tipoTrasformazione
) a

group by agenzia_id;

drop view if exists vw_inc_focus_monitoring_district;
create view vw_inc_focus_monitoring_district as SELECT
  a.area,
  a.district as id,
  a.nome,

  sum(sogliaMA) as sogliaMA,
  sum(sogliaMX) as sogliaMX,

  sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G1_AA_ORO_PLATINO',0)) as totMA,
  sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMANOA,
  sum(coalesce(tipoTrasformazione='G1_MX_ARGENTO', 0)+coalesce(tipoTrasformazione='G1_MX_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G2_MX_ARGENTO',0)) as totMX

  FROM

  (
    select a.area,a.district,d.*, dis.nome, s.sogliaMA, s.sogliaMX from `inc_focus_data` d
      join inc_focus_static s on s.agenzia_id = d.agenzia_id
      join agenzie a on a.id = d.agenzia_id
      join geo_districts dis on dis.id = a.district
    group by d.agenzia_id, cf, tipoTrasformazione
  ) a

group by district;

drop view if exists vw_inc_focus_monitoring_agenzie;
create view vw_inc_focus_monitoring_agenzie as SELECT
    a.area,
    a.district,
    a.agenzia_id as nome,
    a.nome as agencyName,
    a.localita,
    areaNome,
    a.am,
    a.dm,
    a.totMA+a.totMX as tot,
    bonusSem as bonusSem,
    a.bonusTot,

    sogliaMA as sogliaMA,
    sogliaMX as sogliaMX,

    sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMA,
    sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMANOA,
    sum(coalesce(tipoTrasformazione='G1_MX_ARGENTO', 0)+coalesce(tipoTrasformazione='G1_MX_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G2_MX_ARGENTO',0)) as totMX,

    sum(tipoTrasformazione='G1_MA_ARGENTO') as G1_MA_ARGENTO,
    sum(tipoTrasformazione='G1_MX_ARGENTO') as G1_MX_ARGENTO,

    sum(tipoTrasformazione='G1_MA_ORO_PLATINO') as G1_MA_ORO_PLATINO,
    sum(tipoTrasformazione='G1_MX_ORO_PLATINO') as G1_MX_ORO_PLATINO,

    sum(tipoTrasformazione='G1_AA_ORO_PLATINO') as G1_AA_ORO_PLATINO,

    sum(tipoTrasformazione='G2_MX_ARGENTO') as G2_MX_ARGENTO

  FROM

    (
      select a.area,a.district,a.nome,a.localita,are.nome as areaNome, d.*, concat(am.nome, " ", am.cognome) as am, dis.nome as dm, s.sogliaMA, s.sogliaMX, st.totMA, st.totMX, st.totMANOA, sem.bonus as bonusSem, st.bonusTot from `inc_focus_data` d
        join inc_focus_static s on s.agenzia_id = d.agenzia_id
        join inc_focus_status st on st.agenzia_id = d.agenzia_id
        join inc_focus_semester sem on sem.agenzia_id = d.agenzia_id
        join agenzie a on a.id = d.agenzia_id
        join geo_districts dis on dis.id = a.district
        join geo_aree are on are.id = a.area
        join users am on am.area = a.area and am.active = 1 and am.type = 'AREAMGR'
      group by d.agenzia_id, cf, tipoTrasformazione
    ) a

  group by agenzia_id;

drop view if exists vw_inc_focus_monitoring_area;
create view vw_inc_focus_monitoring_area as
  SELECT
         `a`.`area` AS `id`,
         `a`.`nome` AS `nome`,
         `a`.`sogliaMA` AS `sogliaMA`,
         `a`.`sogliaMX` AS `sogliaMX`,
         sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G1_AA_ORO_PLATINO',0)) as totMA,
         sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMANOA,
         sum(coalesce(tipoTrasformazione='G1_MX_ARGENTO', 0)+coalesce(tipoTrasformazione='G1_MX_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G2_MX_ARGENTO',0)) as totMX
  FROM
       (
       SELECT
              `a`.`area` AS `area`,
              `are`.`nome` AS `nome`,
              `a`.`district` AS `district`,
              `d`.`iniziativa_id` AS `iniziativa_id`,
              `d`.`agenzia_id` AS `agenzia_id`,
              `d`.`polizza` AS `polizza`,
              `d`.`data` AS `data`,
              `d`.`dataEffetto` AS `dataEffetto`,
              `d`.`prodotto` AS `prodotto`,
              `d`.`motivo` AS `motivo`,
              `d`.`tipoTrasformazione` AS `tipoTrasformazione`,
              `d`.`partenza` AS `partenza`,
              `d`.`arrivo` AS `arrivo`,
              `d`.`premio` AS `premio`,
              `d`.`cf` AS `cf`,
              `d`.`codiceCliente` AS `codiceCliente`,
              `d`.`nominativo` AS `nominativo`,
              `s`.`sogliaMA` AS `sogliaMA`,
              `s`.`sogliaMX` AS `sogliaMX`
       FROM
            (
                (
                    (
                        (
                            `portaleagendo`.`inc_focus_data` `d`
                                JOIN `portaleagendo`.`agenzie` `a`
                                ON
                                  ((`a`.`id` = `d`.`agenzia_id`))
                            )
                            JOIN(
                                SELECT
                                       `a`.`area` AS `area`,
                                       SUM(`s`.`sogliaMA`) AS `sogliaMA`,
                                       SUM(`s`.`sogliaMX`) AS `sogliaMX`
                                FROM
                                     (
                                         (
                                             `portaleagendo`.`inc_focus_static` `s`
                                                 JOIN `portaleagendo`.`agenzie` `a`
                                                 ON
                                                   ((`a`.`id` = `s`.`agenzia_id`))
                                             )
                                             JOIN `portaleagendo`.`geo_aree` `are`
                                             ON
                                               ((`are`.`id` = `a`.`area`))
                                         )
                                GROUP BY
                                         `are`.`id`
                                ) `s`
                            ON
                              ((`s`.`area` = `a`.`area`))
                        )
                        JOIN `portaleagendo`.`geo_districts` `dis`
                        ON
                          ((`dis`.`id` = `a`.`district`))
                    )
                    JOIN `portaleagendo`.`geo_aree` `are`
                    ON
                      ((`are`.`id` = `a`.`area`))
                )
       GROUP BY
                `d`.`agenzia_id`,
                `d`.`cf`,
                `d`.`tipoTrasformazione`
       ) `a`
  GROUP BY
           `a`.`area`;

drop view if exists vw_inc_focus_monitoring_district;
create view vw_inc_focus_monitoring_district as
  SELECT
         `a`.`area` AS `area`,
         `a`.`district` AS `id`,
         `a`.`nome` AS `nome`,
         `a`.`sogliaMA` AS `sogliaMA`,
         `a`.`sogliaMX` AS `sogliaMX`,
         sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G1_AA_ORO_PLATINO',0)) as totMA,
         sum(coalesce(tipoTrasformazione='G1_MA_ARGENTO',0)+coalesce(tipoTrasformazione='G1_MA_ORO_PLATINO',0)) as totMANOA,
         sum(coalesce(tipoTrasformazione='G1_MX_ARGENTO', 0)+coalesce(tipoTrasformazione='G1_MX_ORO_PLATINO',0)+coalesce(tipoTrasformazione='G2_MX_ARGENTO',0)) as totMX
  FROM
       (
       SELECT
              `a`.`area` AS `area`,
              `dis`.`nome` AS `nome`,
              `a`.`district` AS `district`,
              `d`.`iniziativa_id` AS `iniziativa_id`,
              `d`.`agenzia_id` AS `agenzia_id`,
              `d`.`polizza` AS `polizza`,
              `d`.`data` AS `data`,
              `d`.`dataEffetto` AS `dataEffetto`,
              `d`.`prodotto` AS `prodotto`,
              `d`.`motivo` AS `motivo`,
              `d`.`tipoTrasformazione` AS `tipoTrasformazione`,
              `d`.`partenza` AS `partenza`,
              `d`.`arrivo` AS `arrivo`,
              `d`.`premio` AS `premio`,
              `d`.`cf` AS `cf`,
              `d`.`codiceCliente` AS `codiceCliente`,
              `d`.`nominativo` AS `nominativo`,
              `s`.`sogliaMA` AS `sogliaMA`,
              `s`.`sogliaMX` AS `sogliaMX`
       FROM
            (
                (
                    (
                        (
                            `portaleagendo`.`inc_focus_data` `d`
                                JOIN `portaleagendo`.`agenzie` `a`
                                ON
                                  ((`a`.`id` = `d`.`agenzia_id`))
                            )
                            JOIN(
                                SELECT
                                       `a`.`district` AS `district`,
                                       SUM(`s`.`sogliaMA`) AS `sogliaMA`,
                                       SUM(`s`.`sogliaMX`) AS `sogliaMX`
                                FROM
                                     (
                                         (
                                             `portaleagendo`.`inc_focus_static` `s`
                                                 JOIN `portaleagendo`.`agenzie` `a`
                                                 ON
                                                   ((`a`.`id` = `s`.`agenzia_id`))
                                             )
                                             JOIN `portaleagendo`.`geo_districts` `dis`
                                             ON
                                               ((`dis`.`id` = `a`.`district`))
                                         )
                                GROUP BY
                                         `dis`.`id`
                                ) `s`
                            ON
                              ((`s`.`district` = `a`.`district`))
                        )
                        JOIN `portaleagendo`.`geo_districts` `dis`
                        ON
                          ((`dis`.`id` = `a`.`district`))
                    )
                    JOIN `portaleagendo`.`geo_aree` `are`
                    ON
                      ((`are`.`id` = `a`.`area`))
                )
       GROUP BY
                `d`.`agenzia_id`,
                `d`.`cf`,
                `d`.`tipoTrasformazione`
       ) `a`
  GROUP BY
           `a`.`district`;


drop view if exists vw_inc_focus_status;
CREATE VIEW vw_inc_focus_status AS SELECT
     `sta`.`agenzia_id` AS `agenzia_id`,
     `sta`.`totMA` AS `totMA`,
     `sta`.`totMANOA` AS `totMANOA`,
     `sta`.`objMA` AS `objMA`,
     `sta`.`objMAOk` AS `objMAOk`,
     `sta`.`totMX` AS `totMX`,
     `sta`.`objMX` AS `objMX`,
     `sta`.`objMXOk` AS `objMXOk`,
     `sta`.`bonusMA` AS `bonusMA`,
     `sta`.`bonusMX` AS `bonusMX`,
     `sta`.`bonus` AS `bonus`,
     `sta`.`bonusTot` AS `bonusTot`,
     `sta`.`superbonus` AS `superbonus`,
     `sta`.`superbonus2xMA` AS `superbonus2xMA`,
     `sta`.`superbonus2xMX` AS `superbonus2xMX`,
     `sta`.`superbonus2x` AS `superbonus2x`,
     `sta`.`booster` AS `booster`,
     `sta`.`G1_MA_ARGENTO` AS `G1_MA_ARGENTO`,
     `sta`.`G1_MX_ARGENTO` AS `G1_MX_ARGENTO`,
     `sta`.`G1_MA_ORO_PLATINO` AS `G1_MA_ORO_PLATINO`,
     `sta`.`G1_MX_ORO_PLATINO` AS `G1_MX_ORO_PLATINO`,
     `sta`.`G1_AA_ORO_PLATINO` AS `G1_AA_ORO_PLATINO`,
     `sta`.`G2_MX_ARGENTO` AS `G2_MX_ARGENTO`,
     `sem`.`bonus` AS `semestreBonusTot`,
     `sem`.`bonusMA` AS `semestreBonusMA`,
     `sem`.`ratioMA` AS `ratioMA`,
     `sem`.`totMA` AS `semestreTotMA`,
     `sem`.`soglia70NumMA` AS `soglia70NumMA`,
     `sem`.`soglia80NumMA` AS `soglia80NumMA`,
     `sem`.`soglia70FlagMA` AS `soglia70FlagMA`,
     `sem`.`soglia80FlagMA` AS `soglia80FlagMA`,
     `sem`.`bonusMX` AS `semestreBonusMX`,
     `sem`.`ratioMX` AS `ratioMX`,
     `sem`.`totMX` AS `semestreTotMX`,
     `sem`.`soglia70NumMX` AS `soglia70NumMX`,
     `sem`.`soglia80NumMX` AS `soglia80NumMX`,
     `sem`.`soglia70FlagMX` AS `soglia70FlagMX`,
     `sem`.`soglia80FlagMX` AS `soglia80FlagMX`
   FROM
     (
         `portaleagendo`.`inc_focus_status` `sta`
         JOIN `portaleagendo`.`inc_focus_semester` `sem`
           ON
             (
               (
                 `sta`.`agenzia_id` = `sem`.`agenzia_id`
               )
               )
     );