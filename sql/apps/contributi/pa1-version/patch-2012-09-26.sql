ALTER TABLE  `gestcontrib_budgetArea`
	CHANGE `av` `agVinc`	tinyint(1) unsigned NOT NULL default 0,
	DROP `createdAt`,
	DROP INDEX `area_anno_av`,
	ADD UNIQUE KEY `uk_budgetArea` (`area`,`anno`,`agVinc`)
;
ALTER TABLE  `gestcontrib_budgetDirezione`
	CHANGE `av` `agVinc`	tinyint(1) unsigned NOT NULL default 0,
	DROP `budgetPROD`,
	DROP `budgetORG`,
	DROP `budgetPUBBL`,
	DROP `createdAt`,
	DROP INDEX `area_anno_av`,
	ADD UNIQUE KEY `uk_budgetDirezione` (`area`,`anno`,`agVinc`)
;
ALTER TABLE  `gestcontrib_contributi`
	CHANGE `av` `agVinc`	tinyint(1) unsigned NOT NULL default 0
;


DROP VIEW IF EXISTS `vw_gestcontrib_contributi`;
CREATE VIEW `vw_gestcontrib_contributi` AS
SELECT
	c.*,
	ag.area AS agenziaArea,
	ag.localita AS agenziaLocalita,
	ag.nome AS agenziaNome
FROM
	`gestcontrib_contributi` c
	LEFT JOIN `agenzie` ag ON ( c.agenzia = ag.id )
;


DROP TRIGGER IF EXISTS `triggerAI_gestcontrib_contributi`;
DROP TRIGGER IF EXISTS `triggerAU_gestcontrib_contributi`;
DROP TRIGGER IF EXISTS `triggerAD_gestcontrib_contributi`;

DELIMITER //
CREATE TRIGGER `triggerAI_gestcontrib_contributi` AFTER INSERT ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF NEW.budget = 'AREA' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
		IF NEW.budget = 'DIREZ' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
	END; //
CREATE TRIGGER `triggerAU_gestcontrib_contributi` AFTER UPDATE ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF NEW.budget = 'AREA' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
		IF NEW.budget = 'DIREZ' THEN
			IF NEW.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(NEW.anno, (SELECT `area` FROM `agenzie` WHERE `id` = NEW.agenzia), 0);
		END IF;
	END; //
CREATE TRIGGER `triggerAD_gestcontrib_contributi` AFTER DELETE ON `gestcontrib_contributi`
	FOR EACH ROW BEGIN
		IF OLD.budget = 'AREA' THEN
			IF OLD.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetArea`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetArea`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 0);
		END IF;
		IF OLD.budget = 'DIREZ' THEN
			IF OLD.agVinc = 1 THEN
				CALL `gestcontrib_updateBudgetDirezione`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 1);
			END IF;
			CALL `gestcontrib_updateBudgetDirezione`(OLD.anno, (SELECT `area` FROM `agenzie` WHERE `id` = OLD.agenzia), 0);
		END IF;
	END; //
DELIMITER ;



DROP PROCEDURE IF EXISTS `gestcontrib_updateBudgetArea`;
DELIMITER //
CREATE PROCEDURE `gestcontrib_updateBudgetArea` (
	IN p_anno	year,
	IN p_area	tinyint,
	IN p_agVinc tinyint
)
BEGIN

	DECLARE v_budgetPROD				decimal(12,2);
	DECLARE v_budgetORG					decimal(12,2);
	DECLARE v_budgetPUBBL				decimal(12,2);

	DECLARE v_erogatiPROD_VITA			decimal(12,2);
	DECLARE v_erogatiORG_VITA			decimal(12,2);
	DECLARE v_erogatiPUBBL_VITA			decimal(12,2);
	DECLARE v_erogatiPROD_DANN			decimal(12,2);
	DECLARE v_erogatiORG_DANN			decimal(12,2);
	DECLARE v_erogatiPUBBL_DANN			decimal(12,2);

	DECLARE v_impegniPROD_VITA			decimal(12,2);
	DECLARE v_impegniORG_VITA			decimal(12,2);
	DECLARE v_impegniPUBBL_VITA			decimal(12,2);
	DECLARE v_impegniPROD_DANN			decimal(12,2);
	DECLARE v_impegniORG_DANN			decimal(12,2);
	DECLARE v_impegniPUBBL_DANN			decimal(12,2);

	DECLARE v_aggregatoPROD_DANN_GA		decimal(12,2);
	DECLARE v_aggregatoPROD_DANN_NT		decimal(12,2);
	DECLARE v_aggregatoPROD_VITA_GA		decimal(12,2);
	DECLARE v_aggregatoPROD_VITA_NT		decimal(12,2);
	DECLARE v_aggregatoORG_DANN_GA		decimal(12,2);
	DECLARE v_aggregatoORG_DANN_NT		decimal(12,2);
	DECLARE v_aggregatoORG_VITA_GA		decimal(12,2);
	DECLARE v_aggregatoORG_VITA_NT		decimal(12,2);
	DECLARE v_aggregatoPUBBL_DANN_GA	decimal(12,2);
	DECLARE v_aggregatoPUBBL_DANN_NT	decimal(12,2);
	DECLARE v_aggregatoPUBBL_VITA_GA	decimal(12,2);
	DECLARE v_aggregatoPUBBL_VITA_NT	decimal(12,2);


	SELECT 	`budgetPROD`, `budgetORG`, `budgetPUBBL` INTO v_budgetPROD, v_budgetORG, v_budgetPUBBL
		FROM `gestcontrib_budgetArea`
		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

	/* EROGATI */

	SELECT SUM(`importo`) INTO v_erogatiPROD_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiPROD_VITA IS NULL THEN SET v_erogatiPROD_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiORG_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiORG_VITA IS NULL THEN SET v_erogatiORG_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPUBBL_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_erogatiPUBBL_VITA IS NULL THEN SET v_erogatiPUBBL_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPROD_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiPROD_DANN IS NULL THEN SET v_erogatiPROD_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiORG_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiORG_DANN IS NULL THEN SET v_erogatiORG_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_erogatiPUBBL_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_erogatiPUBBL_DANN IS NULL THEN SET v_erogatiPUBBL_DANN = 0; END IF;

	/* IMPEGNATI */

	SELECT SUM(`importo`) INTO v_impegniPROD_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniPROD_VITA IS NULL THEN SET v_impegniPROD_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniORG_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniORG_VITA IS NULL THEN SET v_impegniORG_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPUBBL_VITA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agVinc` >= p_agVinc;
	IF v_impegniPUBBL_VITA IS NULL THEN SET v_impegniPUBBL_VITA = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPROD_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniPROD_DANN IS NULL THEN SET v_impegniPROD_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniORG_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniORG_DANN IS NULL THEN SET v_impegniORG_DANN = 0; END IF;

	SELECT SUM(`importo`) INTO v_impegniPUBBL_DANN
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'DRAFT' AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agVinc` >= p_agVinc;
	IF v_impegniPUBBL_DANN IS NULL THEN SET v_impegniPUBBL_DANN = 0; END IF;

	/* AGGREGATI */

	SELECT SUM(`importo`) INTO v_aggregatoPROD_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_DANN_GA IS NULL THEN SET v_aggregatoPROD_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_DANN_NT IS NULL THEN SET v_aggregatoPROD_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_VITA_GA IS NULL THEN SET v_aggregatoPROD_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPROD_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPROD_VITA_NT IS NULL THEN SET v_aggregatoPROD_VITA_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_DANN_GA IS NULL THEN SET v_aggregatoORG_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_DANN_NT IS NULL THEN SET v_aggregatoORG_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_VITA_GA IS NULL THEN SET v_aggregatoORG_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoORG_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoORG_VITA_NT IS NULL THEN SET v_aggregatoORG_VITA_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_DANN_GA IS NULL THEN SET v_aggregatoPUBBL_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_DANN_NT IS NULL THEN SET v_aggregatoPUBBL_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_VITA_GA IS NULL THEN SET v_aggregatoPUBBL_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_aggregatoPUBBL_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND (`status` = 'ACTIVE' OR `status` = 'DRAFT') AND `budget` = 'AREA' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_aggregatoPUBBL_VITA_NT IS NULL THEN SET v_aggregatoPUBBL_VITA_NT = 0; END IF;

	UPDATE `gestcontrib_budgetArea` SET
		erogatiPROD_VITA = v_erogatiPROD_VITA, erogatiORG_VITA = v_erogatiORG_VITA, erogatiPUBBL_VITA = v_erogatiPUBBL_VITA,
		erogatiPROD_DANN = v_erogatiPROD_DANN, erogatiORG_DANN = v_erogatiORG_DANN, erogatiPUBBL_DANN = v_erogatiPUBBL_DANN,
		impegniPROD_VITA = v_impegniPROD_VITA, impegniORG_VITA = v_impegniORG_VITA, impegniPUBBL_VITA = v_impegniPUBBL_VITA,
		impegniPROD_DANN = v_impegniPROD_DANN, impegniORG_DANN = v_impegniORG_DANN, impegniPUBBL_DANN = v_impegniPUBBL_DANN,

		disponibilePROD	 = budgetPROD	- v_erogatiPROD_VITA	- v_erogatiPROD_DANN	- v_impegniPROD_VITA	- v_impegniPROD_DANN,
		disponibileORG	 = budgetORG	- v_erogatiORG_VITA		- v_erogatiORG_DANN		- v_impegniORG_VITA		- v_impegniORG_DANN,
		disponibilePUBBL = budgetPUBBL 	- v_erogatiPUBBL_VITA	- v_erogatiPUBBL_DANN	- v_impegniPUBBL_VITA	- v_impegniPUBBL_DANN,

		aggregatoPROD_DANN_GA	= v_aggregatoPROD_DANN_GA,	aggregatoPROD_DANN_NT	= v_aggregatoPROD_DANN_NT,
		aggregatoPROD_VITA_GA	= v_aggregatoPROD_VITA_GA,	aggregatoPROD_VITA_NT	= v_aggregatoPROD_VITA_NT,
		aggregatoORG_DANN_GA	= v_aggregatoORG_DANN_GA,	aggregatoORG_DANN_NT	= v_aggregatoORG_DANN_NT,
		aggregatoORG_VITA_GA	= v_aggregatoORG_VITA_GA,	aggregatoORG_VITA_NT	= v_aggregatoORG_VITA_NT,
		aggregatoPUBBL_DANN_GA	= v_aggregatoPUBBL_DANN_GA,	aggregatoPUBBL_DANN_NT	= v_aggregatoPUBBL_DANN_NT,
		aggregatoPUBBL_VITA_GA	= v_aggregatoPUBBL_VITA_GA,	aggregatoPUBBL_VITA_NT	= v_aggregatoPUBBL_VITA_NT

		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

END; //
DELIMITER ;



DROP PROCEDURE IF EXISTS `gestcontrib_updateBudgetDirezione`;
DELIMITER //
CREATE PROCEDURE `gestcontrib_updateBudgetDirezione` (
	IN p_anno	year,
	IN p_area	tinyint,
	IN p_agVinc tinyint
)
BEGIN

	DECLARE v_spesePROD_DANN_GA		decimal(12,2);
	DECLARE v_spesePROD_DANN_NT		decimal(12,2);
	DECLARE v_spesePROD_VITA_GA		decimal(12,2);
	DECLARE v_spesePROD_VITA_NT		decimal(12,2);

	DECLARE v_speseORG_DANN_GA		decimal(12,2);
	DECLARE v_speseORG_DANN_NT		decimal(12,2);
	DECLARE v_speseORG_VITA_GA		decimal(12,2);
	DECLARE v_speseORG_VITA_NT		decimal(12,2);

	DECLARE v_spesePUBBL_DANN_GA	decimal(12,2);
	DECLARE v_spesePUBBL_DANN_NT	decimal(12,2);
	DECLARE v_spesePUBBL_VITA_GA	decimal(12,2);
	DECLARE v_spesePUBBL_VITA_NT	decimal(12,2);

	/* PROD */

	SELECT SUM(`importo`) INTO v_spesePROD_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_DANN_GA IS NULL THEN SET v_spesePROD_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_DANN_NT IS NULL THEN SET v_spesePROD_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_VITA_GA IS NULL THEN SET v_spesePROD_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePROD_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PROD' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePROD_VITA_NT IS NULL THEN SET v_spesePROD_VITA_NT = 0; END IF;

	/* ORG */

	SELECT SUM(`importo`) INTO v_speseORG_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_DANN_GA IS NULL THEN SET v_speseORG_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_DANN_NT IS NULL THEN SET v_speseORG_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_VITA_GA IS NULL THEN SET v_speseORG_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_speseORG_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'ORG' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_speseORG_VITA_NT IS NULL THEN SET v_speseORG_VITA_NT = 0; END IF;

	/* PUBBL */

	SELECT SUM(`importo`) INTO v_spesePUBBL_DANN_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_DANN_GA IS NULL THEN SET v_spesePUBBL_DANN_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_DANN_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'D' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_DANN_NT IS NULL THEN SET v_spesePUBBL_DANN_NT = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_VITA_GA
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'G%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_VITA_GA IS NULL THEN SET v_spesePUBBL_VITA_GA = 0; END IF;

	SELECT SUM(`importo`) INTO v_spesePUBBL_VITA_NT
		FROM `vw_gestcontrib_contributi`
		WHERE `anno` = p_anno AND `agenziaArea` = p_area AND `status` = 'ACTIVE' AND `budget` = 'DIREZ' AND `tipo` = 'PUBBL' AND `ramo` = 'V' AND `agenzia` LIKE 'N%' AND `agVinc` >= p_agVinc;
	IF v_spesePUBBL_VITA_NT IS NULL THEN SET v_spesePUBBL_VITA_NT = 0; END IF;


	UPDATE `gestcontrib_budgetDirezione` SET
		spesePROD_DANN_GA = v_spesePROD_DANN_GA, spesePROD_DANN_NT = v_spesePROD_DANN_NT, spesePROD_VITA_GA = v_spesePROD_VITA_GA, spesePROD_VITA_NT = v_spesePROD_VITA_NT,
		speseORG_DANN_GA = v_speseORG_DANN_GA, speseORG_DANN_NT = v_speseORG_DANN_NT, speseORG_VITA_GA = v_speseORG_VITA_GA, speseORG_VITA_NT = v_speseORG_VITA_NT,
		spesePUBBL_DANN_GA = v_spesePUBBL_DANN_GA, spesePUBBL_DANN_NT = v_spesePUBBL_DANN_NT, spesePUBBL_VITA_GA = v_spesePUBBL_VITA_GA, spesePUBBL_VITA_NT = v_spesePUBBL_VITA_NT
		WHERE `area` = p_area AND `anno` = p_anno AND `agVinc` = p_agVinc;

END; //
DELIMITER ;

