{"name": "animate.css", "version": "3.7.0", "main": "animate.css", "repository": {"type": "git", "url": "https://github.com/daneden/animate.css.git"}, "author": {"name": "<PERSON>"}, "homepage": "http://daneden.me/animate", "license": "MIT", "style": "./animate.css", "jspm": {"main": "animate.css!", "format": "global", "directories": {"lib": "./"}}, "devDependencies": {"autoprefixer": "^9.0.1", "cssnano": "^4.0.3", "eslint": "^5.2.0", "gulp": "^4.0.0", "gulp-concat": "^2.6.1", "gulp-header": "^2.0.1", "gulp-postcss": "^7.0.1", "gulp-rename": "^1.2.2", "gulp-util": "^3.0.8", "husky": "^0.14.3", "lint-staged": "^7.0.0", "prettier": "^1.10.2", "run-sequence": "^2.2.1"}, "lint-staged": {"*.{js,json,md,css}": ["prettier --write", "git add"]}, "prettier": {"bracketSpacing": false, "proseWrap": "never", "singleQuote": true, "trailingComma": "all"}, "scripts": {"gulp": "./node_modules/gulp/bin/gulp.js", "start": "gulp", "prettier": "prettier --write \"**/*.{js,json,md,css}\"", "precommit": "lint-staged"}, "spm": {"main": "./animate.css"}}