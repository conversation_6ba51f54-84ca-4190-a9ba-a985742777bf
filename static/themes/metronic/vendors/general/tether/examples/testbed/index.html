<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="chrome=1">
        <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
        <link rel="stylesheet" href="../resources/css/base.css" />
        <link rel="stylesheet" href="../common/css/style.css" />
    </head>
    <body>

        <div class="element">
        </div>

        <div class="container">
            <div class="pad"></div>
            <div class="target"></div>
            <div class="pad"></div>
            <div class="pad"></div>
        </div>

        <script src="//github.hubspot.com/tether/dist/js/tether.js"></script>
        <script>
            new Tether({
                element: '.element',
                target: '.target',
                attachment: 'top center',
                targetAttachment: 'bottom center',
                constraints: [{
                  to: 'scrollParent',
                  attachment: 'together'
                }]
            });
        </script>
    </body>
  </html>
