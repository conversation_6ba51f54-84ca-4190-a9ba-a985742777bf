<!doctype html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>Tether – Marrying elements for life</title>
    <meta name="description" content="Tether is a JavaScript and CSS library. It is free and open source and was developed by HubSpot developers <PERSON> (@adamfschwartz) and <PERSON> (@zackbloom).">
    <link rel="icon" href="http://static.hubspot.com/favicon.ico">

    <script type="text/javascript" src="//use.typekit.net/ghy0wve.js"></script>
    <script type="text/javascript">try{Typekit.load();}catch(e){}</script>

    <!-- Tether themes -->
    <link rel="stylesheet" href="//github.hubspot.com/tether/dist/css/tether-theme-arrows-dark.css" />

    <!-- Welcome docs styles -->
    <link rel="stylesheet" href="//github.hubspot.com/tether/docs/welcome/css/prism.css" />
    <link rel="stylesheet" href="//github.hubspot.com/tether/docs/welcome/css/welcome.css" />

    <!-- OS icons -->
    <link rel="stylesheet" href="http://github.hubspot.com/os-icons/os-icons.css" />
</head>
<body>
    <div class="hero-wrap">
        <table class="showcase hero"><tr><td>
            <div class="showcase-inner">
                <div class="tether-target-demo">
                    <h1>Tether</h1>
                    <div class="mobile-copy">
                        <h2>Marrying elements for life</h2>
                        <p>
                            <a class="button" href="http://github.com/HubSpot/tether">★ On Github</a>
                        </p>
                    </div>
                </div>
            </div>
        </td></tr></table>
    </div>

    <div class="browser-demo-start-point"></div>

    <table class="showcase browser-demo"><tr><td>
        <div class="showcase-inner">
            <div class="section-copy" data-section="what">
                <h2>What is Tether?</h2>
            </div>
            <div class="section-copy" data-section="how">
                <h2>How Tether works.</h2>
            </div>
            <div class="section-copy" data-section="why">
                <h2>Tether is powerful.</h2>
            </div>
            <div class="section-copy" data-section="outro">
                <h2>Play with Tether</h2>
            </div>
            <div class="browser-demo-inner">
                <div class="browser-window">
                    <div class="browser-titlebar">
                        <div class="browser-dots"><b></b><b></b><b></b></div>
                    </div>
                    <div class="browser-frame">
                        <iframe src="browser-demo.html"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase browser-demo-section no-next-arrow" data-section="what"><tr><td>
        <div class="section-scroll-copy">
            <div class="section-scroll-copy-inner">
                <p>Tether is a low-level UI library that can be used to position any element on a page <i>next to any other element</i>.</p>
                <p>It can be used for dropdown menus, tooltips, popovers, tours, help information, scroll guides, autocompletes, etc. The possibilities are endless.</p>
                <p class="example-paragraph">In this example we're showing an action menu <em>tethered</em> to a list item.</p>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase browser-demo-section no-next-arrow" data-section="how"><tr><td>
        <div class="section-scroll-copy">
            <div class="section-scroll-copy-inner">
                <p>Tether works by creating an absolutely positioned element and meticulously tracking the movements of a <i>target</i> which you specify.</p>
                <p>The <i>target</i> and <i>element</i> can be tethered together in a variety of different ways.</p>
                <p class="example-paragraph">Notice how the <i>tethered element</i> stays tethered to its <i>target</i> list item even as the left pane is scrolled up and down.</p>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase browser-demo-section no-next-arrow" data-section="why"><tr><td>
        <div class="section-scroll-copy">
            <div class="section-scroll-copy-inner">
                <p>Tether can keep your element positioned properly even in some tough situations.</p>
                <p>Tether handles all of the common pain points:</p>
                <ul>
                    <li>Automatically detect collisions with the edge of the page or edge of the scrollParent</li>
                    <li>Automatically reposition on browser resize, scroll, and other events,</li>
                    <li>Constrain the position to any bounding box,</li>
                </ul>
                <p>...and a lot more.</p>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase browser-demo-section no-next-arrow" data-section="outro"><tr><td>
        <div class="section-scroll-copy">
            <div class="section-scroll-copy-inner">
                <p class="example-paragraph">Interact with this demo.</p>
                <p>&nbsp;</p>
                <p>To learn more, check out our <a href="/">documentation</a>.</p>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase browser-demo-section no-next-arrow" data-section="__empty"><tr><td></td></tr></table>

    <div class="browser-demo-stop-point"></div>

    <table class="showcase projects-showcase no-next-arrow"><tr><td>
        <div class="showcase-inner">
            <h1>Tether Family</h1>
            <h2>These projects are all powered by Tether's positioning engine.</h2>
            <div class="projects-list">
                <a href="//github.hubspot.com/drop/docs/welcome" class="project">
                    <h1>Drop</h1>
                    <span class="os-icon drop-icon"></span>
                    <p>Create dropdowns, popovers, and more.</p>
                </a>
                <a href="//github.hubspot.com/tooltip/docs/welcome" class="project">
                    <h1>Tooltip</h1>
                    <span class="os-icon tooltip-icon"></span>
                    <p>Stylable tooltips built on Tether.</p>
                </a>
                <a href="//github.hubspot.com/select/docs/welcome" class="project">
                    <h1>Select</h1>
                    <span class="os-icon select-icon"></span>
                    <p>Stylable select elements built on Tether.</p>
                </a>
                <a href="//github.hubspot.com/shepherd/docs/welcome" class="project">
                    <h1>Shepherd</h1>
                    <span class="os-icon shepherd-icon"></span>
                    <p>Guide your users through a tour of your app.</p>
                </a>
            </div>
        </div>
    </td></tr></table>

    <table class="showcase last-showcase no-next-arrow share"><tr><td>
        <div class="showcase-inner">
            <h1>Share</h1>
            <h2>Help us spread the word.</h2>
            <!-- Share -->
            <style>
                .share-buttons {
                    margin: 4em auto;
                    text-align: center;
                }
                .share-button {
                    display: inline-block;
                }
                .retweet-button {
                    width: 100px;
                    margin-left: 20px;
                }
                .github-stars {
                    width: 100px;
                }
            </style>
            <div class="share-buttons">
                <div class="share-button retweet-button">
                    <a href="http://twitter.com/share" class="twitter-share-button" data-url="http://github.hubspot.com/tether/docs/welcome" data-text="Tether.js - A positioning engine for JavaScript" data-count="horizontal" data-via="HubSpotDev">Tweet</a>
                    <script>
                        (function(){
                            var recommends, button;

                            if (Math.random() >= 0.5) {
                                recommends = ['hubspotdev', 'zackbloom', 'adamfschwartz'];
                            } else {
                                recommends = ['hubspotdev', 'adamfschwartz', 'zackbloom'];
                            }

                            button = document.querySelector('.twitter-share-button');

                            if (button) {
                                button.setAttribute('data-related', recommends.join(','));
                            }
                        })();
                    </script>
                    <script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
                </div>
                <div class="share-button github-stars-button">
                    <iframe src="http://ghbtns.com/github-btn.html?user=HubSpot&amp;repo=tether&amp;type=watch&amp;count=true&amp;size=small" allowtransparency="true" frameborder="0" scrolling="0" width="100" height="20"></iframe>
                </div>
            </p>
        </div>
    </td></tr></table>

    <!-- Tether javascript -->
    <script src="//github.hubspot.com/tether/dist/js/tether.min.js"></script>

    <!-- Welcome docs javascript -->
    <script src="//github.hubspot.com/tether/docs/welcome/js/log.js"></script>
    <script src="//github.hubspot.com/tether/docs/welcome/js/jquery.js"></script>
    <script src="//github.hubspot.com/tether/docs/welcome/js/drop.js"></script>
    <script src="//github.hubspot.com/tether/docs/welcome/js/welcome.js"></script>

    <!-- HubSpot analytics -->
    <script type="text/javascript">
        (function(d,s,i,r) {
            if (d.getElementById(i)){return;}
            var n=d.createElement(s),e=d.getElementsByTagName(s)[0];
            n.id=i;n.src='//js.hubspot.com/analytics/'+(Math.ceil(new Date()/r)*r)+'/51294.js';
            e.parentNode.insertBefore(n, e);
        })(document,"script","hs-analytics",300000);
    </script>

    <!-- Google analytics -->
    <script>
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
        (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

        ga('create', 'UA-45159009-1', 'auto');
        ga('send', 'pageview');
    </script>
</body>
</html>
