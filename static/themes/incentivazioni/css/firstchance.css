/* lato-regular - latin */
@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/lato-v17-latin-regular.eot'); /* IE9 Compat Modes */
    src: local(''),
    url('../fonts/lato-v17-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/lato-v17-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/lato-v17-latin-regular.woff') format('woff'), /* Modern Browsers */
    url('../fonts/lato-v17-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/lato-v17-latin-regular.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-italic - latin */
@font-face {
    font-family: 'Lato';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/lato-v17-latin-italic.eot'); /* IE9 Compat Modes */
    src: local(''),
    url('../fonts/lato-v17-latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/lato-v17-latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/lato-v17-latin-italic.woff') format('woff'), /* Modern Browsers */
    url('../fonts/lato-v17-latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/lato-v17-latin-italic.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-700 - latin */
@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/lato-v17-latin-700.eot'); /* IE9 Compat Modes */
    src: local(''),
    url('../fonts/lato-v17-latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/lato-v17-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/lato-v17-latin-700.woff') format('woff'), /* Modern Browsers */
    url('../fonts/lato-v17-latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/lato-v17-latin-700.svg#Lato') format('svg'); /* Legacy iOS */
}
/* lato-900 - latin */
@font-face {
    font-family: 'Lato';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/lato-v17-latin-900.eot'); /* IE9 Compat Modes */
    src: local(''),
    url('../fonts/lato-v17-latin-900.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/lato-v17-latin-900.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/lato-v17-latin-900.woff') format('woff'), /* Modern Browsers */
    url('../fonts/lato-v17-latin-900.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/lato-v17-latin-900.svg#Lato') format('svg'); /* Legacy iOS */
}


/* BODY :: START */
* {
    font-family: 'Lato', sans-serif;
}

html {
    font-size: 16px;
}

body {
    background-color: #e4dcd0;
}
/* BODY :: END */

.mini-menu {
    margin-bottom: 60px;
}

.divider {
    font-size: 32px;
    line-height: 100%;
    margin: 0 10px;
}

.mini-menu a {
    color: #1c1c1b;
    font-size: 20px;
}

.iframe-container {
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 30px;
    height: 0;
    overflow: hidden;
}

@media (max-width: 767px) {

    .mobile-full-width {
        padding: 0!important;
    }

    /*.iframe-container {
        position: relative;
        padding-bottom: 130%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }*/

}

.card-firstchance {
    border-radius: 20px;
    padding: 20px;
    background-color: #cfc0ae;
    -webkit-box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
    font-size: 20px;
    font-weight: 700;
}

.card-firstchance.otp {
    border-radius: 20px;
    background-color: #897867;
    box-shadow: none;
}

.card-firstchance .title {
    display: inline-block;
    padding: 5px 15px;
    font-weight: 700;
    border-radius: 20px;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.card-firstchance .title i {
    font-size: 14px;
}

.card-firstchance .title.pu {
    color: #FFFFFF;
    background-color: #1d70b7;
}
.card-firstchance .title.pt {
    color: #cfc0ae;
    background-color: #FFFFFF;
}
.card-firstchance .title.op {
    color: #FFFFFF;
    background-color: #e30513;
}

.card-firstchance .title.sb {
    color: #897867;
    background-color: #e3dbd0;
}

.incentive {
    font-size: 28px;
    font-weight: 700;
    text-align: right;
    color: #FFFFFF;
}

@media (max-width: 1179px) {

    .card-firstchance {
        font-size: 16px;
    }

    .card-firstchance .incentive {
        font-size: 22px;
    }

}

/* Contenitore progressbar */
.progressbar-container {
    position: relative;
    border-radius: 15px;
    height: 50px;
    display: flex;
    background-color: #FFFFFF;
    -webkit-box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
    box-shadow: 0px 0px 15px 0px rgba(0,0,0,0.3);
}

/* Progressbar */
.progressbar-container .progressbar {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);

    background-color: #d6c7b5;
    height: 20px;
    transition: all 1s ease;
}

.table thead th {
    font-weight: 700;
    font-size: 16px;
    color: #897967;
    border-top: 1px solid #1D71B8;
    border-bottom: 1px solid #1D71B8;
    text-align: right;
}

.table thead th a {
    color: #1D71B8 !important;
    text-transform: none;
    text-decoration: underline !important;
    cursor: pointer;
}

.table thead th span {
    text-transform: uppercase;
}

.table thead th:first-child {
    text-align: left;
}

.table td {
    font-size: 14px;
    border: 0;
    color: #000000;
}

.table td:first-child {
    font-weight: 700;
    color: #000000;
    text-transform: uppercase;
}

.table td a {
    font-size: 14px;
    font-weight: 700;
    color: #000000;
    text-decoration: underline;
    text-transform: uppercase;
    cursor: pointer;
}

.table td a:hover {
    color: #1D71B8;
    text-decoration: underline;
}

.table tbody tr:nth-child(even) {
    background-color: #d7c8b5;
}

.table .total {
    background-color: transparent !important;
}

.table .total td {
    border-top: 3px solid #1D71B8;
    border-bottom: 3px solid #1D71B8;
    font-weight: 900;
}

.dot {
    height: 30px;
    width: 30px;
    border-radius: 50%;
    background-color: #e3dbd0;
    margin-right: 6px;
    position: relative;
}

.dot .full {
    color: #897867;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.separator {
    width: 2px;
    height: 50px;
    margin: 0 3px;
    background-color: #e3dbd0;
    overflow: visible;
    position: relative;
}

.notch {
    position: absolute;
    bottom: -20px;
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 700;
    left: 50%;
    transform: translateX(-50%);
}

.arrow-up {
    position: absolute;
    top: -15px;
    right: 25px;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;

    border-bottom: 20px solid #897867;
}