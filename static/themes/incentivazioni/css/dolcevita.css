/* montserrat-regular - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/montserrat-v25-latin-regular.eot'); /* IE9 Compat Modes */
    src: url('../fonts/montserrat-v25-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/montserrat-v25-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/montserrat-v25-latin-regular.woff') format('woff'), /* Modern Browsers */
    url('../fonts/montserrat-v25-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/montserrat-v25-latin-regular.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-italic - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/montserrat-v25-latin-italic.eot'); /* IE9 Compat Modes */
    src: url('../fonts/montserrat-v25-latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/montserrat-v25-latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/montserrat-v25-latin-italic.woff') format('woff'), /* Modern Browsers */
    url('../fonts/montserrat-v25-latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/montserrat-v25-latin-italic.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-500 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 500;
    src: url('../fonts/montserrat-v25-latin-500.eot'); /* IE9 Compat Modes */
    src: url('../fonts/montserrat-v25-latin-500.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/montserrat-v25-latin-500.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/montserrat-v25-latin-500.woff') format('woff'), /* Modern Browsers */
    url('../fonts/montserrat-v25-latin-500.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/montserrat-v25-latin-500.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-600 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 600;
    src: url('../fonts/montserrat-v25-latin-600.eot'); /* IE9 Compat Modes */
    src: url('../fonts/montserrat-v25-latin-600.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/montserrat-v25-latin-600.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/montserrat-v25-latin-600.woff') format('woff'), /* Modern Browsers */
    url('../fonts/montserrat-v25-latin-600.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/montserrat-v25-latin-600.svg#Montserrat') format('svg'); /* Legacy iOS */
}
/* montserrat-900 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/montserrat-v25-latin-900.eot'); /* IE9 Compat Modes */
    src: url('../fonts/montserrat-v25-latin-900.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/montserrat-v25-latin-900.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/montserrat-v25-latin-900.woff') format('woff'), /* Modern Browsers */
    url('../fonts/montserrat-v25-latin-900.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/montserrat-v25-latin-900.svg#Montserrat') format('svg'); /* Legacy iOS */
}


/* BODY :: START */
* {
    font-family: 'Montserrat', sans-serif;
}

html {
    font-size: 16px;
}

body {
    background: #8ed1e4;
    color: #ffffff;
    font-weight: 400;
}

.mini-menu {
    margin-bottom: 60px;
}

.divider {
    font-size: 32px;
    line-height: 100%;
    margin: 0 10px;
}

.mini-menu a {
    color: #1c1c1b;
    font-size: 20px;
}

.embed-container {
    position: relative;
    height: 0;
    overflow: hidden;
    max-width: 100%;

}

.embed-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.tag {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    padding: 20px 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
}

.tag > div {
    font-weight: 900;
    line-height: 1;
}

.tag > div:first-child {
    font-size: 16px;
}

.tag > div:last-child {
    font-size: 25px;
}

.folder-label {
    height: 50px;
    display: inline-flex;
    align-items: center;
    font-weight: 900;
    font-size: 16px;
    line-height: normal;
    min-width: 150px;
    padding: 0 15px;
}

.folder {
    padding: 40px 40px 90px;
}

.tcm .folder-label {
    background-color: #E94E1B;
}
.tcm .folder {
    background-color: #E94E1B;
}

.pi .folder-label {
    background-color: #F9B233;
}
.pi .folder {
    background-color: #F9B233;
}

.open .folder-label {
    background-color: #679BB5;
}
.open .folder {
    background-color: #679BB5;
}

/* Contenitore progressbar */
.progressbar-container {
    position: relative;
    height: 25px;
    display: flex;
    background-color: transparent;
    border: 2px solid #000000;
}

/* Progressbar */
.progressbar-container .progressbar {
    background-color: #FFFFFF;
    height: 100%;
    transition: all 1s ease;
}

.set > div {
    padding: 5px 30px;
    text-align: center;
    line-height: normal;
}

.set div:first-child {
    border-bottom: 2px solid #000000;
    color: #000000;
    font-weight: 500;
    font-size: 16px;
}

.set div:last-child {
    color: #FFFFFF;
    font-size: 22px;
}

.shadow {
    -webkit-box-shadow: 0px 10px 20px 0px rgba(133,133,133,1);
    -moz-box-shadow: 0px 10px 20px 0px rgba(133,133,133,1);
    box-shadow: 0px 10px 20px 0px rgba(133,133,133,1);
}

.opacity-5 {
    opacity: 0.5;
}

.arrow-down {
    position: absolute;
    bottom: -14px;
    right: 31%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid #8ed1e4;
}

.arrow-up {
    position: absolute;
    top: -14px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 15px solid #8ed1e4;
}

/* Ipad mini landscape */
@media (max-width: 1023px) {

    .tag {
        padding: 20px 20px;
    }

    .tag > div:first-child {
        font-size: 14px;
    }

}

.table th {
    color: #AB533F;
    font-size: 12px;
    font-weight: 600;
    text-align: right;
    border-top: 3px solid #ffffff;
    border-bottom: 1px solid #ffffff;
}

.table th a,
.table td a {
    color: #010202;
    font-size: 14px;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
}

.table tr td:first-child {
    font-weight: 600;
}

.table td {
    text-align: right;
    font-weight: 300;
}

.table tbody tr:nth-child(even) {
    background-color: #c5e6f1;
}

.table tbody tr:last-child {
    background-color: transparent;
}

.table .total td {
    border-top: 3px solid #ffffff;
    border-bottom: 3px solid #ffffff;
    color: #AB533F;
}