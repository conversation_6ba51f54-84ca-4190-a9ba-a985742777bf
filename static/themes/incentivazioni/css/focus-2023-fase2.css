/* nunito-regular - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 400;
    src: url('../fonts/nunito-v25-latin-regular.eot'); /* IE9 Compat Modes */
    src: url('../fonts/nunito-v25-latin-regular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/nunito-v25-latin-regular.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/nunito-v25-latin-regular.woff') format('woff'), /* Modern Browsers */
    url('../fonts/nunito-v25-latin-regular.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/nunito-v25-latin-regular.svg#Nunito') format('svg'); /* Legacy iOS */
}
/* nunito-italic - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Nunito';
    font-style: italic;
    font-weight: 400;
    src: url('../fonts/nunito-v25-latin-italic.eot'); /* IE9 Compat Modes */
    src: url('../fonts/nunito-v25-latin-italic.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/nunito-v25-latin-italic.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/nunito-v25-latin-italic.woff') format('woff'), /* Modern Browsers */
    url('../fonts/nunito-v25-latin-italic.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/nunito-v25-latin-italic.svg#Nunito') format('svg'); /* Legacy iOS */
}
/* nunito-700 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 700;
    src: url('../fonts/nunito-v25-latin-700.eot'); /* IE9 Compat Modes */
    src: url('../fonts/nunito-v25-latin-700.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/nunito-v25-latin-700.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/nunito-v25-latin-700.woff') format('woff'), /* Modern Browsers */
    url('../fonts/nunito-v25-latin-700.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/nunito-v25-latin-700.svg#Nunito') format('svg'); /* Legacy iOS */
}
/* nunito-900 - latin */
@font-face {
    font-display: swap; /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
    font-family: 'Nunito';
    font-style: normal;
    font-weight: 900;
    src: url('../fonts/nunito-v25-latin-900.eot'); /* IE9 Compat Modes */
    src: url('../fonts/nunito-v25-latin-900.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
    url('../fonts/nunito-v25-latin-900.woff2') format('woff2'), /* Super Modern Browsers */
    url('../fonts/nunito-v25-latin-900.woff') format('woff'), /* Modern Browsers */
    url('../fonts/nunito-v25-latin-900.ttf') format('truetype'), /* Safari, Android, iOS */
    url('../fonts/nunito-v25-latin-900.svg#Nunito') format('svg'); /* Legacy iOS */
}


/* BODY :: START */
* {
    font-family: 'Nunito', sans-serif;
}

html {
    font-size: 16px;
    height: 100vh;
}

body {
    background-color: #00475c;
    height: 100vh;
    color: #ffffff;
}
/* BODY :: END */

.mini-menu {
    margin-bottom: 60px;
}

.divider {
    font-size: 32px;
    line-height: 100%;
    margin: 0 10px;
}

.mini-menu a {
    color: #ffffff;
    font-size: 20px;
}

.previous-year-comparison {
    background-color: #dddc00;
    color: #FFFFFF;
    padding: 3px;
    -webkit-box-shadow: 0px 0px 30px -1px rgba(191,189,191,1);
    -moz-box-shadow: 0px 0px 30px -1px rgba(191,189,191,1);
    box-shadow: 0px 0px 30px -1px rgba(191,189,191,1);
}

.previous-year-comparison .label {
    padding: 8px 0;
    background-color: #FFFFFF;

    color: #dddc00;
    text-align: center;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    line-height: normal;
}

.previous-year-comparison .percentage {
    padding: 8px 0;
    background-color: #dddc00;

    text-align: center;
    font-size: 29px;
    font-weight: 900;
    line-height: normal;
}

.period {
    border: 3px solid #2daae1;
    border-right: 0;
    text-align: center;
    padding: 8px 0;
    font-size: 17px;
    color: #000000;
    line-height: normal;
}

.period-highlighter .period:last-child {
    border-right: 3px solid #2daae1;
}

.period.current {
    background-color: #2daae1;
    font-weight: 900;
    color: #FFFFFF;
}

.period.future {
    color: #a6a6a6;
}

.block {
    display: flex;
    flex-direction: column;
    border: 3px solid #2daae1;
    height: 100%;
}

.block .label {
    font-size: 14px;
    font-weight: 700;
    color: #2daae1;
    text-align: center;
    padding: 12px 0 0;
    text-transform: uppercase;
    line-height: normal;
}

.block .value {
    font-size: 29px;
    font-weight: 900;
    color: #dddc00;
    text-align: center;
    text-transform: uppercase;
}

.block.total {
    background-color: #2daae1;
    padding-bottom: 30px;
}

.block.total .label {
    color: #FFFFFF;
}

.block.total .value {
    color: #FFFFFF;
}

.operator {
    font-size: 32px;
    font-weight: 700;
    color: #2daae1;
}

.incentive-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 1fr;
    grid-column-gap: 0px;
    grid-row-gap: 0px;
    align-content: space-evenly;
}

.mini-block {
    text-align: center;
    border-color: #2daae1;
    border-style: solid;
    border-width: 3px;
    margin: 0 -3px 0 -3px;
    padding: 4px;
}

.mini-block:first-child {
    margin: 0 -3px 0 0;
}

.mini-block:last-child {
    margin: 0 0 0 -3px;
}

.mini-block.active {
    border-width: 6px;
    margin: 0;
}

.mini-block > div {
    line-height: normal;
    color: #ffffff;
}

.table thead th {
    font-weight: 700;
    font-size: 16px;
    color: #2D2D2D;
    border-top: 1px solid #D1C9BE;
    border-bottom: 2px solid #D1C9BE;
    vertical-align: middle;
}

.table thead th a {
    color: #818181;
    text-transform: none;
    text-decoration: underline !important;
    cursor: pointer;
}

.table thead th span {
    text-transform: uppercase;
}

.table thead th:first-child {
    text-align: left;
}

.table td {
    font-size: 14px;
    border: 0;
    color: #2D2D2D;
    vertical-align: middle;
}

.table td:first-child {
    font-weight: 700;
    color: #2D2D2D;
}

.table td a {
    font-size: 14px;
    font-weight: 700;
    color: #2D2D2D;
    text-decoration: none;
    cursor: pointer;
}

.table td a:hover {
    color: #2D2D2D;
    text-decoration: underline !important;
}

.table tbody tr:nth-child(even) {
    background-color: #F7F6F4;
}

.table .total {
    background-color: transparent !important;
}

.table .total td {
    border-top: 2px solid #D1C9BE;
    border-bottom: 2px solid #D1C9BE;
}

.table .conversions {
    color: #75B54B;
}

.table.front thead th {
    color: #FFFFFF;
}

.table.front td {
    color: #FFFFFF;
}

.table.front tbody tr:nth-child(even) td {
    color: #2D2D2D;
}

.progress {
    height: 1px;
    align-items: center;
    overflow: visible;
    background-color: #818181;
}

.progress-bar {
    height: 7px;
    overflow: visible;
    border-radius: 5px;
}

.auto .progress-bar {
    background-color: #5DB0FF;
}

.monoarea .progress-bar {
    background-color: #BB45C5;
}

.btn-toggle {
    color: #2daae1;
    font-size: 25px;
}

.btn-toggle:hover {
    color: #2b74e1;
}

.modal-title {
    color: #2D2D2D;
    font-size: 20px;
    text-transform: uppercase;
    font-weight: 700;
}

.iframe-container {
    position: relative;
    padding-bottom: 45%;
    height: 0;
    overflow: hidden;
}

.mobile-menu {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #835965;
    padding: 30px;
    z-index: 10;
}

.menu-item {
    font-size: 16px;
    color: #ffffff;
    margin-right: 40px;
}

.menu-item:hover {
    text-decoration: underline!important;
    color: #ffffff;
}

.menu-item.active {
    font-weight: 700;
}

.modal-dialog {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    min-height: calc(100% - 1rem);
    margin: 0 auto;
}

.incentive-total {
    padding: 10px 30px;
    margin-top: -50px;

    background-color: #ffffff;
    border: 20px solid #00475c;
    border-radius: 50px;

    display: flex;
    align-items: center;
    justify-content: space-between;

    font-weight: 900;
    color: #66BB05;
    line-height: 1;
}

.grid-converted {
    display: grid;
    grid-auto-rows: auto;
    grid-template-columns: 1fr 1fr 1fr 2fr;
    gap: 0 10px;
    align-items: center;

    color: #ffffff;
}

.grid-pill {
    border: 1px solid #ffffff;
    border-radius: 30px;
    padding: 5px 0;

    text-align: center;
    font-weight: 700;
    font-size: 24px;
    line-height: normal;
}

.rotate {
    transform: rotate(180deg);
}

.border-right {
    border-right: 1px solid #ffffff;
}

.desaturated {
    color: #96747e;
}

@media (max-width: 767px) {

    .mobile-full-width {
        padding: 0!important;
    }

    .iframe-container {
        position: relative;
        padding-bottom: 130%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }

    .period {
        padding: 12px 0;
    }
    .period:nth-child(-n+2) {
        border-bottom-width: 0;
    }
    .period:nth-child(2) {
        border-right: 3px solid #2daae1;
    }

    .block {
        padding-bottom: 20px;
    }

}

@media (max-width: 1024px) {

    .border-right {
        border: 0 !important;
    }

}