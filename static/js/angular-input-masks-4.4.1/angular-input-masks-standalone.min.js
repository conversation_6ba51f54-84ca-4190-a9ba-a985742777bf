!function i(s,u,o){function l(t,e){if(!u[t]){if(!s[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(c)return c(t,!0);var n=new Error("Cannot find module '"+t+"'");throw n.code="MODULE_NOT_FOUND",n}var a=u[t]={exports:{}};s[t][0].call(a.exports,function(e){return l(s[t][1][e]||e)},a,a.exports,i,s,u,o)}return u[t].exports}for(var c="function"==typeof require&&require,e=0;e<o.length;e++)l(o[e]);return l}({1:[function(e,t,r){var n,a;n=this,a=function(){var e={validate:function(e){var t=[6,5,4,3,2,9,8,7,6,5,4,3,2];if(!(e=e.replace(/[^\d]/g,""))||14!==e.length||/^(0{14}|1{14}|2{14}|3{14}|4{14}|5{14}|6{14}|7{14}|8{14}|9{14})$/.test(e))return!1;e=e.split("");for(var r=0,n=0;r<12;r++)n+=e[r]*t[r+1];if(n=10<=(n=11-n%11)?0:n,parseInt(e[12])!==n)return!1;for(n=r=0;r<=12;r++)n+=e[r]*t[r];return n=10<=(n=11-n%11)?0:n,parseInt(e[13])===n}},t={validate:function(a){if(!(a=a.replace(/[^\d]+/g,""))||11!==a.length||/^(0{11}|1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11})$/.test(a))return!1;function e(e){for(var t=0,r=e-9,n=0;n<9;n++)t+=parseInt(a.charAt(n+r))*(n+1);return t%11%10===parseInt(a.charAt(e))}return e(9)&&e(10)}},r=function(e){if(!(this instanceof r))return new r(e);this.rules=n[e]||[],this.rule,r.prototype._defineRule=function(e){this.rule=void 0;for(var t=0;t<this.rules.length&&void 0===this.rule;t++){var r=e.replace(/[^\d]/g,""),n=this.rules[t];r.length!==n.chars||n.match&&!n.match.test(e)||(this.rule=n)}return!!this.rule},r.prototype.validate=function(e){return!(!e||!this._defineRule(e))&&this.rule.validate(e)}},n={},l={handleStr:{onlyNumbers:function(e){return e.replace(/[^\d]/g,"").split("")},mgSpec:function(e){var t=e.replace(/[^\d]/g,"");return(t=t.substr(0,3)+"0"+t.substr(3,t.length)).split("")}},sum:{normalSum:function(e,t){for(var r=e,n=0,a=0;a<t.length;a++)n+=parseInt(r[a])*t[a];return n},individualSum:function(e,t){for(var r=e,n=0,a=0;a<t.length;a++){var i=parseInt(r[a])*t[a];n+=i%10+parseInt(i/10)}return n},apSpec:function(e,t){var r=this.normalSum(e,t),n=e.join("");return"030000010"<=n&&n<="030170009"?r+5:"030170010"<=n&&n<="030190229"?r+9:r}},rest:{mod11:function(e){return e%11},mod10:function(e){return e%10},mod9:function(e){return e%9}},expectedDV:{minusRestOf11:function(e){return e<2?0:11-e},minusRestOf11v2:function(e){return e<2?11-e-10:11-e},minusRestOf10:function(e){return e<1?0:10-e},mod10:function(e){return e%10},goSpec:function(e,t){var r=t.join("");return 1===e?"101031050"<=r&&r<="101199979"?1:0:0===e?0:11-e},apSpec:function(e,t){var r=t.join("");return 0===e?"030170010"<=r&&r<="030190229"?1:0:1===e?0:11-e},voidFn:function(e){return e}}};function a(e,t){for(var r=0;r<t.dvs.length;r++)if(n=e,a=t.dvs[r],void 0,i=a.algorithmSteps,s=l.handleStr[i[0]](n),u=l.sum[i[1]](s,a.pesos),o=l.rest[i[2]](u),parseInt(s[a.dvpos])!==l.expectedDV[i[3]](o,s))return!1;var n,a,i,s,u,o;return!0}n.PE=[{chars:9,dvs:[{dvpos:7,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}},{chars:14,pesos:[[1,2,3,4,5,9,8,7,6,5,4,3,2]],dvs:[{dvpos:13,pesos:[5,4,3,2,1,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11v2"]}],validate:function(e){return a(e,this)}}],n.RS=[{chars:10,dvs:[{dvpos:9,pesos:[2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.AC=[{chars:13,match:/^01/,dvs:[{dvpos:11,pesos:[4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:12,pesos:[5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.MG=[{chars:13,dvs:[{dvpos:12,pesos:[1,2,1,2,1,2,1,2,1,2,1,2],algorithmSteps:["mgSpec","individualSum","mod10","minusRestOf10"]},{dvpos:12,pesos:[3,2,11,10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.SP=[{chars:12,match:/^[0-9]/,dvs:[{dvpos:8,pesos:[1,3,4,5,6,7,8,10],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]},{dvpos:11,pesos:[3,2,10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]}],validate:function(e){return a(e,this)}},{chars:12,match:/^P/i,dvs:[{dvpos:8,pesos:[1,3,4,5,6,7,8,10],algorithmSteps:["onlyNumbers","normalSum","mod11","mod10"]}],validate:function(e){return a(e,this)}}],n.DF=[{chars:13,dvs:[{dvpos:11,pesos:[4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:12,pesos:[5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.ES=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.BA=[{chars:8,match:/^[0123458]/,dvs:[{dvpos:7,pesos:[7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]},{dvpos:6,pesos:[8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]}],validate:function(e){return a(e,this)}},{chars:8,match:/^[679]/,dvs:[{dvpos:7,pesos:[7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:6,pesos:[8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}},{chars:9,match:/^[0-9][0123458]/,dvs:[{dvpos:8,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]},{dvpos:7,pesos:[9,8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod10","minusRestOf10"]}],validate:function(e){return a(e,this)}},{chars:9,match:/^[0-9][679]/,dvs:[{dvpos:8,pesos:[8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:7,pesos:[9,8,7,6,5,4,3,0,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.AM=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.RN=[{chars:9,match:/^20/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}},{chars:10,match:/^20/,dvs:[{dvpos:8,pesos:[10,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.RO=[{chars:14,dvs:[{dvpos:13,pesos:[6,5,4,3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11v2"]}],validate:function(e){return a(e,this)}}],n.PR=[{chars:10,dvs:[{dvpos:8,pesos:[3,2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]},{dvpos:9,pesos:[4,3,2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.SC=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.RJ=[{chars:8,dvs:[{dvpos:7,pesos:[2,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.PA=[{chars:9,match:/^15/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.SE=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.PB=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.CE=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.PI=[{chars:9,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.MA=[{chars:9,match:/^12/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.MT=[{chars:11,dvs:[{dvpos:10,pesos:[3,2,9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.MS=[{chars:9,match:/^28/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.TO=[{chars:11,match:/^[0-9]{2}((0[123])|(99))/,dvs:[{dvpos:10,pesos:[9,8,0,0,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.AL=[{chars:9,match:/^24[03578]/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","minusRestOf11"]}],validate:function(e){return a(e,this)}}],n.RR=[{chars:9,match:/^24/,dvs:[{dvpos:8,pesos:[1,2,3,4,5,6,7,8],algorithmSteps:["onlyNumbers","normalSum","mod9","voidFn"]}],validate:function(e){return a(e,this)}}],n.GO=[{chars:9,match:/^1[015]/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","normalSum","mod11","goSpec"]}],validate:function(e){return a(e,this)}}],n.AP=[{chars:9,match:/^03/,dvs:[{dvpos:8,pesos:[9,8,7,6,5,4,3,2],algorithmSteps:["onlyNumbers","apSpec","mod11","apSpec"]}],validate:function(e){return a(e,this)}}];var i={validate:function(e){if(!(e=e.replace(/[^\d]+/g,""))||11!==e.length||/^(0{11}|1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11})$/.test(e))return!1;var t=e.substring(0,10),r=e.substring(10);return Number(r)===function(e){for(var t=[3,2,9,8,7,6,5,4,3,2],r=0,n=0;n<=9;n++)r+=parseInt(e.charAt(n))*t[n];var a=11-r%11;return 10==a||11==a?0:a}(t)}};return{ie:r,cpf:t,cnpj:e,pis:i}},"function"==typeof define&&define.amd?define([],a):"object"==typeof r?t.exports=a():n.BrV=a()},{}],2:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n=(0,i.default)(e,r),a=Number(t);return n.setUTCMinutes(n.getUTCMinutes()+a),n};var n,a=e("../../toDate/index.js"),i=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../../toDate/index.js":35}],3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){e=e||{};var t={};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return t},t.exports=r.default},{}],4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=(0,s.default)(e,t),n=r.getTime();r.setUTCMonth(0,1),r.setUTCHours(0,0,0,0);var a=r.getTime(),i=n-a;return Math.floor(i/u)+1};var n,a=e("../../toDate/index.js"),s=(n=a)&&n.__esModule?n:{default:n};var u=864e5;t.exports=r.default},{"../../toDate/index.js":35}],5:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=(0,a.default)(e,t),n=(0,i.default)(r,t).getTime()-(0,s.default)(r,t).getTime();return Math.round(n/u)+1};var a=n(e("../../toDate/index.js")),i=n(e("../startOfUTCISOWeek/index.js")),s=n(e("../startOfUTCISOWeekYear/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}var u=6048e5;t.exports=r.default},{"../../toDate/index.js":35,"../startOfUTCISOWeek/index.js":11,"../startOfUTCISOWeekYear/index.js":12}],6:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=(0,o.default)(e,t),n=r.getUTCFullYear(),a=new Date(0);a.setUTCFullYear(n+1,0,4),a.setUTCHours(0,0,0,0);var i=(0,l.default)(a,t),s=new Date(0);s.setUTCFullYear(n,0,4),s.setUTCHours(0,0,0,0);var u=(0,l.default)(s,t);return r.getTime()>=i.getTime()?n+1:r.getTime()>=u.getTime()?n:n-1};var o=n(e("../../toDate/index.js")),l=n(e("../startOfUTCISOWeek/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}t.exports=r.default},{"../../toDate/index.js":35,"../startOfUTCISOWeek/index.js":11}],7:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n=r||{},a=n.locale,i=a&&a.options&&a.options.weekStartsOn,s=void 0===i?0:Number(i),u=void 0===n.weekStartsOn?s:Number(n.weekStartsOn);if(!(0<=u&&u<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var o=(0,f.default)(e,r),l=Number(t),c=o.getUTCDay(),d=((l%7+7)%7<u?7:0)+l-c;return o.setUTCDate(o.getUTCDate()+d),o};var n,a=e("../../toDate/index.js"),f=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../../toDate/index.js":35}],8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n=Number(t);n%7==0&&(n-=7);var a=(0,u.default)(e,r),i=a.getUTCDay(),s=((n%7+7)%7<1?7:0)+n-i;return a.setUTCDate(a.getUTCDate()+s),a};var n,a=e("../../toDate/index.js"),u=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../../toDate/index.js":35}],9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n=(0,s.default)(e,r),a=Number(t),i=(0,u.default)(n,r)-a;return n.setUTCDate(n.getUTCDate()-7*i),n};var s=n(e("../../toDate/index.js")),u=n(e("../getUTCISOWeek/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}t.exports=r.default},{"../../toDate/index.js":35,"../getUTCISOWeek/index.js":5}],10:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n=(0,o.default)(e,r),a=Number(t),i=(0,l.default)(n,r),s=Math.floor((n.getTime()-i.getTime())/c),u=new Date(0);return u.setUTCFullYear(a,0,4),u.setUTCHours(0,0,0,0),(n=(0,l.default)(u,r)).setUTCDate(n.getUTCDate()+s),n};var o=n(e("../../toDate/index.js")),l=n(e("../startOfUTCISOWeekYear/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}var c=864e5;t.exports=r.default},{"../../toDate/index.js":35,"../startOfUTCISOWeekYear/index.js":12}],11:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=(0,i.default)(e,t),n=r.getUTCDay(),a=(n<1?7:0)+n-1;return r.setUTCDate(r.getUTCDate()-a),r.setUTCHours(0,0,0,0),r};var n,a=e("../../toDate/index.js"),i=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../../toDate/index.js":35}],12:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){var r=(0,a.default)(e,t),n=new Date(0);return n.setUTCFullYear(r,0,4),n.setUTCHours(0,0,0,0),(0,i.default)(n,t)};var a=n(e("../getUTCISOWeekYear/index.js")),i=n(e("../startOfUTCISOWeek/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}t.exports=r.default},{"../getUTCISOWeekYear/index.js":6,"../startOfUTCISOWeek/index.js":11}],13:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){if(arguments.length<2)throw new TypeError("2 arguments required, but only "+arguments.length+" present");var n=(0,i.default)(e,r).getTime(),a=Number(t);return new Date(n+a)};var n,a=e("../toDate/index.js"),i=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../toDate/index.js":35}],14:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){if(arguments.length<2)throw new TypeError("2 arguments required, but only "+arguments.length+" present");var n=Number(t);return(0,i.default)(e,n*s,r)};var n,a=e("../addMilliseconds/index.js"),i=(n=a)&&n.__esModule?n:{default:n};var s=6e4;t.exports=r.default},{"../addMilliseconds/index.js":13}],15:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=s(e("../../../_lib/getUTCDayOfYear/index.js")),a=s(e("../../../_lib/getUTCISOWeek/index.js")),i=s(e("../../../_lib/getUTCISOWeekYear/index.js"));function s(e){return e&&e.__esModule?e:{default:e}}var u={M:function(e){return e.getUTCMonth()+1},Mo:function(e,t){var r=e.getUTCMonth()+1;return t.locale.localize.ordinalNumber(r,{unit:"month"})},MM:function(e){return l(e.getUTCMonth()+1,2)},MMM:function(e,t){return t.locale.localize.month(e.getUTCMonth(),{type:"short"})},MMMM:function(e,t){return t.locale.localize.month(e.getUTCMonth(),{type:"long"})},Q:function(e){return Math.ceil((e.getUTCMonth()+1)/3)},Qo:function(e,t){var r=Math.ceil((e.getUTCMonth()+1)/3);return t.locale.localize.ordinalNumber(r,{unit:"quarter"})},D:function(e){return e.getUTCDate()},Do:function(e,t){return t.locale.localize.ordinalNumber(e.getUTCDate(),{unit:"dayOfMonth"})},DD:function(e){return l(e.getUTCDate(),2)},DDD:function(e){return(0,n.default)(e)},DDDo:function(e,t){return t.locale.localize.ordinalNumber((0,n.default)(e),{unit:"dayOfYear"})},DDDD:function(e){return l((0,n.default)(e),3)},dd:function(e,t){return t.locale.localize.weekday(e.getUTCDay(),{type:"narrow"})},ddd:function(e,t){return t.locale.localize.weekday(e.getUTCDay(),{type:"short"})},dddd:function(e,t){return t.locale.localize.weekday(e.getUTCDay(),{type:"long"})},d:function(e){return e.getUTCDay()},do:function(e,t){return t.locale.localize.ordinalNumber(e.getUTCDay(),{unit:"dayOfWeek"})},E:function(e){return e.getUTCDay()||7},W:function(e){return(0,a.default)(e)},Wo:function(e,t){return t.locale.localize.ordinalNumber((0,a.default)(e),{unit:"isoWeek"})},WW:function(e){return l((0,a.default)(e),2)},YY:function(e){return l(e.getUTCFullYear(),4).substr(2)},YYYY:function(e){return l(e.getUTCFullYear(),4)},GG:function(e){return String((0,i.default)(e)).substr(2)},GGGG:function(e){return(0,i.default)(e)},H:function(e){return e.getUTCHours()},HH:function(e){return l(e.getUTCHours(),2)},h:function(e){var t=e.getUTCHours();return 0===t?12:12<t?t%12:t},hh:function(e){return l(u.h(e),2)},m:function(e){return e.getUTCMinutes()},mm:function(e){return l(e.getUTCMinutes(),2)},s:function(e){return e.getUTCSeconds()},ss:function(e){return l(e.getUTCSeconds(),2)},S:function(e){return Math.floor(e.getUTCMilliseconds()/100)},SS:function(e){return l(Math.floor(e.getUTCMilliseconds()/10),2)},SSS:function(e){return l(e.getUTCMilliseconds(),3)},Z:function(e,t){return o((t._originalDate||e).getTimezoneOffset(),":")},ZZ:function(e,t){return o((t._originalDate||e).getTimezoneOffset())},X:function(e,t){var r=t._originalDate||e;return Math.floor(r.getTime()/1e3)},x:function(e,t){return(t._originalDate||e).getTime()},A:function(e,t){return t.locale.localize.timeOfDay(e.getUTCHours(),{type:"uppercase"})},a:function(e,t){return t.locale.localize.timeOfDay(e.getUTCHours(),{type:"lowercase"})},aa:function(e,t){return t.locale.localize.timeOfDay(e.getUTCHours(),{type:"long"})}};function o(e,t){t=t||"";var r=0<e?"-":"+",n=Math.abs(e),a=n%60;return r+l(Math.floor(n/60),2)+t+l(a,2)}function l(e,t){for(var r=Math.abs(e).toString();r.length<t;)r="0"+r;return r}r.default=u,t.exports=r.default},{"../../../_lib/getUTCDayOfYear/index.js":4,"../../../_lib/getUTCISOWeek/index.js":5,"../../../_lib/getUTCISOWeekYear/index.js":6}],16:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){if(arguments.length<2)throw new TypeError("2 arguments required, but only "+arguments.length+" present");var n=String(t),a=r||{},i=a.locale||h.default;if(!i.localize)throw new RangeError("locale must contain localize property");if(!i.formatLong)throw new RangeError("locale must contain formatLong property");var s=i.formatters||{},u=i.formattingTokensRegExp||b,o=i.formatLong,l=(0,m.default)(e,a);if(!(0,p.default)(l,a))return"Invalid Date";var c=l.getTimezoneOffset(),d=(0,y.default)(l,-c,a),f=(0,g.default)(a);return f.locale=i,f.formatters=v.default,f._originalDate=l,n.replace(M,function(e){return"["===e[0]?e:"\\"===e[0]?D(e):o(e)}).replace(u,function(e){var t=s[e]||v.default[e];return t?t(d,f):D(e)})};var m=n(e("../toDate/index.js")),p=n(e("../isValid/index.js")),h=n(e("../locale/en-US/index.js")),v=n(e("./_lib/formatters/index.js")),g=n(e("../_lib/cloneObject/index.js")),y=n(e("../_lib/addUTCMinutes/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}var M=/(\[[^[]*])|(\\)?(LTS|LT|LLLL|LLL|LL|L|llll|lll|ll|l)/g,b=/(\[[^[]*])|(\\)?(x|ss|s|mm|m|hh|h|do|dddd|ddd|dd|d|aa|a|ZZ|Z|YYYY|YY|X|Wo|WW|W|SSS|SS|S|Qo|Q|Mo|MMMM|MMM|MM|M|HH|H|GGGG|GG|E|Do|DDDo|DDDD|DDD|DD|D|A|.)/g;function D(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|]$/g,""):e.replace(/\\/g,"")}t.exports=r.default},{"../_lib/addUTCMinutes/index.js":2,"../_lib/cloneObject/index.js":3,"../isValid/index.js":17,"../locale/en-US/index.js":30,"../toDate/index.js":35,"./_lib/formatters/index.js":15}],17:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");var r=(0,i.default)(e,t);return!isNaN(r)};var n,a=e("../toDate/index.js"),i=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../toDate/index.js":35}],18:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){var t={LTS:e.LTS,LT:e.LT,L:e.L,LL:e.LL,LLL:e.LLL,LLLL:e.LLLL,l:e.l||a(e.L),ll:e.ll||a(e.LL),lll:e.lll||a(e.LLL),llll:e.llll||a(e.LLLL)};return function(e){return t[e]}};var n=/MMMM|MM|DD|dddd/g;function a(e){return e.replace(n,function(e){return e.slice(1)})}t.exports=r.default},{}],19:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(n,a){return function(e){var t=e||{},r=t.type?String(t.type):a;return n[r]||n[a]}},t.exports=r.default},{}],20:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(s,u,o){return function(e,t){var r=t||{},n=r.type?String(r.type):u,a=s[n]||s[u],i=o?o(Number(e)):Number(e);return a[i]}},t.exports=r.default},{}],21:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(s,u){return function(e,t){var r=t||{},n=r.type?String(r.type):u,a=s[n]||s[u],i=String(e);return i.match(a)}},t.exports=r.default},{}],22:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(r){return function(e){var t=String(e);return t.match(r)}},t.exports=r.default},{}],23:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(s,u){return function(e,t){var r=t||{},n=r.type?String(r.type):u,a=s[n]||s[u],i=e[1];return a.findIndex(function(e){return e.test(i)})}},t.exports=r.default},{}],24:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){return parseInt(e[1],10)},t.exports=r.default},{}],25:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){var n;r=r||{},n="string"==typeof a[e]?a[e]:1===t?a[e].one:a[e].other.replace("{{count}}",t);if(r.addSuffix)return 0<r.comparison?"in "+n:n+" ago";return n};var a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};t.exports=r.default},{}],26:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n,a=e("../../../_lib/buildFormatLongFn/index.js");var i=(0,((n=a)&&n.__esModule?n:{default:n}).default)({LT:"h:mm aa",LTS:"h:mm:ss aa",L:"MM/DD/YYYY",LL:"MMMM D YYYY",LLL:"MMMM D YYYY h:mm aa",LLLL:"dddd, MMMM D YYYY h:mm aa"});r.default=i,t.exports=r.default},{"../../../_lib/buildFormatLongFn/index.js":18}],27:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r,n){return a[e]};var a={lastWeek:"[last] dddd [at] LT",yesterday:"[yesterday at] LT",today:"[today at] LT",tomorrow:"[tomorrow at] LT",nextWeek:"dddd [at] LT",other:"L"};t.exports=r.default},{}],28:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=i(e("../../../_lib/buildLocalizeFn/index.js")),a=i(e("../../../_lib/buildLocalizeArrayFn/index.js"));function i(e){return e&&e.__esModule?e:{default:e}}var s={narrow:["Su","Mo","Tu","We","Th","Fr","Sa"],short:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],long:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},u={short:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],long:["January","February","March","April","May","June","July","August","September","October","November","December"]},o={uppercase:["AM","PM"],lowercase:["am","pm"],long:["a.m.","p.m."]};var l={ordinalNumber:function(e,t){var r=Number(e),n=r%100;if(20<n||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},weekday:(0,n.default)(s,"long"),weekdays:(0,a.default)(s,"long"),month:(0,n.default)(u,"long"),months:(0,a.default)(u,"long"),timeOfDay:(0,n.default)(o,"long",function(e){return 1<=e/12?1:0}),timesOfDay:(0,a.default)(o,"long")};r.default=l,t.exports=r.default},{"../../../_lib/buildLocalizeArrayFn/index.js":19,"../../../_lib/buildLocalizeFn/index.js":20}],29:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=u(e("../../../_lib/buildMatchFn/index.js")),a=u(e("../../../_lib/buildParseFn/index.js")),i=u(e("../../../_lib/buildMatchPatternFn/index.js")),s=u(e("../../../_lib/parseDecimal/index.js"));function u(e){return e&&e.__esModule?e:{default:e}}var o={ordinalNumbers:(0,i.default)(/^(\d+)(th|st|nd|rd)?/i),ordinalNumber:s.default,weekdays:(0,n.default)({narrow:/^(su|mo|tu|we|th|fr|sa)/i,short:/^(sun|mon|tue|wed|thu|fri|sat)/i,long:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},"long"),weekday:(0,a.default)({any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},"any"),months:(0,n.default)({short:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,long:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},"long"),month:(0,a.default)({any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},"any"),timesOfDay:(0,n.default)({short:/^(am|pm)/i,long:/^([ap]\.?\s?m\.?)/i},"long"),timeOfDay:(0,a.default)({any:[/^a/i,/^p/i]},"any")};r.default=o,t.exports=r.default},{"../../../_lib/buildMatchFn/index.js":21,"../../../_lib/buildMatchPatternFn/index.js":22,"../../../_lib/buildParseFn/index.js":23,"../../../_lib/parseDecimal/index.js":24}],30:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=o(e("./_lib/formatDistance/index.js")),a=o(e("./_lib/formatLong/index.js")),i=o(e("./_lib/formatRelative/index.js")),s=o(e("./_lib/localize/index.js")),u=o(e("./_lib/match/index.js"));function o(e){return e&&e.__esModule?e:{default:e}}var l={formatDistance:n.default,formatLong:a.default,formatRelative:i.default,localize:s.default,match:u.default,options:{weekStartsOn:0,firstWeekContainsDate:1}};r.default=l,t.exports=r.default},{"./_lib/formatDistance/index.js":25,"./_lib/formatLong/index.js":26,"./_lib/formatRelative/index.js":27,"./_lib/localize/index.js":28,"./_lib/match/index.js":29}],31:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={M:/^(1[0-2]|0?\d)/,D:/^(3[0-1]|[0-2]?\d)/,DDD:/^(36[0-6]|3[0-5]\d|[0-2]?\d?\d)/,W:/^(5[0-3]|[0-4]?\d)/,YYYY:/^(\d{1,4})/,H:/^(2[0-3]|[0-1]?\d)/,m:/^([0-5]?\d)/,Z:/^([+-])(\d{2}):(\d{2})/,ZZ:/^([+-])(\d{2})(\d{2})/,singleDigit:/^(\d)/,twoDigits:/^(\d{2})/,threeDigits:/^(\d{3})/,fourDigits:/^(\d{4})/,anyDigits:/^(\d+)/};function a(e){return parseInt(e[1],10)}var i={YY:{unit:"twoDigitYear",match:n.twoDigits,parse:function(e){return a(e)}},YYYY:{unit:"year",match:n.YYYY,parse:a},GG:{unit:"isoYear",match:n.twoDigits,parse:function(e){return a(e)+1900}},GGGG:{unit:"isoYear",match:n.YYYY,parse:a},Q:{unit:"quarter",match:n.singleDigit,parse:a},Qo:{unit:"quarter",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"quarter"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"quarter"})}},M:{unit:"month",match:n.M,parse:function(e){return a(e)-1}},Mo:{unit:"month",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"month"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"month"})-1}},MM:{unit:"month",match:n.twoDigits,parse:function(e){return a(e)-1}},MMM:{unit:"month",match:function(e,t){return t.locale.match.months(e,{type:"short"})},parse:function(e,t){return t.locale.match.month(e,{type:"short"})}},MMMM:{unit:"month",match:function(e,t){return t.locale.match.months(e,{type:"long"})||t.locale.match.months(e,{type:"short"})},parse:function(e,t){var r=t.locale.match.month(e,{type:"long"});return null==r&&(r=t.locale.match.month(e,{type:"short"})),r}},W:{unit:"isoWeek",match:n.W,parse:a},Wo:{unit:"isoWeek",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"isoWeek"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"isoWeek"})}},WW:{unit:"isoWeek",match:n.twoDigits,parse:a},d:{unit:"dayOfWeek",match:n.singleDigit,parse:a},do:{unit:"dayOfWeek",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"dayOfWeek"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"dayOfWeek"})}},dd:{unit:"dayOfWeek",match:function(e,t){return t.locale.match.weekdays(e,{type:"narrow"})},parse:function(e,t){return t.locale.match.weekday(e,{type:"narrow"})}},ddd:{unit:"dayOfWeek",match:function(e,t){return t.locale.match.weekdays(e,{type:"short"})||t.locale.match.weekdays(e,{type:"narrow"})},parse:function(e,t){var r=t.locale.match.weekday(e,{type:"short"});return null==r&&(r=t.locale.match.weekday(e,{type:"narrow"})),r}},dddd:{unit:"dayOfWeek",match:function(e,t){return t.locale.match.weekdays(e,{type:"long"})||t.locale.match.weekdays(e,{type:"short"})||t.locale.match.weekdays(e,{type:"narrow"})},parse:function(e,t){var r=t.locale.match.weekday(e,{type:"long"});return null==r&&null==(r=t.locale.match.weekday(e,{type:"short"}))&&(r=t.locale.match.weekday(e,{type:"narrow"})),r}},E:{unit:"dayOfISOWeek",match:n.singleDigit,parse:function(e){return a(e)}},D:{unit:"dayOfMonth",match:n.D,parse:a},Do:{unit:"dayOfMonth",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"dayOfMonth"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"dayOfMonth"})}},DD:{unit:"dayOfMonth",match:n.twoDigits,parse:a},DDD:{unit:"dayOfYear",match:n.DDD,parse:a},DDDo:{unit:"dayOfYear",match:function(e,t){return t.locale.match.ordinalNumbers(e,{unit:"dayOfYear"})},parse:function(e,t){return t.locale.match.ordinalNumber(e,{unit:"dayOfYear"})}},DDDD:{unit:"dayOfYear",match:n.threeDigits,parse:a},A:{unit:"timeOfDay",match:function(e,t){return t.locale.match.timesOfDay(e,{type:"short"})},parse:function(e,t){return t.locale.match.timeOfDay(e,{type:"short"})}},aa:{unit:"timeOfDay",match:function(e,t){return t.locale.match.timesOfDay(e,{type:"long"})||t.locale.match.timesOfDay(e,{type:"short"})},parse:function(e,t){var r=t.locale.match.timeOfDay(e,{type:"long"});return null==r&&(r=t.locale.match.timeOfDay(e,{type:"short"})),r}},H:{unit:"hours",match:n.H,parse:a},HH:{unit:"hours",match:n.twoDigits,parse:a},h:{unit:"timeOfDayHours",match:n.M,parse:a},hh:{unit:"timeOfDayHours",match:n.twoDigits,parse:a},m:{unit:"minutes",match:n.m,parse:a},mm:{unit:"minutes",match:n.twoDigits,parse:a},s:{unit:"seconds",match:n.m,parse:a},ss:{unit:"seconds",match:n.twoDigits,parse:a},S:{unit:"milliseconds",match:n.singleDigit,parse:function(e){return 100*a(e)}},SS:{unit:"milliseconds",match:n.twoDigits,parse:function(e){return 10*a(e)}},SSS:{unit:"milliseconds",match:n.threeDigits,parse:a},Z:{unit:"timezone",match:n.Z,parse:function(e){var t=e[1],r=60*parseInt(e[2],10)+parseInt(e[3],10);return"+"===t?r:-r}},ZZ:{unit:"timezone",match:n.ZZ,parse:function(e){var t=e[1],r=60*parseInt(e[2],10)+parseInt(e[3],10);return"+"===t?r:-r}},X:{unit:"timestamp",match:n.anyDigits,parse:function(e){return 1e3*a(e)}},x:{unit:"timestamp",match:n.anyDigits,parse:a}};i.a=i.A,r.default=i,t.exports=r.default},{}],32:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=l(e("../../../_lib/setUTCDay/index.js")),a=l(e("../../../_lib/setUTCISODay/index.js")),i=l(e("../../../_lib/setUTCISOWeek/index.js")),s=l(e("../../../_lib/setUTCISOWeekYear/index.js")),u=l(e("../../../_lib/startOfUTCISOWeek/index.js")),o=l(e("../../../_lib/startOfUTCISOWeekYear/index.js"));function l(e){return e&&e.__esModule?e:{default:e}}var c={twoDigitYear:{priority:10,set:function(e,t){var r=100*Math.floor(e.date.getUTCFullYear()/100)+t;return e.date.setUTCFullYear(r,0,1),e.date.setUTCHours(0,0,0,0),e}},year:{priority:10,set:function(e,t){return e.date.setUTCFullYear(t,0,1),e.date.setUTCHours(0,0,0,0),e}},isoYear:{priority:10,set:function(e,t,r){return e.date=(0,o.default)((0,s.default)(e.date,t,r),r),e}},quarter:{priority:20,set:function(e,t){return e.date.setUTCMonth(3*(t-1),1),e.date.setUTCHours(0,0,0,0),e}},month:{priority:30,set:function(e,t){return e.date.setUTCMonth(t,1),e.date.setUTCHours(0,0,0,0),e}},isoWeek:{priority:40,set:function(e,t,r){return e.date=(0,u.default)((0,i.default)(e.date,t,r),r),e}},dayOfWeek:{priority:50,set:function(e,t,r){return e.date=(0,n.default)(e.date,t,r),e.date.setUTCHours(0,0,0,0),e}},dayOfISOWeek:{priority:50,set:function(e,t,r){return e.date=(0,a.default)(e.date,t,r),e.date.setUTCHours(0,0,0,0),e}},dayOfMonth:{priority:50,set:function(e,t){return e.date.setUTCDate(t),e.date.setUTCHours(0,0,0,0),e}},dayOfYear:{priority:50,set:function(e,t){return e.date.setUTCMonth(0,t),e.date.setUTCHours(0,0,0,0),e}},timeOfDay:{priority:60,set:function(e,t,r){return e.timeOfDay=t,e}},hours:{priority:70,set:function(e,t,r){return e.date.setUTCHours(t,0,0,0),e}},timeOfDayHours:{priority:70,set:function(e,t,r){var n=e.timeOfDay;return null!=n&&(t=function(e,t){if(0===t){if(12===e)return 0}else if(12!==e)return 12+e;return e}(t,n)),e.date.setUTCHours(t,0,0,0),e}},minutes:{priority:80,set:function(e,t){return e.date.setUTCMinutes(t,0,0),e}},seconds:{priority:90,set:function(e,t){return e.date.setUTCSeconds(t,0),e}},milliseconds:{priority:100,set:function(e,t){return e.date.setUTCMilliseconds(t),e}},timezone:{priority:110,set:function(e,t){return e.date=new Date(e.date.getTime()-6e4*t),e}},timestamp:{priority:120,set:function(e,t){return e.date=new Date(t),e}}};r.default=c,t.exports=r.default},{"../../../_lib/setUTCDay/index.js":7,"../../../_lib/setUTCISODay/index.js":8,"../../../_lib/setUTCISOWeek/index.js":9,"../../../_lib/setUTCISOWeekYear/index.js":10,"../../../_lib/startOfUTCISOWeek/index.js":11,"../../../_lib/startOfUTCISOWeekYear/index.js":12}],33:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r,n){if(arguments.length<3)throw new TypeError("3 arguments required, but only "+arguments.length+" present");var a=String(e),i=n||{},s=void 0===i.weekStartsOn?0:Number(i.weekStartsOn);if(!(0<=s&&s<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var u=i.locale||N.default,o=u.parsers||{},l=u.units||{};if(!u.match)throw new RangeError("locale must contain match property");if(!u.formatLong)throw new RangeError("locale must contain formatLong property");var c=String(t).replace(L,function(e){return"["===e[0]?e:"\\"===e[0]?function(e){if(e.match(/\[[\s\S]/))return e.replace(/^\[|]$/g,"");return e.replace(/\\/g,"")}(e):u.formatLong(e)});if(""===c)return""===a?(0,_.default)(r,i):new Date(NaN);var d=(0,$.default)(i);d.locale=u;var f,m=c.match(u.parsingTokensRegExp||P),p=m.length,h=[{priority:U,set:R,index:0}];for(f=0;f<p;f++){var v=m[f],g=o[v]||Y.default[v];if(g){var y;if(!(y=g.match instanceof RegExp?g.match.exec(a):g.match(a,d)))return new Date(NaN);var M=g.unit,b=l[M]||C.default[M];h.push({priority:b.priority,set:b.set,value:g.parse(y,d),index:h.length});var D=y[0];a=a.slice(D.length)}else{var S=m[f].match(/^\[.*]$/)?m[f].replace(/^\[|]$/g,""):m[f];if(0!==a.indexOf(S))return new Date(NaN);a=a.slice(S.length)}}var k=h.map(function(e){return e.priority}).sort(function(e,t){return e-t}).filter(function(e,t,r){return r.indexOf(e)===t}).map(function(t){return h.filter(function(e){return e.priority===t}).reverse()}).map(function(e){return e[0]}),x=(0,_.default)(r,i);if(isNaN(x))return new Date(NaN);var w={date:(0,j.default)(x,x.getTimezoneOffset())},O=k.length;for(f=0;f<O;f++){var T=k[f];w=T.set(w,T.value,d)}return w.date};var _=n(e("../toDate/index.js")),j=n(e("../subMinutes/index.js")),N=n(e("../locale/en-US/index.js")),Y=n(e("./_lib/parsers/index.js")),C=n(e("./_lib/units/index.js")),$=n(e("../_lib/cloneObject/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}var U=110,a=6e4,L=/(\[[^[]*])|(\\)?(LTS|LT|LLLL|LLL|LL|L|llll|lll|ll|l)/g,P=/(\[[^[]*])|(\\)?(x|ss|s|mm|m|hh|h|do|dddd|ddd|dd|d|aa|a|ZZ|Z|YYYY|YY|X|Wo|WW|W|SSS|SS|S|Qo|Q|Mo|MMMM|MMM|MM|M|HH|H|GGGG|GG|E|Do|DDDo|DDDD|DDD|DD|D|A|.)/g;function R(e){var t=e.date,r=t.getTime(),n=t.getTimezoneOffset();return n=new Date(r+n*a).getTimezoneOffset(),e.date=new Date(r+n*a),e}t.exports=r.default},{"../_lib/cloneObject/index.js":3,"../locale/en-US/index.js":30,"../subMinutes/index.js":34,"../toDate/index.js":35,"./_lib/parsers/index.js":31,"./_lib/units/index.js":32}],34:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t,r){if(arguments.length<2)throw new TypeError("2 arguments required, but only "+arguments.length+" present");var n=Number(t);return(0,i.default)(e,-n,r)};var n,a=e("../addMinutes/index.js"),i=(n=a)&&n.__esModule?n:{default:n};t.exports=r.default},{"../addMinutes/index.js":14}],35:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,t){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");if(null===e)return new Date(NaN);var r=t||{},n=void 0===r.additionalDigits?m:Number(r.additionalDigits);if(2!==n&&1!==n&&0!==n)throw new RangeError("additionalDigits must be 0, 1 or 2");{if(e instanceof Date)return new Date(e.getTime());if("string"!=typeof e)return new Date(e)}var a=function(e){var t,r={},n=e.split(p.dateTimeDelimeter);t=p.plainTime.test(n[0])?(r.date=null,n[0]):(r.date=n[0],n[1]);if(t){var a=p.timezone.exec(t);a?(r.time=t.replace(a[1],""),r.timezone=a[1]):r.time=t}return r}(e),i=function(e,t){var r,n=p.YYY[t],a=p.YYYYY[t];if(r=p.YYYY.exec(e)||a.exec(e)){var i=r[1];return{year:parseInt(i,10),restDateString:e.slice(i.length)}}if(r=p.YY.exec(e)||n.exec(e)){var s=r[1];return{year:100*parseInt(s,10),restDateString:e.slice(s.length)}}return{year:null}}(a.date,n),s=i.year,u=function(e,t){if(null===t)return null;var r,n,a,i;if(0===e.length)return(n=new Date(0)).setUTCFullYear(t),n;if(r=p.MM.exec(e))return n=new Date(0),a=parseInt(r[1],10)-1,n.setUTCFullYear(t,a),n;if(r=p.DDD.exec(e)){n=new Date(0);var s=parseInt(r[1],10);return n.setUTCFullYear(t,0,s),n}if(r=p.MMDD.exec(e)){n=new Date(0),a=parseInt(r[1],10)-1;var u=parseInt(r[2],10);return n.setUTCFullYear(t,a,u),n}if(r=p.Www.exec(e))return i=parseInt(r[1],10)-1,h(t,i);if(r=p.WwwD.exec(e)){i=parseInt(r[1],10)-1;var o=parseInt(r[2],10)-1;return h(t,i,o)}return null}(i.restDateString,s);{if(u){var o,l=u.getTime(),c=0;return a.time&&(c=function(e){var t,r,n;if(t=p.HH.exec(e))return(r=parseFloat(t[1].replace(",",".")))%24*d;if(t=p.HHMM.exec(e))return r=parseInt(t[1],10),n=parseFloat(t[2].replace(",",".")),r%24*d+n*f;if(t=p.HHMMSS.exec(e)){r=parseInt(t[1],10),n=parseInt(t[2],10);var a=parseFloat(t[3].replace(",","."));return r%24*d+n*f+1e3*a}return null}(a.time)),o=a.timezone?function(e){var t,r;if(t=p.timezoneZ.exec(e))return 0;if(t=p.timezoneHH.exec(e))return r=60*parseInt(t[2],10),"+"===t[1]?-r:r;if(t=p.timezoneHHMM.exec(e))return r=60*parseInt(t[2],10)+parseInt(t[3],10),"+"===t[1]?-r:r;return 0}(a.timezone):(o=new Date(l+c).getTimezoneOffset(),new Date(l+c+o*f).getTimezoneOffset()),new Date(l+c+o*f)}return new Date(e)}};var d=36e5,f=6e4,m=2,p={dateTimeDelimeter:/[T ]/,plainTime:/:/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timezone:/([Z+-].*)$/,timezoneZ:/^(Z)$/,timezoneHH:/^([+-])(\d{2})$/,timezoneHHMM:/^([+-])(\d{2}):?(\d{2})$/};function h(e,t,r){t=t||0,r=r||0;var n=new Date(0);n.setUTCFullYear(e,0,4);var a=7*t+r+1-(n.getUTCDay()||7);return n.setUTCDate(n.getUTCDate()+a),n}t.exports=r.default},{}],36:[function(e,t,r){var n,a;n=this,a=function(){var h={0:{pattern:/\d/,_default:"0"},9:{pattern:/\d/,optional:!0},"#":{pattern:/\d/,optional:!0,recursive:!0},A:{pattern:/[a-zA-Z0-9]/},S:{pattern:/[a-zA-Z]/},U:{pattern:/[a-zA-Z]/,transform:function(e){return e.toLocaleUpperCase()}},L:{pattern:/[a-zA-Z]/,transform:function(e){return e.toLocaleLowerCase()}},$:{escape:!0}};function v(e,t){for(var r=0,n=t-1,a={escape:!0};0<=n&&a&&a.escape;)r+=(a=h[e.charAt(n)])&&a.escape?1:0,n--;return 0<r&&r%2==1}function g(e,t,r,n){return n&&"function"==typeof n.transform&&(t=n.transform(t)),r.reverse?t+e:e+t}function y(e,t,r){var n=e.split("");return n.splice(r,0,t),n.join("")}function n(e,t){this.options=t||{},this.options={reverse:this.options.reverse||!1,usedefaults:this.options.usedefaults||this.options.reverse},this.pattern=e}return n.prototype.process=function(r){if(!r)return{result:"",valid:!1};r+="";var n=this.pattern,e=!0,t="",a=this.options.reverse?r.length-1:0,i=0,s=function(e,t){var r=e.replace(/[^0]/g,"").length;return t.replace(/[^\d]/g,"").length-r}(n,r),u=!1,o=[],l=!1,c={start:this.options.reverse?n.length-1:0,end:this.options.reverse?-1:n.length,inc:this.options.reverse?-1:1};function d(e){if(!l&&!o.length&&function e(t,r,n){var a=t.charAt(r),i=h[a];return""!==a&&(!(!i||i.escape)||e(t,r+n,n))}(n,i,c.inc))return!0;if(!l&&o.length&&function e(t,r,n){var a=t.charAt(r),i=h[a];return""!==a&&(!(!i||!i.recursive)||e(t,r+n,n))}(n,i,c.inc))return!0;if(l||(l=0<o.length),l){var t=o.shift();if(o.push(t),e.reverse&&0<=a)return n=y(n,t,++i),!0;if(!e.reverse&&a<r.length)return n=y(n,t,i),!0}return i<n.length&&0<=i}for(i=c.start;d(this.options);i+=c.inc){var f=r.charAt(a),m=n.charAt(i),p=h[m];if(o.length&&p&&!p.recursive&&(p=null),!l||f){if(this.options.reverse&&v(n,i)){t=g(t,m,this.options,p),i+=c.inc;continue}if(!this.options.reverse&&u){t=g(t,m,this.options,p),u=!1;continue}if(!this.options.reverse&&p&&p.escape){u=!0;continue}}if(!l&&p&&p.recursive)o.push(m);else{if(l&&!f){t=g(t,m,this.options,p);continue}if(!l&&0<o.length&&!f)continue}if(p)if(p.optional){if(p.pattern.test(f)&&s)t=g(t,f,this.options,p),a+=c.inc,s--;else if(0<o.length&&f){e=!1;break}}else if(p.pattern.test(f))t=g(t,f,this.options,p),a+=c.inc;else{if(f||!p._default||!this.options.usedefaults){e=!1;break}t=g(t,p._default,this.options,p)}else t=g(t,m,this.options,p),!l&&o.length&&o.push(m)}return{result:t,valid:e}},n.prototype.apply=function(e){return this.process(e).result},n.prototype.validate=function(e){return this.process(e).valid},n.process=function(e,t,r){return new n(t,r).process(e)},n.apply=function(e,t,r){return new n(t,r).apply(e)},n.validate=function(e,t,r){return new n(t,r).validate(e)},n},"function"==typeof define&&define.amd?define([],a):"object"==typeof r?t.exports=a():n.StringMask=a()},{}],37:[function(e,t,r){"use strict";t.exports=angular.module("ui.utils.masks",[e("./global/global-masks"),e("./br/br-masks"),e("./ch/ch-masks"),e("./fr/fr-masks"),e("./us/us-masks")]).name},{"./br/br-masks":39,"./ch/ch-masks":49,"./fr/fr-masks":51,"./global/global-masks":55,"./us/us-masks":66}],38:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("00000.00000 00000.000000 00000.000000 0 00000000000000"),s=new n("00000000000-0 00000000000-0 00000000000-0 00000000000-0");t.exports=a({clearValue:function(e){return e.replace(/[^0-9]/g,"").slice(0,48)},format:function(e){return 0===e.length?e:"8"===e[0]?s.apply(e).replace(/[^0-9]$/,""):i.apply(e).replace(/[^0-9]$/,"")},validations:{brBoletoBancario:function(e){return 0<=[47,48].indexOf(e.length)}}})},{"../../helpers/mask-factory":61,"string-mask":36}],39:[function(e,t,r){"use strict";var n=angular.module("ui.utils.masks.br",[]).directive("uiBrBoletoBancarioMask",e("./boleto-bancario/boleto-bancario")).directive("uiBrCarPlateMask",e("./car-plate/car-plate")).directive("uiBrCepMask",e("./cep/cep")).directive("uiBrCnpjMask",e("./cnpj/cnpj")).directive("uiBrCpfMask",e("./cpf/cpf")).directive("uiBrCpfcnpjMask",e("./cpf-cnpj/cpf-cnpj")).directive("uiBrNumeroBeneficioMask",e("./numero-beneficio/numero-beneficio")).directive("uiBrIeMask",e("./inscricao-estadual/ie")).directive("uiNfeAccessKeyMask",e("./nfe/nfe")).directive("uiBrPhoneNumberMask",e("./phone/br-phone"));t.exports=n.name},{"./boleto-bancario/boleto-bancario":38,"./car-plate/car-plate":40,"./cep/cep":41,"./cnpj/cnpj":42,"./cpf-cnpj/cpf-cnpj":43,"./cpf/cpf":44,"./inscricao-estadual/ie":45,"./nfe/nfe":46,"./numero-beneficio/numero-beneficio":47,"./phone/br-phone":48}],40:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("UUU-0000");t.exports=a({clearValue:function(e){return e.replace(/[^a-zA-Z0-9]/g,"").slice(0,7)},format:function(e){return(i.apply(e)||"").replace(/[^a-zA-Z0-9]$/,"")},validations:{carPlate:function(e){return 7===e.length}}})},{"../../helpers/mask-factory":61,"string-mask":36}],41:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("00000-000");t.exports=a({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,8)},format:function(e){return(i.apply(e)||"").replace(/[^0-9]$/,"")},validations:{cep:function(e){return 8===e.toString().trim().length}}})},{"../../helpers/mask-factory":61,"string-mask":36}],42:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("br-validations"),i=e("../../helpers/mask-factory"),s=new n("00.000.000/0000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,14)},format:function(e){return(s.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{cnpj:function(e){return a.cnpj.validate(e)}}})},{"../../helpers/mask-factory":61,"br-validations":1,"string-mask":36}],43:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("br-validations"),i=e("../../helpers/mask-factory"),s=new n("00.000.000/0000-00"),u=new n("000.000.000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,14)},format:function(e){return(11<e.length?s.apply(e):u.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{cpf:function(e){return 11<e.length||a.cpf.validate(e)},cnpj:function(e){return e.length<=11||a.cnpj.validate(e)}}})},{"../../helpers/mask-factory":61,"br-validations":1,"string-mask":36}],44:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("br-validations"),i=e("../../helpers/mask-factory"),s=new n("000.000.000-00");t.exports=i({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,11)},format:function(e){return(s.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{cpf:function(e){return a.cpf.validate(e)}}})},{"../../helpers/mask-factory":61,"br-validations":1,"string-mask":36}],45:[function(e,t,r){"use strict";var n=e("string-mask"),l=e("br-validations"),a={AC:[{mask:new n("00.000.000/000-00")}],AL:[{mask:new n("000000000")}],AM:[{mask:new n("00.000.000-0")}],AP:[{mask:new n("000000000")}],BA:[{chars:8,mask:new n("000000-00")},{mask:new n("0000000-00")}],CE:[{mask:new n("00000000-0")}],DF:[{mask:new n("00000000000-00")}],ES:[{mask:new n("00000000-0")}],GO:[{mask:new n("00.000.000-0")}],MA:[{mask:new n("000000000")}],MG:[{mask:new n("000.000.000/0000")}],MS:[{mask:new n("000000000")}],MT:[{mask:new n("0000000000-0")}],PA:[{mask:new n("00-000000-0")}],PB:[{mask:new n("00000000-0")}],PE:[{chars:9,mask:new n("0000000-00")},{mask:new n("00.0.000.0000000-0")}],PI:[{mask:new n("000000000")}],PR:[{mask:new n("000.00000-00")}],RJ:[{mask:new n("00.000.00-0")}],RN:[{chars:9,mask:new n("00.000.000-0")},{mask:new n("00.0.000.000-0")}],RO:[{mask:new n("0000000000000-0")}],RR:[{mask:new n("00000000-0")}],RS:[{mask:new n("000/0000000")}],SC:[{mask:new n("000.000.000")}],SE:[{mask:new n("00000000-0")}],SP:[{mask:new n("000.000.000.000")},{mask:new n("-00000000.0/000")}],TO:[{mask:new n("00000000000")}]};function i(s){function u(e){return e?e.replace(/[^0-9]/g,""):e}function o(e,t){var r=function(e,t){if(e&&a[e]){if("SP"===e&&/^P/i.test(t))return a.SP[1].mask;for(var r=a[e],n=0;r[n].chars&&r[n].chars<u(t).length&&n<r.length-1;)n++;return r[n].mask}}(t,e);if(!r)return e;var n=r.process(u(e)).result||"";return n=n.trim().replace(/[^0-9]$/,""),"SP"===t&&/^p/i.test(e)?"P"+n:n}return{restrict:"A",require:"ngModel",link:function(e,t,r,n){var a=(s(r.uiBrIeMask)(e)||"").toUpperCase();function i(e){if(n.$isEmpty(e))return e;var t=o(e,a),r=u(t);return n.$viewValue!==t&&(n.$setViewValue(t),n.$render()),a&&"SP"===a.toUpperCase()&&/^p/i.test(e)?"P"+r:r}n.$formatters.push(function(e){return n.$isEmpty(e)?e:o(e,a)}),n.$parsers.push(i),n.$validators.ie=function(e){return n.$isEmpty(e)||l.ie(a).validate(e)},e.$watch(r.uiBrIeMask,function(e){a=(e||"").toUpperCase(),i(n.$viewValue),n.$validate()})}}}i.$inject=["$parse"],t.exports=i},{"br-validations":1,"string-mask":36}],46:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000");t.exports=a({clearValue:function(e){return e.replace(/[^0-9]/g,"").slice(0,44)},format:function(e){return(i.apply(e)||"").replace(/[^0-9]$/,"")},validations:{nfeAccessKey:function(e){return 44===e.length}}})},{"../../helpers/mask-factory":61,"string-mask":36}],47:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("###.###.###-#",{reverse:!0});t.exports=a({clearValue:function(e){return e.replace(/[^\d]/g,"").slice(0,10)},format:function(e){return(i.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{}})},{"../../helpers/mask-factory":61,"string-mask":36}],48:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i={countryCode:new n("+00 (00) 0000-0000"),areaCode:new n("(00) 0000-0000"),simple:new n("0000-0000")},s={countryCode:new n("+00 (00) 00000-0000"),areaCode:new n("(00) 00000-0000"),simple:new n("00000-0000")},u={countryCode:null,areaCode:null,simple:new n("0000-000-0000")},o={countryCode:{sliceSize:13,min:12,max:13},areaCode:{sliceSize:11,min:10,max:11},simple:{sliceSize:9,min:8,max:9},all:{sliceSize:13,min:8,max:13}};function l(e){var r=o.all;if(e&&e.uiBrPhoneNumberMask){var n=e.uiBrPhoneNumberMask;angular.forEach(o,function(e,t){t!==n||(r=e)})}return r}t.exports=a({clearValue:function(e,t){var r=l(t);return e.toString().replace(/[^0-9]/g,"").slice(0,r.sliceSize)},format:function(e){return(0===e.indexOf("0800")?u.simple.apply(e):e.length<9?i.simple.apply(e)||"":e.length<10?s.simple.apply(e):e.length<11?i.areaCode.apply(e):e.length<12?s.areaCode.apply(e):e.length<13?i.countryCode.apply(e):s.countryCode.apply(e)).trim().replace(/[^0-9]$/,"")},getModelValue:function(e,t){var r=this.clearValue(e);return"number"===t?parseInt(r):r},validations:{brPhoneNumber:function(e,t,r){var n=l(r),a=e&&e.toString().length;return a>=n.min&&a<=n.max}}})},{"../../helpers/mask-factory":61,"string-mask":36}],49:[function(e,t,r){"use strict";var n=angular.module("ui.utils.masks.ch",[]).directive("uiChPhoneNumberMask",e("./phone/ch-phone"));t.exports=n.name},{"./phone/ch-phone":50}],50:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("+00 00 000 00 00");t.exports=a({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,11)},format:function(e){return(i.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{chPhoneNumber:function(e){return 11===(e&&e.toString().length)}}})},{"../../helpers/mask-factory":61,"string-mask":36}],51:[function(e,t,r){"use strict";var n=angular.module("ui.utils.masks.fr",[]).directive("uiFrPhoneNumberMask",e("./phone/fr-phone"));t.exports=n.name},{"./phone/fr-phone":52}],52:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("00 00 00 00 00");t.exports=a({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,10)},format:function(e){return(i.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{frPhoneNumber:function(e){return 10===(e&&e.toString().length)}}})},{"../../helpers/mask-factory":61,"string-mask":36}],53:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("0000 0000 0000 0000");t.exports=a({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"").slice(0,16)},format:function(e){return(i.apply(e)||"").trim().replace(/[^0-9]$/,"")},validations:{creditCard:function(e){return 16===(e&&e.toString().length)}}})},{"../../helpers/mask-factory":61,"string-mask":36}],54:[function(e,t,r){"use strict";var u=e("date-fns/format"),o=e("date-fns/parse"),l=e("date-fns/isValid"),c=e("string-mask");var n={"pt-br":"DD/MM/YYYY","es-ar":"DD/MM/YYYY","es-mx":"DD/MM/YYYY",es:"DD/MM/YYYY","en-us":"MM/DD/YYYY",en:"MM/DD/YYYY","fr-fr":"DD/MM/YYYY",fr:"DD/MM/YYYY",ru:"DD.MM.YYYY"};function a(e){var s=n[e.id]||"YYYY-MM-DD";return{restrict:"A",require:"ngModel",link:function(e,t,r,n){r.parse=r.parse||"true",s=r.uiDateMask||s;var a=new c(s.replace(/[YMD]/g,"0"));function i(e){if(n.$isEmpty(e))return null;var t=e;return"object"!=typeof e&&!function(e){return/^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]{3}([-+][0-9]{2}:[0-9]{2}|Z)$/.test(e.toString())}(e)||(t=u(e,s)),t=t.replace(/[^0-9]/g,""),(a.apply(t)||"").trim().replace(/[^0-9]$/,"")}n.$formatters.push(i),n.$parsers.push(function(e){if(n.$isEmpty(e))return e;var t=i(e);return n.$viewValue!==t&&(n.$setViewValue(t),n.$render()),"false"===r.parse?t:o(t,s,new Date)}),n.$validators.date=function(e,t){return!!n.$isEmpty(e)||l(o(t,s,new Date))&&t.length===s.length}}}}a.$inject=["$locale"],t.exports=a},{"date-fns/format":16,"date-fns/isValid":17,"date-fns/parse":33,"string-mask":36}],55:[function(e,t,r){"use strict";var n=angular.module("ui.utils.masks.global",[]).directive("uiCreditCardMask",e("./credit-card/credit-card")).directive("uiDateMask",e("./date/date")).directive("uiMoneyMask",e("./money/money")).directive("uiNumberMask",e("./number/number")).directive("uiPercentageMask",e("./percentage/percentage")).directive("uiScientificNotationMask",e("./scientific-notation/scientific-notation")).directive("uiTimeMask",e("./time/time"));t.exports=n.name},{"./credit-card/credit-card":53,"./date/date":54,"./money/money":56,"./number/number":57,"./percentage/percentage":58,"./scientific-notation/scientific-notation":59,"./time/time":60}],56:[function(e,t,r){"use strict";var g=e("string-mask"),y=e("../../helpers/validators"),M=e("../../helpers/pre-formatters");function n(h,v){return{restrict:"A",require:"ngModel",link:function(e,t,i,s){var n=h.NUMBER_FORMATS.DECIMAL_SEP,a=h.NUMBER_FORMATS.GROUP_SEP,u=h.NUMBER_FORMATS.CURRENCY_SYM,o=" ",l=v(i.uiMoneyMask)(e),c=!1;function r(e){var t=0<e?n+new Array(e+1).join("0"):"",r="#"+a+"##0"+t;return angular.isDefined(i.uiCurrencyAfter)?r+=o:r=o+r,new g(r,{reverse:!0})}t.bind("keydown keypress",function(e){c=8===e.which}),angular.isDefined(i.uiDecimalDelimiter)&&(n=i.uiDecimalDelimiter),angular.isDefined(i.uiThousandsDelimiter)&&(a=i.uiThousandsDelimiter),angular.isDefined(i.uiHideGroupSep)&&(a=""),angular.isDefined(i.uiHideSpace)&&(o=""),angular.isDefined(i.currencySymbol)&&(u=i.currencySymbol,0===i.currencySymbol.length&&(o="")),isNaN(l)&&(l=2);var d,f,m=r(l=parseInt(l));function p(e){if(s.$isEmpty(e))return null;var t,r=e.replace(/[^\d]+/g,"");if(r=(r=r.replace(/^[0]+([1-9])/,"$1"))||"0",c&&angular.isDefined(i.uiCurrencyAfter)&&0!==r&&(r=r.substring(0,r.length-1),c=!1),t=angular.isDefined(i.uiCurrencyAfter)?m.apply(r)+u:u+m.apply(r),angular.isDefined(i.uiNegativeNumber)){var n="-"===e[0];"-"===e.slice(-1)^n&&r&&(r*=-1,t="-"+t)}e!==t&&(s.$setViewValue(t),s.$render());var a=parseInt(t.replace(/[^\d\-]+/g,""));return isNaN(a)?null:(angular.isDefined(i.uiIntegerModel)||(a/=Math.pow(10,l)),a)}s.$formatters.push(function(e){if(s.$isEmpty(e))return"";angular.isDefined(i.uiIntegerModel)&&(e/=Math.pow(10,l));var t=angular.isDefined(i.uiNegativeNumber)&&e<0?"-":"",r=M.prepareNumberToFormatter(e,l);return angular.isDefined(i.uiCurrencyAfter)?t+m.apply(r)+u:t+u+m.apply(r)}),s.$parsers.push(p),i.uiMoneyMask&&e.$watch(i.uiMoneyMask,function(e){l=isNaN(e)?2:e,l=parseInt(l),m=r(l),p(s.$viewValue)}),i.currency&&e.$watch(i.currency,function(e){u=e,m=r(l),p(s.$viewValue)}),i.min&&(s.$validators.min=function(e){return y.minNumber(s,e,d)},e.$watch(i.min,function(e){d=e,s.$validate()}));i.max&&(s.$validators.max=function(e){return y.maxNumber(s,e,f)},e.$watch(i.max,function(e){f=e,s.$validate()}))}}}n.$inject=["$locale","$parse"],t.exports=n},{"../../helpers/pre-formatters":63,"../../helpers/validators":64,"string-mask":36}],57:[function(e,t,r){"use strict";var p=e("../../helpers/validators"),h=e("../../helpers/number-mask-builder"),v=e("../../helpers/pre-formatters");function n(f,m){return{restrict:"A",require:"ngModel",link:function(e,t,i,s){var r=f.NUMBER_FORMATS.DECIMAL_SEP,n=f.NUMBER_FORMATS.GROUP_SEP,a=m(i.uiNumberMask)(e);angular.isDefined(i.uiHideGroupSep)&&(n=""),isNaN(a)&&(a=2);var u,o,l=h.viewMask(a,r,n),c=h.modelMask(a);function d(e){if(s.$isEmpty(e))return null;var t=v.clearDelimitersAndLeadingZeros(e)||"0",r=l.apply(t),n=parseFloat(c.apply(t));if(angular.isDefined(i.uiNegativeNumber)){var a="-"===e[0];("-"===e.slice(-1)^a||"-"===e)&&(r="-"+(0!==(n*=-1)?r:""))}return s.$viewValue!==r&&(s.$setViewValue(r),s.$render()),n}t.on("blur",function(){"-"===s.$viewValue&&(s.$setViewValue(""),s.$render())}),s.$formatters.push(function(e){if(s.$isEmpty(e))return e;var t=angular.isDefined(i.uiNegativeNumber)&&e<0?"-":"",r=v.prepareNumberToFormatter(e,a);return t+l.apply(r)}),s.$parsers.push(d),i.uiNumberMask&&e.$watch(i.uiNumberMask,function(e){a=isNaN(e)?2:e,l=h.viewMask(a,r,n),c=h.modelMask(a),d(s.$viewValue)}),i.min&&(s.$validators.min=function(e){return p.minNumber(s,e,u)},e.$watch(i.min,function(e){u=e,s.$validate()}));i.max&&(s.$validators.max=function(e){return p.maxNumber(s,e,o)},e.$watch(i.max,function(e){o=e,s.$validate()}))}}}n.$inject=["$locale","$parse"],t.exports=n},{"../../helpers/number-mask-builder":62,"../../helpers/pre-formatters":63,"../../helpers/validators":64}],58:[function(e,t,r){"use strict";var y=e("../../helpers/validators"),M=e("../../helpers/number-mask-builder"),b=e("../../helpers/pre-formatters");function n(g){return{restrict:"A",require:"ngModel",link:function(e,t,i,s){var r=g.NUMBER_FORMATS.DECIMAL_SEP,u=!1;t.bind("keydown keypress",function(e){u=8===e.which});var n=g.NUMBER_FORMATS.GROUP_SEP;angular.isDefined(i.uiHideGroupSep)&&(n="");var o=" %";angular.isDefined(i.uiHidePercentageSign)?o="":angular.isDefined(i.uiHideSpace)&&(o="%");var a=parseInt(i.uiPercentageMask);isNaN(a)&&(a=2);var l={multiplier:100,decimalMask:2};angular.isDefined(i.uiPercentageValue)&&(l.multiplier=1,l.decimalMask=0);var c,d,f=a+l.decimalMask,m=M.viewMask(a,r,n),p=M.modelMask(f);function h(e){if(s.$isEmpty(e))return e;var t=angular.isDefined(i.uiNegativeNumber)&&e<0?"-":"",r=function(e,t,r){return b.clearDelimitersAndLeadingZeros((parseFloat(e)*r).toFixed(t))}(e,a,l.multiplier);return t+m.apply(r)+o}function v(e){if(s.$isEmpty(e))return null;var t=b.clearDelimitersAndLeadingZeros(e)||"0";""!==o&&1<e.length&&-1===e.indexOf("%")&&(t=t.slice(0,t.length-1)),u&&1===e.length&&"%"!==e&&(t="0");var r=m.apply(t)+o,n=parseFloat(p.apply(t));if(angular.isDefined(i.uiNegativeNumber)){var a="-"===e[0];("-"===e.slice(-1)^a||"-"===e)&&(r="-"+(0!==(n*=-1)?r:""))}return s.$viewValue!==r&&(s.$setViewValue(r),s.$render()),n}s.$formatters.push(h),s.$parsers.push(v),i.uiPercentageMask&&e.$watch(i.uiPercentageMask,function(e){a=isNaN(e)?2:e,f=a+l.decimalMask,m=M.viewMask(a,r,n),p=M.modelMask(f),v(h(s.$modelValue))}),i.min&&(s.$validators.min=function(e){return y.minNumber(s,e,c)},e.$watch(i.min,function(e){c=e,s.$validate()}));i.max&&(s.$validators.max=function(e){return y.maxNumber(s,e,d)},e.$watch(i.max,function(e){d=e,s.$validate()}))}}}n.$inject=["$locale"],t.exports=n},{"../../helpers/number-mask-builder":62,"../../helpers/pre-formatters":63,"../../helpers/validators":64}],59:[function(e,t,r){"use strict";var n=e("string-mask");function a(e,r){var c=e.NUMBER_FORMATS.DECIMAL_SEP;return{restrict:"A",require:"ngModel",link:function(e,t,s,u){var o=r(s.uiScientificNotationMask)(e);isNaN(o)&&(o=2);var l=function(e){var t="0";if(0<e){t+=c;for(var r=0;r<e;r++)t+="0"}return new n(t,{reverse:!0})}(o);function i(e){if(u.$isEmpty(e))return e;var t,r,n=function(e){var t=e.toString().match(/(-?[0-9]*)[\.]?([0-9]*)?[Ee]?([\+-]?[0-9]*)?/);return{integerPartOfSignificand:t[1],decimalPartOfSignificand:t[2],exponent:0|t[3]}}(e="number"==typeof e?e.toExponential(o):e.toString().replace(c,".")),a=n.integerPartOfSignificand||0,i=a.toString();return angular.isDefined(n.decimalPartOfSignificand)&&(i+=n.decimalPartOfSignificand),(1<=a||a<=-1)&&(angular.isDefined(n.decimalPartOfSignificand)&&n.decimalPartOfSignificand.length>o||0===o&&2<=i.length)&&(r=i.slice(o+1,i.length),i=i.slice(0,o+1)),t=l.apply(i),0!==n.exponent&&(r=n.exponent),angular.isDefined(r)&&(t+="e"+r),(angular.isDefined(s.uiNegativeNumber)&&"-"===e[0]?"-":"")+t}u.$formatters.push(i),u.$parsers.push(function(e){if(u.$isEmpty(e))return e;var t=/e-/.test(e),r=i(e.replace("e-","e")),n="-"===e.slice(-1);n^t&&(r=r.replace(/(e[-]?)/,"e-")),n&&t&&(r="-"!==r[0]?"-"+r:r.replace(/^(-)/,""));var a=parseFloat(r.replace(c,"."));return u.$viewValue!==r&&(u.$setViewValue(r),u.$render()),a}),u.$validators.max=function(e){return u.$isEmpty(e)||e<Number.MAX_VALUE}}}}a.$inject=["$locale","$parse"],t.exports=a},{"string-mask":36}],60:[function(e,t,r){"use strict";var l=e("string-mask");t.exports=function(){return{restrict:"A",require:"ngModel",link:function(e,t,r,i){var n="00:00:00";angular.isDefined(r.uiTimeMask)&&"short"===r.uiTimeMask&&(n="00:00");var s=n.length,a=n.replace(":","").length,u=new l(n);function o(e){if(i.$isEmpty(e))return e;var t=e.replace(/[^0-9]/g,"").slice(0,a)||"";return(u.apply(t)||"").replace(/[^0-9]$/,"")}i.$formatters.push(o),i.$parsers.push(function(e){if(i.$isEmpty(e))return e;var t=o(e),r=t;return i.$viewValue!==t&&(i.$setViewValue(t),i.$render()),r}),i.$validators.time=function(e){if(i.$isEmpty(e))return!0;var t=e.toString().split(/:/).filter(function(e){return!!e}),r=parseInt(t[0]),n=parseInt(t[1]),a=parseInt(t[2]||0);return e.toString().length===s&&r<24&&n<60&&a<60}}}}},{"string-mask":36}],61:[function(e,t,r){"use strict";t.exports=function(s){return function(){return{restrict:"A",require:"ngModel",link:function(e,t,a,i){i.$formatters.push(function(e){if(i.$isEmpty(e))return e;var t=s.clearValue(e.toString(),a);return s.format(t)}),i.$parsers.push(function(e){if(i.$isEmpty(e))return e;var t=s.clearValue(e.toString(),a),r=s.format(t);if(i.$viewValue!==r&&(i.$setViewValue(r),i.$render()),angular.isUndefined(s.getModelValue))return t;var n=typeof i.$modelValue;return s.getModelValue(r,n)}),angular.forEach(s.validations,function(r,e){i.$validators[e]=function(e,t){return i.$isEmpty(e)||r(e,t,a)}})}}}}},{}],62:[function(e,t,r){"use strict";var i=e("string-mask");t.exports={viewMask:function(e,t,r){var n="#"+r+"##0";if(0<e){n+=t;for(var a=0;a<e;a++)n+="0"}return new i(n,{reverse:!0})},modelMask:function(e){var t="###0";if(0<e){t+=".";for(var r=0;r<e;r++)t+="0"}return new i(t,{reverse:!0})}}},{"string-mask":36}],63:[function(e,t,r){"use strict";function n(e){return"0"===e?"0":e.toString().replace(/^-/,"").replace(/^0*/,"").replace(/[^0-9]/g,"")}t.exports={clearDelimitersAndLeadingZeros:n,prepareNumberToFormatter:function(e,t){return n(parseFloat(e).toFixed(t))}}},{}],64:[function(e,t,r){"use strict";t.exports={maxNumber:function(e,t,r){var n=parseFloat(r,10);return e.$isEmpty(t)||isNaN(n)||t<=n},minNumber:function(e,t,r){var n=parseFloat(r,10);return e.$isEmpty(t)||isNaN(n)||n<=t}}},{}],65:[function(e,t,r){"use strict";var n=e("string-mask"),a=e("../../helpers/mask-factory"),i=new n("(*************"),s=new n("+00-00-000-000000");t.exports=a({clearValue:function(e){return e.toString().replace(/[^0-9]/g,"")},format:function(e){return(e.length<11?i.apply(e)||"":s.apply(e)).trim().replace(/[^0-9]$/,"")},validations:{usPhoneNumber:function(e){return e&&9<e.toString().length}}})},{"../../helpers/mask-factory":61,"string-mask":36}],66:[function(e,t,r){"use strict";var n=angular.module("ui.utils.masks.us",[]).directive("uiUsPhoneNumberMask",e("./phone/us-phone"));t.exports=n.name},{"./phone/us-phone":65}]},{},[37]);