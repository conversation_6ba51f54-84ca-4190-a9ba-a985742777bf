/*
 AngularJS v1.7.8
 (c) 2010-2018 Google, Inc. http://angularjs.org
 License: MIT
*/
(function(z,l){'use strict';function q(n){return["$animate",function(u){return{restrict:"AE",transclude:"element",priority:1,terminal:!0,require:"^^ngMessages",link:function(m,f,a,e,r){var b,h,s;if(!n){b=f[0];s=a.ngMessage||a.when;a=a.ngMessageExp||a.whenExp;var k=function(c){h=c?v(c)?c:c.split(/[\s,]+/):null;e.reRender()};a?(k(m.$eval(a)),m.$watchCollection(a,k)):k(s)}var g,t;e.register(b,t={test:function(c){var b=h;c=b?v(b)?0<=b.indexOf(c):b.hasOwnProperty(c):void 0;return c},attach:function(){g||
r(function(c,a){u.enter(c,null,f);g=c;var d=g.$$attachId=e.getAttachId();g.on("$destroy",function(){g&&g.$$attachId===d&&(e.deregister(b,n),t.detach());a.$destroy()})})},detach:function(){if(g){var c=g;g=null;u.leave(c)}}},n);m.$on("$destroy",function(){e.deregister(b,n)})}}}]}var x,v,p,y;l.module("ngMessages",[],function(){x=l.forEach;v=l.isArray;p=l.isString;y=l.element}).info({angularVersion:"1.7.8"}).directive("ngMessages",["$animate",function(n){function u(f,a){return p(a)&&0===a.length||m(f.$eval(a))}
function m(f){return p(f)?f.length:!!f}return{require:"ngMessages",restrict:"AE",controller:["$element","$scope","$attrs",function(f,a,e){function r(c,b){for(var d=b,a=[];d&&d!==c;){var e=d.$$ngMessageNode;if(e&&e.length)return k[e];d.childNodes.length&&-1===a.indexOf(d)?(a.push(d),d=d.childNodes[d.childNodes.length-1]):d.previousSibling?d=d.previousSibling:(d=d.parentNode,a.push(d))}}var b=this,h=0,s=0;this.getAttachId=function(){return s++};var k=this.messages={},g,t;this.render=function(c){c=c||
{};g=!1;t=c;for(var w=u(a,e.ngMessagesMultiple)||u(a,e.multiple),d=[],r={},h=0,k=b.head,s=!1,l=0;null!=k;){l++;var p=k.message,q=!1;s||x(c,function(b,c){m(b)&&!q&&(h++,p.test(c)&&!r[c]&&(q=r[c]=!0,p.attach()))});q?s=!w:d.push(p);k=k.next}x(d,function(c){c.detach()});c=d.length!==l;(w=b.default&&!c&&0<h)?b.default.attach():b.default&&b.default.detach();c||w?n.setClass(f,"ng-active","ng-inactive"):n.setClass(f,"ng-inactive","ng-active")};a.$watchCollection(e.ngMessages||e["for"],b.render);this.reRender=
function(){g||(g=!0,a.$evalAsync(function(){g&&t&&b.render(t)}))};this.register=function(c,a,d){if(d)b.default=a;else{d=h.toString();k[d]={message:a};var e=f[0];a=k[d];b.head?(e=r(e,c))?(a.next=e.next,e.next=a):(a.next=b.head,b.head=a):b.head=a;c.$$ngMessageNode=d;h++}b.reRender()};this.deregister=function(a,e){if(e)delete b.default;else{var d=a.$$ngMessageNode;delete a.$$ngMessageNode;var g=k[d];if(g){var h=r(f[0],a);h?h.next=g.next:b.head=g.next}delete k[d]}b.reRender()}}]}}]).directive("ngMessagesInclude",
["$templateRequest","$document","$compile",function(n,l,m){function f(a,e){var f=m.$$createComment?m.$$createComment("ngMessagesInclude",e):l[0].createComment(" ngMessagesInclude: "+e+" "),f=y(f);a.after(f);a.remove()}return{restrict:"AE",require:"^^ngMessages",link:function(a,e,l){var b=l.ngMessagesInclude||l.src;n(b).then(function(h){a.$$destroyed||(p(h)&&!h.trim()?f(e,b):m(h)(a,function(a){e.after(a);f(e,b)}))})}}}]).directive("ngMessage",q()).directive("ngMessageExp",q()).directive("ngMessageDefault",
q(!0))})(window,window.angular);
//# sourceMappingURL=angular-messages.min.js.map
